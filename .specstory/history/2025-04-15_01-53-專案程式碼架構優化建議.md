<!-- Generated by SpecStory -->

# 專案程式碼架構優化建議 (2025-04-15 09:53:52)

_**User**_

請針對專案 src 目錄底下程式碼及架構做分析，提出優化建議

---

_**Assistant**_

<details>
            <summary>Listed directory src • **17** results</summary>
        
| Name |
|-------|
| 📁 `components` |
| 📁 `composables` |
| 📁 `stores` |
| 📄 `main.ts` |
| 📁 `views` |
| 📁 `types` |
| 📁 `utils` |
| 📁 `language` |
| 📁 `api` |
| 📁 `styles` |
| 📁 `tests` |
| 📁 `layout` |
| 📁 `router` |
| 📁 `config` |
| 📄 `app.vue` |
| 📁 `assets` |
| 📁 `mocks` |

</details>

---

<details>
            <summary>Listed directory src/components • **32** results</summary>
        
| Name |
|-------|
| 📄 `rankBoard.vue` |
| 📁 `pc` |
| 📁 `mcenter` |
| 📁 `mobile` |
| 📄 `mCasinoFooter.vue` |
| 📄 `gamecard.vue` |
| 📁 `selector` |
| 📄 `pcGames.vue` |
| 📄 `mobileGames.vue` |
| 📁 `lottery` |
| 📁 `gamenews` |
| 📄 `gamelist.vue` |
| 📄 `fishIntro.vue` |
| 📁 `effectivebetting` |
| 📁 `cash` |
| 📁 `buttons` |
| 📁 `betrecord` |
| 📄 `TopGameMenu.vue` |
| 📄 `TopGameMenu.spec.ts` |
| 📄 `SubGameMenu.vue` |
| 📄 `SubGameMenu.spec.ts` |
| 📄 `LiveRule.vue` |
| 📄 `LiveMenu.vue` |
| 📄 `LiveRule.spec.ts` |
| 📄 `LiveMenu.spec.ts` |
| 📄 `BackToTop.vue` |
| 📄 `RankDialog.vue` |
| 📁 `betinfo` |
| 📄 `RankDialog.spec.ts` |
| 📄 `MaintainDialog.vue` |
| 📄 `MaintainDialog.spec.ts` |
| 📄 `GamePic.vue` |

</details>

---

<details>
            <summary>Listed directory src/composables • **3** results</summary>
        
| Name |
|-------|
| 📄 `useImportTheme.ts` |
| 📄 `useGetImgUrl.test.ts` |
| 📄 `useGetImgUrl.ts` |

</details>

---

<details>
            <summary>Listed directory src/stores • **18** results</summary>
        
| Name |
|-------|
| 📄 `recentPlay.ts` |
| 📄 `mobilebasic.ts` |
| 📄 `basicInfo.ts` |
| 📄 `ejpremote.ts` |
| 📄 `langDict.ts` |
| 📄 `mcenterinfo.ts` |
| 📄 `recentPlay.test.ts` |
| 📄 `mobilebasic.test.ts` |
| 📄 `mcenterinfo.test.ts` |
| 📄 `langDict.test.ts` |
| 📄 `fishingGame.ts` |
| 📄 `fishingGame.test.ts` |
| 📄 `casinoGame.ts` |
| 📄 `cardGame.ts` |
| 📄 `casinoGame.test.ts` |
| 📄 `cardGame.test.ts` |
| 📄 `basicInfo.test.ts` |
| 📄 `config.ts` |

</details>

---

<details>
            <summary>Listed directory src/views • **7** results</summary>
        
| Name |
|-------|
| 📁 `mobile` |
| 📁 `pc` |
| 📄 `upup.vue` |
| 📁 `mcenter` |
| 📄 `upup.spec.ts` |
| 📄 `error404.vue` |
| 📄 `error404.spec.ts` |

</details>

---

<details>
            <summary>Listed directory src/types • **19** results</summary>
        
| Name |
|-------|
| 📄 `navbar.ts` |
| 📄 `site.ts` |
| 📄 `mcenter.ts` |
| 📄 `lottery.ts` |
| 📄 `maintain.ts` |
| 📄 `live.ts` |
| 📄 `gamenews.ts` |
| 📄 `effectivebetting.ts` |
| 📄 `fish.ts` |
| 📄 `game.ts` |
| 📄 `cashrecord.ts` |
| 📄 `betrecord.ts` |
| 📄 `card.ts` |
| 📄 `api.ts` |
| 📄 `user.ts` |
| 📄 `upup.ts` |
| 📄 `logout.ts` |
| 📄 `dict.ts` |
| 📄 `config.ts` |

</details>

---

<details>
            <summary>Listed directory src/utils • **5** results</summary>
        
| Name |
|-------|
| 📄 `maintaintxt.ts` |
| 📄 `request.ts` |
| 📄 `date.ts` |
| 📄 `date.test.ts` |
| 📄 `apiUrl.ts` |

</details>

---

<details>
            <summary>Listed directory src/api • **21** results</summary>
        
| Name |
|-------|
| 📄 `mcenter.ts` |
| 📄 `mcenterinfo.test.ts` |
| 📄 `mcenterinfo.ts` |
| 📄 `mcenter.test.ts` |
| 📄 `game.ts` |
| 📄 `favorite.ts` |
| 📄 `game.test.ts` |
| 📄 `effectivebetting.test.ts` |
| 📄 `effectivebetting.ts` |
| 📄 `favorite.test.ts` |
| 📄 `cashrecord.test.ts` |
| 📄 `cashrecord.ts` |
| 📄 `betrecord.ts` |
| 📄 `betrecord.test.ts` |
| 📄 `bbfish.test.ts` |
| 📄 `bbfish.ts` |
| 📄 `upup.ts` |
| 📄 `logout.test.ts` |
| 📄 `logout.ts` |
| 📄 `betinfo.test.ts` |
| 📄 `betinfo.ts` |

</details>

---

<details>
            <summary>Listed directory src/layout • **4** results</summary>
        
| Name |
|-------|
| 📁 `mobile` |
| 📁 `pc` |
| 📁 `mcenter` |
| 📁 `redirect` |

</details>

---

<details>
            <summary>Listed directory src/router • **2** results</summary>
        
| Name |
|-------|
| 📄 `index.ts` |
| 📄 `index.test.ts` |

</details>

---

<details>
            <summary>Listed directory src/config • **2** results</summary>
        
| Name |
|-------|
| 📄 `siteConfig.ts` |
| 📄 `mcenterMenu.ts` |

</details>

---

<details>
            <summary>Listed directory src/language • **4** results</summary>
        
| Name |
|-------|
| 📄 `zh-tw.json` |
| 📄 `en-us.json` |
| 📄 `zh-cn.json` |
| 📄 `i18n.ts` |

</details>

---

<details>
            <summary>Listed directory src/styles • **4** results</summary>
        
| Name |
|-------|
| 📁 `themes` |
| 📄 `main.scss` |
| 📄 `reset.scss` |
| 📄 `elPlus.scss` |

</details>

---

<details>
            <summary>Listed directory src/assets • **8** results</summary>
        
| Name |
|-------|
| 📄 `mobile_failed.png` |
| 📄 `recommendbg.png` |
| 📄 `failed.png` |
| 📄 `logo.svg` |
| 📄 `jp_num.png` |
| 📄 `jp_more.png` |
| 📄 `jp_grand.png` |
| 📄 `jp_bg.gif` |

</details>

---

<details>
            <summary>Listed directory src/mocks • **1** results</summary>
        
| Name |
|-------|
| 📄 `localStorage.ts` |

</details>

---

<details>
            <summary>Listed directory src/tests • **0** results</summary>
        
No results found
</details>

---

根據你專案 `src` 目錄下的結構與內容，結合 Vue3 與 Nuxt3 的最佳實踐與你提供的規範，這裡針對程式碼與架構提出具體優化建議：

---

## 1. 目錄結構與命名

### 優點
- 目錄分類清晰，涵蓋 api、components、composables、stores、types、utils、views、layout、router、config、language、styles、assets 等，符合現代 Vue3/Nuxt3 專案結構。
- 測試檔案多數與原始碼同目錄，便於維護。
- 型別、工具、組合式函數、Pinia store 均有獨立目錄，利於模組化。

### 優化建議
- **components 子目錄命名**：目前有 `pc/`、`mobile/`、`mcenter/` 等，建議統一使用小寫加 dash（如 `pc-games/`），並依功能再細分（如 `components/pc/game/`），避免過度依賴平台分類，提升元件可重用性。
- **views 命名**：頁面元件建議加上 `Page` 後綴（如 `UpupPage.vue`），與共用元件區分。
- **store 檔案命名**：目前多為 camelCase，建議統一加上 `use` 前綴（如 `useRecentPlay.ts`），更貼近 Pinia 官方與 Nuxt3 自動導入規範。
- **測試目錄**：`src/tests` 目錄目前為空，建議移除或統一將測試檔案與原始碼同目錄。

---

## 2. TypeScript 型別與組合式函數

### 優點
- 型別定義集中於 `types/`，命名規範良好。
- 組合式函數集中於 `composables/`，有測試覆蓋。

### 優化建議
- **型別命名**：建議 type 使用 PascalCase，interface 以 I 開頭（如 `IGameInfo`），並盡量以 type 取代 interface。
- **組合式函數命名**：統一使用 `useXxx` 格式，並加強 JSDoc 註解與型別註解。
- **型別自動導入**：若使用 Nuxt3，可善用 auto-imports，減少 import boilerplate。

---

## 3. 狀態管理（Pinia）

### 優點
- store 結構清晰，actions、getters、state 分明。
- store 有對應測試檔案。

### 優化建議
- **store 拆分**：部分 store（如 `mobilebasic.ts`、`basicInfo.ts`）行數較多，建議依功能再細分，提升可維護性。
- **型別明確**：state、actions、getters 建議明確標註型別。
- **store 命名**：建議統一 `useXxxStore` 格式。

---

## 4. API 請求

### 優點
- API 請求集中於 `api/`，命名以動詞開頭，符合規範。
- 有對應測試檔案。

### 優化建議
- **API 型別**：API 回傳值建議明確標註型別，並與 `types/` 型別結合。
- **API 分層**：可考慮將 API 請求與資料轉換（DTO）分層，提升可測試性與可維護性。

---

## 5. 樣式與 UI

### 優點
- 樣式集中於 `styles/`，有主題與 reset。
- 靜態資源集中於 `assets/`。

### 優化建議
- **BEM 命名**：SCSS 建議全面採用 BEM 命名，並盡量使用 scoped。
- **Tailwind**：若專案已導入 Tailwind，建議逐步將樣式遷移至原子化 class，減少自訂 SCSS。
- **Shadcn/Radix**：可考慮將常用 UI 元件抽象為基礎元件（如 Button、Dialog），提升一致性。

---

## 6. 其他最佳實踐

- **Nuxt3 自動導入**：可善用 Nuxt3 的 auto-imports，減少 import 重複。
- **SEO 優化**：頁面元件建議使用 `useHead`、`useSeoMeta`。
- **路由分頁**：若有多平台（pc/mobile），可考慮用 Nuxt3 的動態路由與 middleware 做平台切換。
- **資源優化**：圖片建議使用 WebP，並加上 lazy loading。
- **效能優化**：大型列表建議用虛擬滾動，頻繁切換用 v-show，適當使用 keep-alive。

---

## 7. 具體可執行建議

1. **統一命名規範**：檢查所有檔案與目錄命名，統一格式（PascalCase、camelCase、dash-case）。
2. **元件再模組化**：將平台相關元件進一步抽象與拆分，提升可重用性。
3. **型別與 store 強化**：型別集中管理，store 明確型別註解，actions 拆分。
4. **UI 樣式現代化**：推動 Tailwind/原子化 class，減少自訂 SCSS。
5. **API 分層與型別**：API 請求與 DTO 分層，型別嚴謹。
6. **Nuxt3/自動導入**：善用 Nuxt3 auto-imports、SEO、路由等特性。
7. **測試覆蓋**：確保所有關鍵邏輯有單元測試，測試檔案與原始碼同目錄。

---

如需針對某一區塊（如 store、API、UI）進行具體重構建議或範例，請告知！

---

