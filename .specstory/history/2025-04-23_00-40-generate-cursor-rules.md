<!-- Generated by SpecStory -->

# Generate cursor rules (2025-04-23 08:40:40)

_**User**_

/Generate Cursor Rules 

---

_**Assistant**_

<details>
            <summary>Listed directory .cursor/rules • **2** results</summary>
        
| Name |
|-------|
| 📄 `project.mdc` |
| 📄 `vue3.mdc` |

</details>

---

Read file: .cursor/rules/project.mdc

---

Read file: .cursor/rules/vue3.mdc

---

You already have two Cursor rules in `.cursor/rules/`:
- `project.mdc`: Vue3 project code conventions (directory structure, naming, TypeScript, API, Pinia, testing, commit, performance, etc.)
- `vue3.mdc`: Vue 3, Nuxt, TypeScript, Tailwind, Shadcn, Radix, VueUse, Pinia, and best practices (composition API, naming, formatting, performance, Nuxt specifics, etc.)

Both are thorough and cover your workspace rules and tech stack.  
No new rules are needed unless you want to add something not already covered.

If you want a new rule, tell me what specific aspect or file/process you want to document, and I’ll generate the `.mdc` for you.  
Otherwise, you’re set—your Cursor rules are already in place and up to spec.

---

_**User**_

將 src/api/ 內呼叫 api 方式更新到 docs/development_guide.md，讓 ai 之後可以正確寫出適合的程式

---

_**Assistant**_

<details>
            <summary>Listed directory src/api • **30** results</summary>
        
| Name |
|-------|
| 📄 `game.ts` |
| 📄 `game.test.ts` |
| 📄 `mcenter.ts` |
| 📄 `server.ts` |
| 📄 `upup.ts` |
| 📄 `logout.test.ts` |
| 📄 `logout.ts` |
| 📄 `mcenter.test.ts` |
| 📄 `lang.ts` |
| 📄 `lobbyswitch.test.ts` |
| 📄 `lobbyswitch.ts` |
| 📄 `hall.ts` |
| 📄 `favorite.ts` |
| 📄 `cashrecord.ts` |
| 📄 `effectivebetting.ts` |
| 📄 `favorite.test.ts` |
| 📄 `betrecord.ts` |
| 📄 `betrecord.test.ts` |
| 📄 `betinfo.ts` |
| 📄 `betinfo.test.ts` |
| 📄 `account.ts` |
| 📄 `bbfish.ts` |
| 📄 `server.test.ts` |
| 📄 `upup.test.ts` |
| 📄 `effectivebetting.test.ts` |
| 📄 `cashrecord.test.ts` |
| 📄 `bbfish.test.ts` |
| 📄 `account.test.ts` |
| 📄 `lang.test.ts` |
| 📄 `hall.test.ts` |

</details>

---

<details>
            <summary>Searched codebase "api call OR request OR axios OR fetch OR useFetch OR useAsyncData" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/api/lang.ts` | L12-23 |
| `src/api/lobbyswitch.ts` | L58-68 |
| `src/api/server.ts` | L21-31 |
| `src/api/hall.ts` | L1-19 |
| `src/api/logout.ts` | L1-11 |
| `src/api/account.ts` | L1-15 |
| `src/api/favorite.ts` | L1-12 |
| `src/api/hall.test.ts` | L11-44 |
| `src/api/effectivebetting.ts` | L30-39 |
| `src/api/upup.test.ts` | L1-8 |
| `src/api/upup.test.ts` | L9-15 |
| `src/api/server.test.ts` | L1-11 |
| `src/api/game.ts` | L103-120 |
| `src/api/bbfish.test.ts` | L1-11 |
| `src/api/server.ts` | L1-21 |
| `src/api/game.ts` | L120-136 |
| `src/api/bbfish.ts` | L25-42 |
| `src/api/game.ts` | L237-250 |
| `src/api/game.test.ts` | L26-34 |
| `src/api/upup.ts` | L1-56 |
| `src/api/bbfish.test.ts` | L55-90 |
| `src/api/game.ts` | L338-351 |
| `src/api/server.test.ts` | L11-52 |
| `src/api/cashrecord.ts` | L40-50 |
| `src/api/game.test.ts` | L601-624 |

</details>

---

<details>
            <summary>Grep search for "apiRequest" • **15** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/logout.ts` | Lundefined | `import apiRequest from '@/utils/apiRequest';` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/logout.ts` | L5 | `const response = await apiRequest.delete('/api/account/logout', { params })` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/lang.ts` | Lundefined | `import apiRequest from '@/utils/apiRequest'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/lang.ts` | L15 | `const res = await apiRequest.get('/api/lang/list', { params })` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/server.ts` | Lundefined | `import apiRequest from '@/utils/apiRequest'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/server.ts` | L24 | `const res = await apiRequest.get('/api/server/info', { params })` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/LotteryCompleteTable.spec.ts` | L46 | `vi.mock('@/utils/apiRequest', () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/upup.ts` | Lundefined | `import apiRequest from '@/utils/apiRequest';` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/upup.ts` | L8 | `const res = await apiRequest.get(`${apiUrl.value}/api/entrance/upup`, { params })` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/server.test.ts` | Lundefined | `import apiRequest from '@/utils/apiRequest'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/server.test.ts` | L4 | `vi.mock('@/utils/apiRequest', () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/server.test.ts` | L29 | `vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/server.test.ts` | L47 | `vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | Lundefined | `import apiRequest from '@/utils/apiRequest'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L37 | `vi.mock('@/utils/apiRequest', () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L102 | `vi.mocked(apiRequest.get).mockResolvedValue(mockLiveRecordResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L168 | `vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainLiveRecordResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L187 | `vi.mocked(apiRequest.get).mockRejectedValue(mockError)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L247 | `vi.mocked(apiRequest.get).mockResolvedValue(mockLiveRecordResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L314 | `vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainLiveRecordResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L334 | `vi.mocked(apiRequest.get).mockRejectedValue(mockError)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L396 | `vi.mocked(apiRequest.get).mockResolvedValue(mockCasinoGameResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L446 | `vi.mocked(apiRequest.get).mockRejectedValue(mockError)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L489 | `vi.mocked(apiRequest.get).mockResolvedValue(mockCasinoRecordResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L544 | `vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainCasinoRecordResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L564 | `vi.mocked(apiRequest.get).mockRejectedValue(mockError)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L620 | `vi.mocked(apiRequest.get).mockResolvedValue(mockCasinoRecordResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L684 | `vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainCasinoRecordResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L705 | `vi.mocked(apiRequest.get).mockRejectedValue(mockError)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L760 | `vi.mocked(apiRequest.get).mockResolvedValue(mockFishingRecordResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L821 | `vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainFishingRecordResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L841 | `vi.mocked(apiRequest.get).mockRejectedValue(mockError)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L898 | `vi.mocked(apiRequest.get).mockResolvedValue(mockCardRecordResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L960 | `vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainCardRecordResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L980 | `vi.mocked(apiRequest.get).mockRejectedValue(mockError)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1020 | `vi.mocked(apiRequest.get).mockResolvedValue(mockLotteryCompleteResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1062 | `vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainLotteryCompleteResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1077 | `vi.mocked(apiRequest.get).mockRejectedValue(mockError)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1129 | `vi.mocked(apiRequest.get).mockResolvedValue(mockLotteryCompleteDetailResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1191 | `vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainLotteryCompleteDetailResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1210 | `vi.mocked(apiRequest.get).mockRejectedValue(mockError)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1264 | `vi.mocked(apiRequest.get).mockResolvedValue(mockLotteryUnCompleteDetailResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1323 | `vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainLotteryUnCompleteDetailResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1341 | `vi.mocked(apiRequest.get).mockRejectedValue(mockError)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1378 | `vi.mocked(apiRequest.get).mockResolvedValue(mockLotteryUnCompleteResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1422 | `vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainLotteryUnCompleteResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1438 | `vi.mocked(apiRequest.get).mockRejectedValue(mockError)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1468 | `vi.mocked(apiRequest.get).mockResolvedValue(mockLotteryCompleteResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1508 | `vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainLotteryCompleteResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1525 | `vi.mocked(apiRequest.get).mockRejectedValue(mockError)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1581 | `vi.mocked(apiRequest.get).mockResolvedValue(mockSportCompleteResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1647 | `vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainSportCompleteResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1663 | `vi.mocked(apiRequest.get).mockRejectedValue(mockError)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1721 | `vi.mocked(apiRequest.get).mockResolvedValue(mockSportCompleteDetailResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1782 | `vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainSportCompleteDetailResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1800 | `vi.mocked(apiRequest.get).mockRejectedValue(mockError)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1840 | `vi.mocked(apiRequest.get).mockResolvedValue(mockSportCompleteContentResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1884 | `vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainSportCompleteContentResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1926 | `vi.mocked(apiRequest.get).mockResolvedValue(mockSportCompleteContentResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L1979 | `vi.mocked(apiRequest.get).mockResolvedValue(mockSportCompleteContentResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L2032 | `vi.mocked(apiRequest.get).mockResolvedValue(mockSportCompleteContentResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L2060 | `vi.mocked(apiRequest.get).mockRejectedValue(mockError)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L2094 | `vi.mocked(apiRequest.get).mockResolvedValue(mockSportUncompleteResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L2136 | `vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainSportUncompleteResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L2152 | `vi.mocked(apiRequest.get).mockRejectedValue(mockError)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L2193 | `vi.mocked(apiRequest.get).mockResolvedValue(mockSportUnCompleteDetailResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L2246 | `vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainSportUnCompleteDetailResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/betrecord.test.ts` | L2263 | `vi.mocked(apiRequest.get).mockRejectedValue(mockError)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/lobbyswitch.ts` | Lundefined | `import apiRequest from '@/utils/apiRequest'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/lobbyswitch.ts` | L61 | `const res = await apiRequest.get('/api/account/lobby_switch', { params })` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/account.test.ts` | Lundefined | `import apiRequest from '@/utils/apiRequest'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/account.test.ts` | L4 | `vi.mock('@/utils/apiRequest', () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/account.test.ts` | L28 | `vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/account.test.ts` | L46 | `vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/bbfish.ts` | Lundefined | `import apiRequest from '@/utils/apiRequest'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/bbfish.ts` | L9 | `const response = await apiRequest.get('/api/fish/game_lobby_menu_link', { params })` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/bbfish.ts` | L28 | `const response = await apiRequest.get(`/api/fish/introduction_images_info`)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/effectivebetting.ts` | Lundefined | `import apiRequest from '@/utils/apiRequest'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/effectivebetting.ts` | L33 | `const res = await apiRequest.get('/api/account/effective_betting', {params})` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/cashrecord.test.ts` | Lundefined | `import apiRequest from '@/utils/apiRequest';` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/cashrecord.test.ts` | L10 | `vi.mock('@/utils/apiRequest', () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/cashrecord.test.ts` | L77 | `vi.mocked(apiRequest.get).mockResolvedValue(mockResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/cashrecord.test.ts` | L125 | `vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/effectivebetting.test.ts` | L1 | `import apiRequest from '@/utils/apiRequest'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/effectivebetting.test.ts` | L10 | `vi.mock('@/utils/apiRequest', () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/effectivebetting.test.ts` | L54 | `vi.mocked(apiRequest.get).mockResolvedValue(mockResponse)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/effectivebetting.test.ts` | L77 | `vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/game.ts` | Lundefined | `import apiRequest from '@/utils/apiRequest'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/account.ts` | Lundefined | `import apiRequest from '@/utils/apiRequest'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/account.ts` | L5 | `const res = await apiRequest.get('/api/account/info', { params })` |

</details>

---

<details>
            <summary>Searched codebase "apiRequest" • **1** results</summary>
        
| File |
|------|
| `undefined` |

</details>

---

Read file: src/utils/apiRequest.ts

---

Read file: docs/development_guide.md

---

```diff
  }
  ```
+ 
+ ### src/api/ 標準 API 呼叫方式
+ 
+ - 統一使用 `apiRequest`（`@/utils/apiRequest`）作為 axios 實例
+ - 需匯入型別與轉換函式（如有）
+ - 失敗時直接 `Promise.reject(error)`
+ - 回傳資料前可用 transform 處理
+ - 例：
+ 
+ ```typescript
+ import apiRequest from '@/utils/apiRequest'
+ import type { IGameInfo } from '@/types/game'
+ 
+ const gameListTransform = (data: any): IGameInfo[] => {
+   // ...transform logic
+   return data as IGameInfo[]
+ }
+ 
+ export const getGameList = async(params: { lang: string }): Promise<IGameInfo[]> => {
+   try {
+     const res = await apiRequest.get('/api/game/list', { params })
+     return gameListTransform(res.data.data)
+   } catch (error: any) {
+     return Promise.reject(error)
+   }
+ }
+ ```
+ 
+ - 支援多種 HTTP 方法：`get`、`post`、`delete` ...
+ - 若需 baseURL，會自動由 `configStore.apiUrl` 注入
+ - 錯誤訊息與 session 處理已於 apiRequest 實作
+ - 只需專注於資料轉換與型別註解
  
  ## 狀態管理規範
```

---

已將 src/api/ 內標準 API 呼叫方式（apiRequest 實例、型別註解、錯誤處理、transform、範例）補充到 `docs/development_guide.md` 的 API 請求規範區塊下。  
這樣 AI 之後就能正確寫出專案慣用的 API 呼叫 code。

重點摘要：
- 統一用 `apiRequest`（@/utils/apiRequest）
- try/catch，失敗時 `Promise.reject(error)`
- 回傳前可 transform
- 型別註解明確
- 範例已補
- baseURL、錯誤、session 都在 apiRequest 處理

有要補充特殊情境或進階用法再告訴我。

---

_**User**_

參考所有測試碼 *.test.ts, *.spec.ts，將寫測試碼方式更新到 docs/development_guide.md，讓 ai 之後可以正確寫出適合的測試碼

---

_**Assistant**_

<details>
            <summary>Grep search for "describe\(|it\(|test\(|vi\.mock|expect\(" • **7** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/TopGameMenu.spec.ts` | L20 | `describe('TopGameMenu', () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/TopGameMenu.spec.ts` | L21 | `it('renders correctly with gameMenu prop', () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/TopGameMenu.spec.ts` | L33 | `expect(menuItems).toHaveLength(mockGameMenu.length)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/TopGameMenu.spec.ts` | L34 | `expect(menuItems[0].text()).toBe('Menu 1')` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/TopGameMenu.spec.ts` | L35 | `expect(menuItems[1].text()).toBe('Menu 2')` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/TopGameMenu.spec.ts` | L36 | `expect(menuItems[2].text()).toBe('Menu 3')` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/TopGameMenu.spec.ts` | L39 | `it('sets active class on clicked menu item', async () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/TopGameMenu.spec.ts` | L53 | `expect(firstMenuItem.classes()).toContain('active')` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/TopGameMenu.spec.ts` | L54 | `expect(wrapper.vm.topMenuId).toBe(1)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/TopGameMenu.spec.ts` | L59 | `expect(secondMenuItem.classes()).toContain('active')` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/TopGameMenu.spec.ts` | L60 | `expect(firstMenuItem.classes()).not.toContain('active')` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/TopGameMenu.spec.ts` | L61 | `expect(wrapper.vm.topMenuId).toBe(2)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/SearchBox.spec.ts` | L15 | `describe('SearchBox', () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/SearchBox.spec.ts` | L16 | `it('renders correctly with show false', () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/SearchBox.spec.ts` | L27 | `expect(wrapper.exists()).toBe(true)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/SearchBox.spec.ts` | L30 | `expect(search.exists()).toBe(true)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/SearchBox.spec.ts` | L31 | `expect(search.classes()).not.toContain('active')` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/SearchBox.spec.ts` | L32 | `expect(search.props('fill')).toBe('var(--icon-bg)')` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/SearchBox.spec.ts` | L34 | `expect(wrapper.find('.search-input').exists()).toBe(false)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/SearchBox.spec.ts` | L37 | `it('renders correctly with show true', () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/SearchBox.spec.ts` | L49 | `expect(search.exists()).toBe(true)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/SearchBox.spec.ts` | L50 | `expect(search.classes()).toContain('active')` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/SearchBox.spec.ts` | L51 | `expect(search.props('fill')).toBe('var(--icon-bg-active)')` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/SearchBox.spec.ts` | L53 | `expect(wrapper.exists()).toBe(true)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/SearchBox.spec.ts` | L54 | `expect(wrapper.find('.search-input').exists()).toBe(true)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/SearchBox.spec.ts` | L57 | `it('toggles show state on Search icon click', async () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/SearchBox.spec.ts` | L72 | `expect(wrapper.emitted('update:show')).toEqual([[true]]);` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/SearchBox.spec.ts` | L73 | `expect(wrapper.find('.search-input').exists()).toBe(true)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/SearchBox.spec.ts` | L77 | `expect(wrapper.emitted('update:show')).toEqual([[true], [false]]);` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/SearchBox.spec.ts` | L78 | `expect(wrapper.find('.search-input').exists()).toBe(false)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/SearchBox.spec.ts` | L81 | `it('displays the correct placeholder', () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/SearchBox.spec.ts` | L93 | `expect(input.attributes('placeholder')).toBe('Enter game name')` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/SearchBox.spec.ts` | L96 | `it('emits the correct data when input changes', async () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/SearchBox.spec.ts` | L109 | `expect(wrapper.emitted('update:data')).toEqual([['test']]);` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/EnterCardLobby.spec.ts` | L17 | `vi.mock('element-plus', () => ({` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/EnterCardLobby.spec.ts` | L23 | `describe('EnterCardLobby', () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/EnterCardLobby.spec.ts` | L43 | `it('should display the button with correct text', () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/EnterCardLobby.spec.ts` | L44 | `expect(wrapper.find('.cardlobby-btn').text()).toBe('Enter Game Hall')` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/EnterCardLobby.spec.ts` | L47 | `it('should alert when user is not logged in', async () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/EnterCardLobby.spec.ts` | L53 | `expect(ElMessageBox.alert).toHaveBeenCalledWith('Please Login!', '', { center: true, showClose: false })` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/EnterCardLobby.spec.ts` | L57 | `it('should open window to cardlobby when user is logged in', async () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/EnterCardLobby.spec.ts` | L62 | `expect(window.open).toHaveBeenCalledWith('/cardlobby', '', 'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no')` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/logout.test.ts` | L4 | `vi.mock('@/utils/apiRequest', () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/logout.test.ts` | L12 | `describe('logout.ts', () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/logout.test.ts` | L13 | `it('success', async () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/logout.test.ts` | L15 | `vi.mocked(apiRequest.delete).mockResolvedValueOnce({` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/logout.test.ts` | L21 | `expect(apiRequest.delete).toHaveBeenCalledWith('/api/account/logout', { params })` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/logout.test.ts` | L22 | `expect(response).toEqual({ code: 0, message: 'Logout successful' })` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/logout.test.ts` | L25 | `it('error', async () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/logout.test.ts` | L27 | `vi.mocked(apiRequest.delete).mockRejectedValueOnce(new Error('Request failed'))` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/logout.test.ts` | L29 | `await expect(logout(params)).rejects.toThrow('Request failed')` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/UserInfo.spec.ts` | L7 | `vi.mock('@/stores/account', () => ({` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/UserInfo.spec.ts` | L11 | `describe('BasicInfo component', () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/UserInfo.spec.ts` | L20 | `it('should display the user avatar and name correctly when the user is logged in', async () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/UserInfo.spec.ts` | L21 | `vi.mocked(useAccountInfoStore).mockReturnValue({` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/UserInfo.spec.ts` | L40 | `expect(wrapper.find('.account-name').text()).toBe('testUser')` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/UserInfo.spec.ts` | L41 | `expect(wrapper.find('.bbin-balance').text()).toBe('100')` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/UserInfo.spec.ts` | L44 | `it('should display the ballguest name correctly when the user is not logged in', async () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/UserInfo.spec.ts` | L45 | `vi.mocked(useAccountInfoStore).mockReturnValue({` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/UserInfo.spec.ts` | L57 | `expect(wrapper.find('.account-name').text()).toBe('ballguest')` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/UserInfo.spec.ts` | L58 | `expect(wrapper.find('.bbin-balance').exists()).toBe(false)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mcenter/effectivebetting.spec.ts` | L5 | `vi.mock('@/api/effectivebetting', () => ({` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mcenter/effectivebetting.spec.ts` | L9 | `vi.mock('vue-i18n', () => ({` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mcenter/effectivebetting.spec.ts` | L15 | `vi.mock('vue3-cookies', () => ({` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mcenter/effectivebetting.spec.ts` | L26 | `vi.mock('@/components/effectivebetting/EffectiveBettingSearch.vue', () => ({` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mcenter/effectivebetting.spec.ts` | L33 | `vi.mock('@/components/effectivebetting/EffectiveBettingTable.vue', () => ({` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mcenter/effectivebetting.spec.ts` | L40 | `describe('EffectiveBetting', () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mcenter/effectivebetting.spec.ts` | L45 | `it('renders correctly', () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mcenter/effectivebetting.spec.ts` | L47 | `expect(wrapper.html()).toContain('Mock EffectiveBettingSearch')` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mcenter/effectivebetting.spec.ts` | L48 | `expect(wrapper.html()).toContain('Mock EffectiveBettingTable')` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mcenter/effectivebetting.spec.ts` | L51 | `it('fetches effective betting data', async () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mcenter/effectivebetting.spec.ts` | L52 | `vi.mocked(getEffectiveBetting).mockResolvedValue({` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mcenter/effectivebetting.spec.ts` | L71 | `await searchInstance.$emit('search')` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mcenter/effectivebetting.spec.ts` | L75 | `expect(getEffectiveBetting).toHaveBeenCalled()` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mcenter/effectivebetting.spec.ts` | L76 | `expect(wrapperInstance.topTableData).toEqual([{` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mcenter/effectivebetting.spec.ts` | L82 | `expect(wrapperInstance.ebTableData).toEqual([{` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mcenter/effectivebetting.spec.ts` | L89 | `it('handles error', async () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mcenter/effectivebetting.spec.ts` | L91 | `vi.mocked(getEffectiveBetting).mockRejectedValue(mockError)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mcenter/effectivebetting.spec.ts` | L100 | `await searchInstance.$emit('search')` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mcenter/effectivebetting.spec.ts` | L102 | `expect(getEffectiveBetting).toHaveBeenCalled()` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mcenter/effectivebetting.spec.ts` | L103 | `expect(console.log).toHaveBeenCalledWith(mockError)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mcenter/effectivebetting.spec.ts` | L104 | `expect(ElMessage.error).toHaveBeenCalledWith(mockError.message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/mcenter.test.ts` | L11 | `vi.mock('@/utils/request', () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/mcenter.test.ts` | L19 | `vi.mock('@/utils/apiRequest', () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/mcenter.test.ts` | L27 | `vi.mock('@/utils/maintaintxt', () => ({` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/mcenter.test.ts` | L33 | `describe('getLiveNews', () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/mcenter.test.ts` | L34 | `it('getLiveNews should return live news data', async () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/mcenter.test.ts` | L58 | `vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/mcenter.test.ts` | L61 | `expect(result.data?.[0].dateTime).toBe('2024-11-11 23:17:21')` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/mcenter.test.ts` | L64 | `it('getLiveNews should return maintain info', async () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/mcenter.test.ts` | L78 | `vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/mcenter.test.ts` | L81 | `expect(result.maintain).toBe(true)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/mcenter.test.ts` | L82 | `expect(result.maintainInfo).toBe('maintain text')` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/mcenter.test.ts` | L85 | `it('should reject when request fails', async () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/mcenter.test.ts` | L87 | `vi.mocked(apiRequest.get).mockRejectedValue(mockError)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/mcenter.test.ts` | L90 | `await expect(getLiveNews({ session_id: '123', hall_id: 456, lang: 'zh-cn'  })).rejects.toThrow('Network Error')` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/mcenter.test.ts` | L91 | `expect(console.log).toHaveBeenCalledWith(mockError)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/mcenter.test.ts` | L95 | `describe('getLiveGameCode', () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/mcenter.test.ts` | L96 | `it('getLiveGameCode should return game code data', async () => {` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/api/mcenter.test.ts` | L108 | `vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })` |

</details>

---

<details>
            <summary>Searched codebase "mount OR shallowMount OR render OR wrapper" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/components/buttons/FreeTrial.spec.ts` | L1-71 |
| `src/components/selector/ObjectSelector.spec.ts` | L1-11 |
| `src/components/cash/CashTable.spec.ts` | L1-9 |
| `src/views/mobile/live.spec.ts` | L39-66 |
| `src/views/upup.spec.ts` | L1-10 |
| `src/components/selector/KeyValueSelector.spec.ts` | L1-11 |
| `src/components/selector/ObjectSelector.spec.ts` | L11-50 |
| `src/components/selector/KeyValueSelector.spec.ts` | L11-45 |
| `src/components/selector/ArraySelector.spec.ts` | L1-11 |
| `src/components/pc/MenuBoard.spec.ts` | L74-103 |
| `src/components/mcenter/PaginationComponent.spec.ts` | L1-31 |
| `src/components/buttons/GameEnter.spec.ts` | L1-51 |
| `src/layout/mobile/mfooter.spec.ts` | L10-21 |
| `src/components/selector/ArraySelector.spec.ts` | L11-41 |
| `src/views/pc/card.spec.ts` | L1-12 |
| `src/components/mobile/SeaarchBox.spec.ts` | L1-51 |
| `src/components/LiveMenu.spec.ts` | L21-87 |
| `src/components/buttons/ListMode.spec.ts` | L1-52 |
| `src/components/SubGameMenu.spec.ts` | L1-52 |
| `src/components/pc/SearchBox.spec.ts` | L56-112 |
| `src/components/TopGameMenu.spec.ts` | L1-64 |
| `src/components/betrecord/BetHistoryDialog.spec.ts` | L46-76 |
| `src/views/mobile/card.spec.ts` | L1-19 |
| `src/components/cash/CashSearch.spec.ts` | L1-11 |
| `src/components/LiveRule.spec.ts` | L98-127 |

</details>

---

Read file: docs/development_guide.md

---



---

```diff
  - 使用 it/test 描述測試案例
  - 測試覆蓋重要業務邏輯
- 
+ - 使用 Vitest + @vue/test-utils
+ - 組件測試用 mount，API/mock 用 vi.mock
+ - 斷言用 expect
+ - 常見 mock：api、store、i18n、router、window、Element Plus
+ - 支援 async/await、emit、spy、錯誤處理
+ - 建議型別註解 wrapper
+ - 可用 nextTick/flushPromises 處理異步
+ - 事件/emit 驗證
+ - snapshot 視需求
+ 
+ #### 組件測試範例
  ```typescript
- describe('GameCard', () => {
-   it('should render game title', () => {
-     const wrapper = mount(GameCard, {
-       props: {
-         game: { title: 'Test Game' }
-       }
-     })
-     expect(wrapper.find('.game-card__title').text()).toBe('Test Game')
-   })
- })
+ import { mount } from '@vue/test-utils'
+ import { describe, it, expect, vi } from 'vitest'
+ import MyComponent from '@/components/MyComponent.vue'
+ import { createI18n } from 'vue-i18n'
+ 
+ const i18n = createI18n({ locale: 'en', messages: { en: { hello: 'Hello' } } })
+ 
+ describe('MyComponent', () => {
+   it('renders with props', () => {
+     const wrapper = mount(MyComponent, {
+       props: { msg: 'test' },
+       global: { plugins: [i18n] }
+     })
+     expect(wrapper.text()).toContain('test')
+   })
+ 
+   it('emits event', async () => {
+     const wrapper = mount(MyComponent)
+     await wrapper.find('button').trigger('click')
+     expect(wrapper.emitted('myEvent')).toBeTruthy()
+   })
+ })
+ ```
+ 
+ #### API/mock 測試範例
+ ```typescript
+ import apiRequest from '@/utils/apiRequest'
+ import { myApi } from '@/api/myApi'
+ import { describe, it, expect, vi } from 'vitest'
+ 
+ vi.mock('@/utils/apiRequest', () => ({
+   default: { get: vi.fn() }
+ }))
+ 
+ describe('myApi', () => {
+   it('success', async () => {
+     vi.mocked(apiRequest.get).mockResolvedValue({ data: { code: 0, data: [1,2,3] } })
+     const result = await myApi()
+     expect(result).toEqual([1,2,3])
+   })
+   it('error', async () => {
+     vi.mocked(apiRequest.get).mockRejectedValue(new Error('fail'))
+     await expect(myApi()).rejects.toThrow('fail')
+   })
+ })
+ ```
+ 
+ #### Pinia/store mock 範例
+ ```typescript
+ import { setActivePinia, createPinia } from 'pinia'
+ beforeEach(() => { setActivePinia(createPinia()) })
+ ```
+ 
+ #### i18n mock 範例
+ ```typescript
+ vi.mock('vue-i18n', () => ({
+   useI18n: () => ({ t: (key: string) => key })
+ }))
+ ```
+ 
+ #### 全域 plugin/router mock
+ ```typescript
+ import { createRouter, createWebHistory } from 'vue-router'
+ const router = createRouter({ history: createWebHistory(), routes: [] })
+ const wrapper = mount(MyComponent, { global: { plugins: [router] } })
+ ```
+ 
+ #### 斷言/spy/emit/異步
+ ```typescript
+ const spy = vi.spyOn(window, 'open').mockImplementation(vi.fn())
+ expect(wrapper.emitted('update:modelValue')).toEqual([[true]])
+ await nextTick()
+ spy.mockRestore()
  ```
  
```

---

測試規範已擴充，現在包含：
- Vitest + @vue/test-utils
- 組件測試用 mount
- API/mock 用 vi.mock
- 斷言用 expect
- 常見 mock（api、store、i18n、router、window、Element Plus）
- 支援 async/await、emit、spy、錯誤處理
- nextTick/flushPromises 處理異步
- 事件/emit 驗證
- snapshot 視需求
- Pinia/store、i18n、router mock 範例
- API 測試與錯誤處理範例

這樣 AI 之後就能寫出專案慣用的測試碼。  
有特殊測試情境要補充再說。

---

