<!-- Generated by SpecStory -->

# Request test code for casino.vue (2025-06-05 15:23:14)

_**User**_

@index.md 提供 src/views/pc/casino.vue 的測試碼

---

_**Assistant**_

<details>
            <summary>Searched codebase "casino.vue" • **7** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

Read file: src/views/pc/casino.vue

---

Read file: src/views/pc/casino.vue

---

<details>
            <summary>Listed directory src/tests • **0** results</summary>
        
No results found
</details>

---

<details>
            <summary>Listed directory src/views/pc • **7** results</summary>
        
| Name |
|-------|
| 📄 `casino.vue` |
| 📄 `fishing.vue` |
| 📄 `lottery.vue` |
| 📄 `live.vue` |
| 📄 `card.spec.ts` |
| 📄 `card.vue` |
| 📄 `live.spec.ts` |

</details>

---

<details>
            <summary>Listed directory src/views/pc • **7** results</summary>
        
| Name |
|-------|
| 📄 `casino.vue` |
| 📄 `fishing.vue` |
| 📄 `lottery.vue` |
| 📄 `live.vue` |
| 📄 `card.spec.ts` |
| 📄 `card.vue` |
| 📄 `live.spec.ts` |

</details>

---

<details>
            <summary>Searched codebase "casino" • **10** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

Read file: src/stores/casinoGame.test.ts

---

Read file: src/views/pc/card.spec.ts

---

<details>
            <summary>Searched codebase "useCasinoGameStore" • **0** results</summary>
        
No results found
</details>

---

<details>
            <summary>Searched codebase "useAccountInfoStore" • **0** results</summary>
        
No results found
</details>

---

<details>
            <summary>Searched codebase "useEjpStore" • **0** results</summary>
        
No results found
</details>

---

<details>
            <summary>Searched codebase "rankBoard" • **1** results</summary>
        
| File |
|------|
| `undefined` |

</details>

---

<details>
            <summary>Searched codebase "ElMessage" • **0** results</summary>
        
No results found
</details>

---

<details>
            <summary>Grep search for "ElMessage" • **48** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/upup.vue` | L44 | `ElMessage.error(error.message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/upup.vue` | L107 | `ElMessage.error(error.message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/LiveMenu.vue` | L2 | `import { ElMessageBox } from 'element-plus'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/LiveMenu.vue` | L46 | `ElMessageBox.alert(` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/LiveMenu.vue` | L67 | `ElMessage.error({` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/LiveMenu.vue` | L80 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mobile/live.vue` | L3 | `import { ElMessageBox } from 'element-plus'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mobile/live.vue` | L13 | `ElMessageBox.alert(` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mobile/live.vue` | L41 | `ElMessage.error({` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mobile/live.vue` | L50 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/lottery/gamePanel.vue` | L4 | `import { ElMessageBox } from 'element-plus'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/lottery/gamePanel.vue` | L64 | `ElMessageBox.alert(` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/CasinoIndex.vue` | L80 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/CasinoIndex.vue` | L131 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/SportUncompleteDialog.vue` | L78 | `ElMessage.error({` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/SportUncompleteDialog.vue` | L91 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/SportUncompleteDialog.vue` | L115 | `ElMessage.error({` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/SportUncompleteDialog.vue` | L130 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/LotteryDetailDialog.vue` | L102 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/LotteryDetailDialog.vue` | L118 | `ElMessage.error({` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/LotteryDetailDialog.vue` | L151 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/CardIndex.vue` | L78 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/CardIndex.vue` | L101 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/lottery/heroBoard.vue` | L3 | `import { ElMessageBox } from 'element-plus'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/lottery/heroBoard.vue` | L46 | `ElMessageBox.alert(` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/lottery/guessLike.vue` | L2 | `import { ElMessageBox } from 'element-plus'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/lottery/guessLike.vue` | L38 | `ElMessageBox.alert(` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/SportUncompeleteTable.vue` | L41 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/FishingIndex.vue` | L84 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/FishingIndex.vue` | L107 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/LiveSearch.vue` | L51 | `ElMessage.error(t('S_FIELD_REQUIRE'))` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/lottery/traditionBoard.vue` | L3 | `import { ElMessageBox } from 'element-plus'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/lottery/traditionBoard.vue` | L49 | `ElMessageBox.alert(` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/lottery/officialBoard.vue` | L3 | `import { ElMessageBox } from 'element-plus'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/lottery/officialBoard.vue` | L53 | `ElMessageBox.alert(` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/CardSearch.vue` | L45 | `ElMessage.error(t('S_FIELD_REQUIRE'))` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/CasinoSearch.vue` | L69 | `ElMessage.error(t('S_FIELD_REQUIRE'))` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/LiveIndex.vue` | L73 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/LiveIndex.vue` | L113 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/LiveIndex.vue` | L134 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/LotteryUncompleteTable.vue` | L40 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/LotteryCompleteTable.vue` | L58 | `ElMessage.error({` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/LotteryCompleteTable.vue` | L71 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/LotteryCompleteTable.vue` | L98 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/FishingSearch.vue` | L45 | `ElMessage.error(t('S_FIELD_REQUIRE'))` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/SportCompeleteTable.vue` | L67 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/SportCompleteDialog.vue` | L68 | `ElMessage.error({` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/SportCompleteDialog.vue` | L83 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/SportCompleteDialog.vue` | L108 | `ElMessage.error({` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betrecord/SportCompleteDialog.vue` | L124 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mobile/fishing.vue` | L30 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mobile/lottery.vue` | L36 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mobile/casino.vue` | L8 | `import { ElMessageBox } from 'element-plus'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mobile/casino.vue` | L104 | `ElMessageBox.alert(` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mobile/casino.vue` | L117 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mobile/index.vue` | L6 | `import { ElMessageBox } from 'element-plus'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mobile/index.vue` | L106 | `ElMessage.error({` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mobile/index.vue` | L115 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mobile/index.vue` | L132 | `ElMessageBox.alert(` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mobile/index.vue` | L145 | `return ElMessageBox.alert(` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mobile/index.vue` | L158 | `ElMessageBox.alert(` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mobile/index.vue` | L205 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mobile/card.vue` | L5 | `import { ElMessageBox } from 'element-plus'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mobile/card.vue` | L54 | `ElMessageBox.alert(` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mobile/card.vue` | L67 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/gamenews/casino.vue` | L47 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/gamenews/live.vue` | L44 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/gamenews/live.vue` | L54 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/gamenews/live.vue` | L85 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/gamenews/live.vue` | L99 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/gamenews/lottery.vue` | L49 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/mobile/GameCenter.vue` | L11 | `import { ElMessageBox } from 'element-plus'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/mobile/GameCenter.vue` | L42 | `ElMessageBox.alert(` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betinfo/live.vue` | L2 | `import { ElMessage } from 'element-plus'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betinfo/live.vue` | L38 | `ElMessage.error(error.message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betinfo/bbsport.vue` | L4 | `import { ElMessage } from 'element-plus'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betinfo/bbsport.vue` | L43 | `ElMessage.error(error.message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betinfo/lottery.vue` | L4 | `import { ElMessage } from 'element-plus'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/betinfo/lottery.vue` | L44 | `ElMessage.error(error.message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/fishIntro.vue` | L8 | `import { ElMessageBox } from 'element-plus'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/fishIntro.vue` | L60 | `ElMessageBox.alert(` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/fishIntro.vue` | L82 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/pc/card.vue` | L58 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/pc/card.vue` | L83 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mcenter/cash.vue` | L68 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/pc/live.vue` | L38 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/mcenter/effectivebetting.vue` | L42 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/pc/lottery.vue` | L172 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/pc/lottery.vue` | L200 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/pc/casino.vue` | L134 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/pc/fishing.vue` | L51 | `ElMessage.error((error as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/GameList.vue` | L46 | `ElMessage.error((err as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/EnterCardLobby.vue` | L2 | `import { ElMessageBox } from 'element-plus'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/EnterCardLobby.vue` | L26 | `ElMessageBox.alert(` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/GameCard.vue` | L12 | `import { ElMessageBox } from 'element-plus'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/GameCard.vue` | L71 | `ElMessage.error((err as Error).message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/components/pc/GameCard.vue` | L77 | `ElMessageBox.alert(` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/layout/mobile/index.vue` | L72 | `ElMessage.error(error.message)` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/layout/pc/header.vue` | L10 | `import { ElMessageBox } from 'element-plus'` |
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/layout/pc/header.vue` | L35 | `ElMessageBox.alert(` |

</details>

---

<details>
            <summary>Grep search for "import { ElMessage" • **0** files</summary>
        
No results found
</details>

---

<details>
            <summary>Grep search for "ElMessage" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/gt/gtp/bbgp_frontend/src/views/pc/casino.vue` | L134 | `ElMessage.error((error as Error).message)` |

</details>

---

Read file: src/views/pc/casino.vue

---

Read file: src/views/pc/casino.vue

---

Read file: src/views/pc/live.spec.ts

---

你要的是 `src/views/pc/casino.vue` 的 Vitest 單元測試碼，放在同目錄下，命名為 `casino.spec.ts`。這裡直接給你一個基礎範例，mock 主要 store、i18n、ElMessage，並驗證初始渲染、API error、登入狀態、搜尋、收藏等主要行為。

```ts
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import Casino from './casino.vue'
import { ref, nextTick } from 'vue'

// Mock i18n
vi.mock('vue-i18n', () => ({
  useI18n: () => ({ t: (k: string) => k })
}))

// Mock Pinia stores
vi.mock('@/stores/casinoGame', () => ({
  useCasinoGameStore: vi.fn(() => ({
    fetchCasinoData: vi.fn().mockResolvedValue(undefined),
    getTopGameMenu: vi.fn().mockReturnValue([{ id: 2, name: 'Top' }, { id: 84, name: 'All' }]),
    getSubGameMenu: vi.fn().mockReturnValue([{ id: 84, name: 'All' }]),
    getGameListByMenuId: vi.fn().mockReturnValue([{ gameId: 1, name: 'Game1' }]),
    searchGame: vi.fn().mockReturnValue([{ gameId: 1, name: 'Game1' }]),
    gameList: ref([{ gameId: 1, name: 'Game1' }]),
    favoriteGameList: ref([{ gameId: 1, name: 'Game1' }])
  }))
}))
vi.mock('@/stores/account', () => ({
  useAccountInfoStore: vi.fn(() => ({
    accountInfo: ref({ isLogin: true })
  }))
}))
vi.mock('@/stores/ejpremote', () => ({
  useEjpStore: vi.fn(() => ({
    jpList: ref([]),
    isShowJackpot: ref(false),
    connectWebSocket: vi.fn(),
    disconnectWebSocket: vi.fn()
  }))
}))

// Mock ElMessage
globalThis.ElMessage = { error: vi.fn() }

// Mock child components
vi.mock('@/components/pc/bbSwitchBoard.vue', () => ({ default: { template: '<div class="bb-switch-board"></div>' } }))
vi.mock('@/components/pc/historyBoard.vue', () => ({ default: { template: '<div class="history-board"></div>' } }))
vi.mock('@/components/TopGameMenu.vue', () => ({ default: { template: '<div class="top-game-menu"></div>' } }))
vi.mock('@/components/pc/SearchBox.vue', () => ({ default: { template: '<div class="search-box"></div>' } }))
vi.mock('@/components/SubGameMenu.vue', () => ({ default: { template: '<div class="sub-game-menu"></div>' } }))
vi.mock('@/components/MaintainDialog.vue', () => ({ default: { template: '<div class="maintain-dialog"></div>' } }))
vi.mock('@/components/pc/MenuBoard.vue', () => ({ default: { template: '<div class="menu-board"><slot /></div>' } }))
vi.mock('@/components/pc/GameCenter.vue', () => ({ default: { template: '<div class="game-center"></div>' } }))
vi.mock('@/components/buttons/ListMode.vue', () => ({ default: { template: '<div class="list-mode"></div>' } }))
vi.mock('@/components/buttons/FavoriteMode.vue', () => ({ default: { template: '<div class="favorite-mode"></div>' } }))

describe('Casino.vue', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders main structure', async () => {
    const wrapper = mount(Casino)
    await nextTick()
    expect(wrapper.find('.bbgp-main').exists()).toBe(true)
    expect(wrapper.find('.menu-board').exists()).toBe(true)
    expect(wrapper.find('.top-game-menu').exists()).toBe(true)
    expect(wrapper.find('.game-center').exists()).toBe(true)
  })

  it('shows no-data if no games', async () => {
    // Override getGameListByMenuId to return empty
    vi.mocked(require('@/stores/casinoGame').useCasinoGameStore).mockReturnValueOnce({
      ...require('@/stores/casinoGame').useCasinoGameStore(),
      getGameListByMenuId: vi.fn().mockReturnValue([]),
      gameList: ref([]),
      favoriteGameList: ref([])
    })
    const wrapper = mount(Casino)
    await nextTick()
    expect(wrapper.find('.no-data').exists()).toBe(true)
  })

  it('shows login prompt if not logged in and showFavs', async () => {
    vi.mocked(require('@/stores/account').useAccountInfoStore).mockReturnValueOnce({
      accountInfo: ref({ isLogin: false })
    })
    const wrapper = mount(Casino)
    await nextTick()
    // 手動設 showFavs
    wrapper.vm.showFavs = true
    await nextTick()
    expect(wrapper.text()).toContain('S_PleaseLogin')
  })

  it('calls ElMessage.error on fetchCasinoData error', async () => {
    vi.mocked(require('@/stores/casinoGame').useCasinoGameStore).mockReturnValueOnce({
      ...require('@/stores/casinoGame').useCasinoGameStore(),
      fetchCasinoData: vi.fn().mockRejectedValue(new Error('test error')),
      getTopGameMenu: vi.fn().mockReturnValue([]),
      getSubGameMenu: vi.fn().mockReturnValue([]),
      getGameListByMenuId: vi.fn().mockReturnValue([]),
      gameList: ref([]),
      favoriteGameList: ref([])
    })
    mount(Casino)
    await nextTick()
    expect(globalThis.ElMessage.error).toHaveBeenCalledWith('test error')
  })
})
```

這份測試碼涵蓋：
- 結構渲染
- 無資料顯示
- 未登入時收藏提示
- API error 彈訊息

你可以根據實際需求擴充更多互動、事件、狀態切換等測試。

---

