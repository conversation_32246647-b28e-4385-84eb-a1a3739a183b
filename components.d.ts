/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BackToTop: typeof import('./src/components/BackToTop.vue')['default']
    BetinfoBbsport: typeof import('./src/components/betinfo/bbsport.vue')['default']
    BetinfoLive: typeof import('./src/components/betinfo/live.vue')['default']
    BetinfoLottery: typeof import('./src/components/betinfo/lottery.vue')['default']
    BetrecordBetHistoryDialog: typeof import('./src/components/betrecord/BetHistoryDialog.vue')['default']
    BetrecordCardIndex: typeof import('./src/components/betrecord/CardIndex.vue')['default']
    BetrecordCardSearch: typeof import('./src/components/betrecord/CardSearch.vue')['default']
    BetrecordCardTable: typeof import('./src/components/betrecord/CardTable.vue')['default']
    BetrecordCasinoIndex: typeof import('./src/components/betrecord/CasinoIndex.vue')['default']
    BetrecordCasinoSearch: typeof import('./src/components/betrecord/CasinoSearch.vue')['default']
    BetrecordCasinoTable: typeof import('./src/components/betrecord/CasinoTable.vue')['default']
    BetrecordFishingIndex: typeof import('./src/components/betrecord/FishingIndex.vue')['default']
    BetrecordFishingSearch: typeof import('./src/components/betrecord/FishingSearch.vue')['default']
    BetrecordFishingTable: typeof import('./src/components/betrecord/FishingTable.vue')['default']
    BetrecordLiveIndex: typeof import('./src/components/betrecord/LiveIndex.vue')['default']
    BetrecordLiveSearch: typeof import('./src/components/betrecord/LiveSearch.vue')['default']
    BetrecordLiveTable: typeof import('./src/components/betrecord/LiveTable.vue')['default']
    BetrecordLotteryCompleteTable: typeof import('./src/components/betrecord/LotteryCompleteTable.vue')['default']
    BetrecordLotteryDetailDialog: typeof import('./src/components/betrecord/LotteryDetailDialog.vue')['default']
    BetrecordLotteryIndex: typeof import('./src/components/betrecord/LotteryIndex.vue')['default']
    BetrecordLotteryUncompleteTable: typeof import('./src/components/betrecord/LotteryUncompleteTable.vue')['default']
    BetrecordSport: typeof import('./src/components/betrecord/sport.vue')['default']
    BetrecordSportCompeleteTable: typeof import('./src/components/betrecord/SportCompeleteTable.vue')['default']
    BetrecordSportCompleteDialog: typeof import('./src/components/betrecord/SportCompleteDialog.vue')['default']
    BetrecordSportIndex: typeof import('./src/components/betrecord/SportIndex.vue')['default']
    BetrecordSportUncompeleteTable: typeof import('./src/components/betrecord/SportUncompeleteTable.vue')['default']
    BetrecordSportUncompleteDialog: typeof import('./src/components/betrecord/SportUncompleteDialog.vue')['default']
    ButtonsFavoriteMode: typeof import('./src/components/buttons/FavoriteMode.vue')['default']
    ButtonsFreeTrial: typeof import('./src/components/buttons/FreeTrial.vue')['default']
    ButtonsGameEnter: typeof import('./src/components/buttons/GameEnter.vue')['default']
    ButtonsGameIntro: typeof import('./src/components/buttons/GameIntro.vue')['default']
    ButtonsListMode: typeof import('./src/components/buttons/ListMode.vue')['default']
    ButtonsMemberCenter: typeof import('./src/components/buttons/MemberCenter.vue')['default']
    CashCash: typeof import('./src/components/cash/cash.vue')['default']
    CashCashSearch: typeof import('./src/components/cash/CashSearch.vue')['default']
    CashCashTable: typeof import('./src/components/cash/CashTable.vue')['default']
    EffectivebettingEffectivebetting: typeof import('./src/components/effectivebetting/effectivebetting.vue')['default']
    EffectivebettingEffectiveBettingSearch: typeof import('./src/components/effectivebetting/EffectiveBettingSearch.vue')['default']
    EffectivebettingEffectiveBettingTable: typeof import('./src/components/effectivebetting/EffectiveBettingTable.vue')['default']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBacktop: typeof import('element-plus/es')['ElBacktop']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCarousel: typeof import('element-plus/es')['ElCarousel']
    ElCarouselItem: typeof import('element-plus/es')['ElCarouselItem']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElFooter: typeof import('element-plus/es')['ElFooter']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    Error404: typeof import('./src/views/error404.vue')['default']
    FishIntro: typeof import('./src/components/fishIntro.vue')['default']
    Gamecard: typeof import('./src/components/gamecard.vue')['default']
    Gamelist: typeof import('./src/components/gamelist.vue')['default']
    GamenewsCasino: typeof import('./src/components/gamenews/casino.vue')['default']
    GamenewsLive: typeof import('./src/components/gamenews/live.vue')['default']
    GamenewsLottery: typeof import('./src/components/gamenews/lottery.vue')['default']
    GamenewsNewsContent: typeof import('./src/components/gamenews/NewsContent.vue')['default']
    GamePic: typeof import('./src/components/GamePic.vue')['default']
    LiveMenu: typeof import('./src/components/LiveMenu.vue')['default']
    LiveRule: typeof import('./src/components/LiveRule.vue')['default']
    LotteryGamePanel: typeof import('./src/components/lottery/gamePanel.vue')['default']
    LotteryGuessLike: typeof import('./src/components/lottery/guessLike.vue')['default']
    LotteryHeroBoard: typeof import('./src/components/lottery/heroBoard.vue')['default']
    LotteryOfficialBoard: typeof import('./src/components/lottery/officialBoard.vue')['default']
    LotteryTraditionBoard: typeof import('./src/components/lottery/traditionBoard.vue')['default']
    MaintainDialog: typeof import('./src/components/MaintainDialog.vue')['default']
    MCasinoFooter: typeof import('./src/components/mCasinoFooter.vue')['default']
    Mcenter: typeof import('./src/layout/mcenter/index.vue')['default']
    McenterBet: typeof import('./src/views/mcenter/bet.vue')['default']
    McenterBetinfo: typeof import('./src/views/mcenter/betinfo.vue')['default']
    McenterCash: typeof import('./src/views/mcenter/cash.vue')['default']
    McenterEffectivebetting: typeof import('./src/views/mcenter/effectivebetting.vue')['default']
    McenterGameMaintainTooltip: typeof import('./src/components/mcenter/GameMaintainTooltip.vue')['default']
    McenterGamenews: typeof import('./src/views/mcenter/gamenews.vue')['default']
    McenterMainMenu: typeof import('./src/components/mcenter/MainMenu.vue')['default']
    McenterPaginationComponent: typeof import('./src/components/mcenter/PaginationComponent.vue')['default']
    McenterSubMenu: typeof import('./src/components/mcenter/SubMenu.vue')['default']
    McenterTopHeader: typeof import('./src/components/mcenter/TopHeader.vue')['default']
    Mobile: typeof import('./src/views/mobile/index.vue')['default']
    MobileBbSwitchBoard: typeof import('./src/components/mobile/bbSwitchBoard.vue')['default']
    MobileCard: typeof import('./src/views/mobile/card.vue')['default']
    MobileCasino: typeof import('./src/views/mobile/casino.vue')['default']
    MobileFishing: typeof import('./src/views/mobile/fishing.vue')['default']
    MobileGameCenter: typeof import('./src/components/mobile/GameCenter.vue')['default']
    MobileGames: typeof import('./src/components/mobileGames.vue')['default']
    MobileHistoryBoard: typeof import('./src/components/mobile/historyBoard.vue')['default']
    MobileLive: typeof import('./src/views/mobile/live.vue')['default']
    MobileLottery: typeof import('./src/views/mobile/lottery.vue')['default']
    MobileSearchBox: typeof import('./src/components/mobile/SearchBox.vue')['default']
    MobileUserInfo: typeof import('./src/components/mobile/UserInfo.vue')['default']
    PcBbSwitchBoard: typeof import('./src/components/pc/bbSwitchBoard.vue')['default']
    PcCard: typeof import('./src/views/pc/card.vue')['default']
    PcCasino: typeof import('./src/views/pc/casino.vue')['default']
    PcEnterCardLobby: typeof import('./src/components/pc/EnterCardLobby.vue')['default']
    PcFishing: typeof import('./src/views/pc/fishing.vue')['default']
    PcGameCard: typeof import('./src/components/pc/GameCard.vue')['default']
    PcGameCenter: typeof import('./src/components/pc/GameCenter.vue')['default']
    PcGameList: typeof import('./src/components/pc/GameList.vue')['default']
    PcGames: typeof import('./src/components/pcGames.vue')['default']
    PcHistoryBoard: typeof import('./src/components/pc/historyBoard.vue')['default']
    PcLive: typeof import('./src/views/pc/live.vue')['default']
    PcLottery: typeof import('./src/views/pc/lottery.vue')['default']
    PcMenuBoard: typeof import('./src/components/pc/MenuBoard.vue')['default']
    PcSearchBox: typeof import('./src/components/pc/SearchBox.vue')['default']
    PcUserInfo: typeof import('./src/components/pc/UserInfo.vue')['default']
    RankBoard: typeof import('./src/components/rankBoard.vue')['default']
    RankDialog: typeof import('./src/components/RankDialog.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SelectorArraySelector: typeof import('./src/components/selector/ArraySelector.vue')['default']
    SelectorKeyValueSelector: typeof import('./src/components/selector/KeyValueSelector.vue')['default']
    SelectorObjectSelector: typeof import('./src/components/selector/ObjectSelector.vue')['default']
    SubGameMenu: typeof import('./src/components/SubGameMenu.vue')['default']
    TopGameMenu: typeof import('./src/components/TopGameMenu.vue')['default']
    Upup: typeof import('./src/views/upup.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
