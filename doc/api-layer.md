---
path: doc/api-layer.md
---
# API Layer: Design and Conventions

This document details the design patterns, programming style, and conventions observed in the API layer (`src/api/`) of the `bbgp_frontend` project.

## Core Principles

*   **Centralized API Calls:** All interactions with backend services are managed through modules within the `src/api/` directory. Each file typically corresponds to a specific resource or feature set (e.g., `account.ts`, `game.ts`, `betrecord.ts`).
*   **`apiRequest` Utility:** A utility, presumably named `apiRequest` (located at `@/utils/apiRequest`), is consistently used for making HTTP requests (GET, POST, DELETE). This centralizes request logic and allows for easy modification or extension (e.g., adding default headers, error interceptors).
*   **Asynchronous Operations:** `async/await` is the standard for handling asynchronous API calls, leading to cleaner and more readable code.
*   **TypeScript Integration:**
    *   **Parameter Typing:** Function parameters for API calls are strongly typed using interfaces defined in `src/types/` (e.g., `IGetAccountInfoParams`, `IGetGameListParams`).
    *   **Return Typing:** The expected shape of the transformed API response is also typed (e.g., `IAccountInfo`, `ILobbyLink`).
    *   **Type Assertions:** After data transformation, type assertions (`as IAccountInfo`) are sometimes used to cast the transformed object to its specific interface.

## Data Transformation

A significant responsibility of the API layer is to transform raw data from backend responses into a format more suitable for the frontend.

*   **Dedicated Transformer Functions:** Many API modules define private helper functions for this purpose (e.g., `cashRecordDataTransform`, `liveDataTransForm`, `lotteryListDataTransform`, `effectiveBettingTransform`).
*   **Key Naming Convention:** A common transformation is converting snake\_case keys from the API (e.g., `hall_id`, `game_list`) to camelCase (e.g., `hallId`, `gameList`) for JavaScript/TypeScript conventions.
*   **Date Formatting:** `dayjs` is extensively used to parse and format date strings from the API, often including timezone conversions (e.g., `dayjs(item.wagers_date).tz('Etc/GMT+4').format('YYYY-MM-DD HH:mm:ss')`).
*   **Default Values:** Transformer functions often provide default values for fields that might be missing or null in the API response (e.g., `search_range = [{ start_date: '', end_date: '' }]`, `total = [{ total_bet_amount: '', ... }]`). This helps prevent runtime errors in components.
*   **Structure Adaptation:** Responses are sometimes restructured. For example, a list of game objects might be converted into a dictionary/map where game IDs are keys for faster lookups (e.g., `getLiveGameList`, `getLotteryGameList`).

## Error Handling and Maintenance

*   **`try...catch` Blocks:** All API call functions are wrapped in `try...catch` blocks.
*   **Error Propagation:** Caught errors are typically re-thrown using `return Promise.reject(error)` or by directly throwing the error. This allows calling components or services to handle the error appropriately (e.g., display a message to the user).
*   **Maintenance State:**
    *   API responses often include an `is_maintain` flag (or a specific `code` like `561000008`).
    *   If `is_maintain` is true, the API function typically returns a standardized object indicating maintenance mode, usually including `maintain: true` and `maintainInfo` (formatted by `maintainText` utility).
    *   This allows components to display a user-friendly maintenance message instead of an error or broken UI.
    *   Example:
        ```typescript
        if (resData.data.is_maintain) {
          result = {
            maintain: true,
            maintainInfo: maintainText(resData.data.maintain_info[0])
          };
        } else {
          // process data
        }
        ```

## Specific API Examples and Patterns

*   **`getAccountInfo` (`account.ts`):**
    *   Simple GET request.
    *   Adds an `isLogin: true` flag to the transformed data.
*   **`getBBfishingList` (`bbfish.ts`):**
    *   Maps `game_list` items, transforming keys and adding a static `nickname: 'BB'`.
*   **`getLiveBetInfo`, `getSportBetInfo`, `getLotteryBetInfo` (`betinfo.ts`):**
    *   Handle `is_maintain` flag.
    *   Perform complex transformations, especially `getSportBetInfo` which creates a `groupNameMap` for easier data lookup during mapping.
*   **`betrecord.ts` (Multiple functions):**
    *   This is a large file with many API calls, each with its own transformer function (e.g., `liveDataTransForm`, `casinoDateDataTransForm`).
    *   Demonstrates consistent patterns for handling pagination, totals, search ranges, and maintenance states.
    *   `sportContentTransform` builds an HTML string from API data.
*   **`game.ts` (Multiple functions):**
    *   Handles fetching game lists, menus, and lobby links.
    *   `getCardLobbyLink`, `getLiveLobbyLink`, `getLotteryList` involve `testApiEndpoints` utility, suggesting a mechanism to find a working domain from a list.
    *   `getLotteryLobbyLink` throws an error directly if in maintenance, which is a slight variation from returning a maintenance object.
*   **`upup.ts` (`upUp` function):**
    *   Uses `useApiUrl()` composable, suggesting dynamic API base URLs.
    *   Calculates `hours` from `upup_end_time`.

## Recommendations and Observations

*   **Consistency:** The API layer is generally very consistent in its approach to making requests, transforming data, and handling errors/maintenance.
*   **Clarity:** The use of TypeScript and dedicated transformer functions enhances code readability and maintainability.
*   **Error Details:** While errors are propagated, the API layer itself doesn't usually log detailed error messages to the console (some exceptions in `mcenter.ts`). This responsibility might lie with a global error handler or the `apiRequest` utility.
*   **Type Safety:** The strong typing is a significant advantage. Consider ensuring that all `any` types in transformer function parameters or intermediate variables are replaced with more specific types where possible for even greater safety.
*   **Transformer Function Placement:** Transformer functions are co-located with their respective API calls, which is good for organization.
