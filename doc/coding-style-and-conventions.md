
```markdown
---
path: doc/coding-style-and-conventions.md
---
# Coding Style and Conventions

This document summarizes the general coding style, naming conventions, and other notable software engineering practices observed in the `bbgp_frontend` project.

## General TypeScript Usage

*   **Strong Typing:** TypeScript is consistently used across the project, including API layers, Vue components, and utility functions.
*   **Interfaces and Types:** Custom types and interfaces are defined in the `src/types/` directory (e.g., `IAccountInfo`, `IGameMenu`, `ILiveRecord`). This promotes code clarity, maintainability, and helps catch errors early.
*   **`any` Type:** While generally well-typed, there are instances of `any` being used, particularly in data transformation functions or error handling (e.g., `catch (error: any)`). Minimizing `any` in favor of more specific types or `unknown` (with appropriate type checking) can further enhance type safety.
*   **Type Assertions:** `as TypeName` is used when the developer is certain about the type of a variable, often after manual data transformation (e.g., `return result as ILiveBetInfo`).

## Naming Conventions

*   **Variables and Functions:** `camelCase` is standard (e.g., `getAccountInfo`, `isLoading`, `fetchSportBetinfo`).
*   **Interfaces and Types:** `PascalCase` with an `I` prefix for interfaces is common (e.g., `IAccountInfo`, `IGameMenu`), though not universally applied to all type aliases. `PascalCase` is standard for type aliases (e.g., `HallInfo`, `LangData`).
*   **Vue Components:** `PascalCase` for component file names (e.g., `LiveMenu.vue`, `GamePic.vue`) and when referencing them in templates.
*   **Constants:** `UPPER_SNAKE_CASE` is not explicitly shown in these snippets but is a common convention. `gameKindNameMap` in `lobbyswitch.ts` uses `camelCase` for its keys, which is appropriate for object keys.

## Code Formatting and Readability

*   **Indentation and Spacing:** Code is generally well-formatted, suggesting the use of a code formatter like Prettier or ESLint with formatting rules.
*   **Modularity:**
    *   Code is broken down into smaller, manageable files and functions.
    *   API calls are grouped by resource/feature.
    *   Vue components are self-contained units.
*   **Comments:** Comments are used sparingly, with the code often being self-explanatory due to good naming and typing. JSDoc-style comments are not prevalent in the provided snippets.
*   **Destructuring:** Object destructuring is frequently used to extract properties from objects, especially API responses (e.g., `const { code, message, data } = mockData`).
*   **Optional Chaining (`?.`) and Nullish Coalescing (`??` or `||`):**
    *   Optional chaining is used to safely access nested properties that might be undefined (e.g., `resData.data.game_limit_list?.map(...)`).
    *   Nullish coalescing or OR operators are used to provide default values (e.g., `wagers_list = []`, `total = [{...}] || []`).

## Asynchronous Programming

*   **`async/await`:** This is the standard pattern for handling promises, making asynchronous code look and behave a bit more like synchronous code, which improves readability.
*   **`Promise.reject(error)`:** Commonly used in `catch` blocks within API functions to propagate errors.

## Error Handling

*   **`try...catch`:** Standard for operations that might fail, especially API calls.
*   **User Feedback:** `ElMessage.error()` from Element Plus is used to display error messages to the user.
*   **Console Logging:**
    *   `console.error(error)` or `console.log(error)` is sometimes used within `catch` blocks in API or component methods (e.g., `mcenter.ts`, `LiveIndex.vue`). Centralized error logging might be beneficial.

## Vue-Specific Conventions

*   **`<script setup>`:** Preferred for Vue component logic.
*   **Scoped SCSS:** Standard for component styling, preventing style leakage.
*   **CSS Variables:** Used for theming, promoting consistency and ease of theme changes.
*   **Composables:** Custom composables like `useImportTheme`, `useApiUrl`, and library composables (`useI18n`, `useCookies`) are used to encapsulate and reuse logic.

## Utility Usage

*   **`dayjs`:** The go-to library for date and time manipulations, consistently used for formatting API date strings and timezone conversions.
*   **`maintainText`:** A custom utility for formatting maintenance messages, ensuring consistency.
*   **`testApiEndpoints`:** A utility used in `game.ts` to select a working API domain from a list, indicating a strategy for domain failover or selection.

## Testing Conventions (Vitest)

*   **Mocking:** `vi.mock` is heavily relied upon to isolate units under test.
*   **Clarity:** Test descriptions are generally clear.
*   **`vi.mocked()`:** Used to type-safely access mocked functions.
*   **`flushPromises()` / `nextTick()`:** Essential in component tests involving asynchronous operations.

## Potential Areas for Enhancement

*   **Stricter Typing:** Reducing the use of `any` where possible.
*   **Global Error Handling:** Consider a more centralized global error handling/logging mechanism if not already in place beyond individual `console.error` calls.
*   **Code Comments:** While the code is often clear, more complex logic or business rules could benefit from explanatory comments.
*   **Linting and Formatting:** Enforcing consistent code style strictly through ESLint and Prettier (if not already at maximum strictness) can prevent minor inconsistencies.
