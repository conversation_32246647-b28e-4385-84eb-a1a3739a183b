---
path: doc/index.md
---
# Project Documentation Index

This index provides a quick overview and links to detailed documentation about the `bbgp_frontend` Vue 3 project.

## Core Project Information

*   **Project Overview**
    *   A high-level summary of the project, including core technologies, inferred project structure, and key architectural patterns.

## API Layer

*   **API Layer: Design and Conventions**
    *   Details the design patterns, programming style, and conventions observed in the API layer (`src/api/`), including data transformation, error handling, and maintenance state management.

## Component Design

*   **Vue Component Design and Conventions**
    *   Describes common patterns, programming style, and conventions for Vue 3 components, covering `<script setup>`, Composition API usage, props, events, styling (SCSS, scoped styles, CSS variables), and common UI patterns.

## Testing

*   **Testing Strategy: Vitest and Vue Test Utils**
    *   Outlines the testing approach, focusing on unit tests for both the API layer and Vue components using Vitest and Vue Test Utils, including mocking strategies.

## Coding Standards

*   **Coding Style and Conventions**
    *   Summarizes general coding style, naming conventions, TypeScript usage, asynchronous programming, error handling, and other software engineering practices observed throughout the project.

