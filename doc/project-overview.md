---
path: doc/project-overview.md
---
# Project Overview

This document provides a high-level overview of the `bbgp_frontend` Vue 3 project, focusing on its structure, technologies, and general architecture as observed from the provided codebase.

## Core Technologies

*   **Framework:** Vue 3 (using `<script setup lang="ts">` syntax)
*   **Language:** TypeScript
*   **UI Library:** Element Plus
*   **Routing:** Vue Router
*   **State Management:** Pinia (implied by `use...Store` patterns, e.g., `useCardGameStore`, `useFishingGameStore`)
*   **HTTP Client:** A custom `apiRequest` utility (likely a wrapper around a library like Axios or `fetch`)
*   **Testing:** Vitest with Vue Test Utils
*   **Date/Time Manipulation:** `dayjs`
*   **Internationalization (i18n):** `vue-i18n`
*   **Styling:** SCSS (scoped per component)

## Project Structure (Inferred)

The project appears to follow a standard Vue project structure:

*   **`src/api/`**: Contains modules for interacting with backend APIs. Each module typically groups related API calls (e.g., `account.ts`, `game.ts`).
*   **`src/components/`**: Houses reusable Vue components. This includes general UI elements (e.g., `BackToTop.vue`, `GamePic.vue`) and more specific components related to features like bet information and bet records.
*   **`src/views/`**: (Not explicitly provided, but typical for page-level components managed by Vue Router).
*   **`src/router/`**: (Not explicitly provided, but would contain Vue Router configuration).
*   **`src/stores/`**: (Implied by usage like `useCardGameStore`) Contains Pinia store modules for state management.
*   **`src/types/`**: Defines TypeScript interfaces and types used throughout the application, promoting type safety and clarity (e.g., `account.d.ts`, `game.d.ts`).
*   **`src/utils/`**: Contains utility functions and modules, such as `apiRequest.ts` for API calls, `maintaintxt.ts` for formatting maintenance messages, and `date.ts` for date utilities.
*   **`src/assets/`**: For static assets like images (e.g., `logo.svg`).
*   **`src/language/`**: (Implied by `i18n` mocks) Likely contains translation files.
*   **`src/App.vue`**: The root Vue component, setting up global configurations like Element Plus and handling global concerns like page loading indicators.

## Key Architectural Patterns

*   **API Abstraction:** API calls are centralized in the `src/api/` directory, providing a clear separation of concerns.
*   **Data Transformation:** API response data is often transformed into frontend-friendly structures within the API modules before being returned to the calling components or stores. This includes renaming keys (snake\_case to camelCase) and formatting data (e.g., dates).
*   **Error Handling:** `try...catch` blocks are consistently used in API functions to handle errors, often re-throwing them or using `Promise.reject`. UI feedback for errors is provided using `ElMessage.error`.
*   **Maintenance Handling:** A common pattern is to check for `is_maintain` flags in API responses and display appropriate maintenance messages to the user, often using a utility like `maintainText`.
*   **Composability:** Vue 3's Composition API is utilized with `<script setup>`, and custom composables (e.g., `useImportTheme`, `useCookies`) are used.
*   **Strong Typing:** TypeScript is used throughout, with interfaces defined in `src/types/` to ensure data consistency and improve developer experience.
