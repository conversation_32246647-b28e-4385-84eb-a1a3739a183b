---
path: doc/testing-strategy.md
---
# Testing Strategy: Vitest and Vue Test Utils

This document outlines the testing strategy employed in the `bbgp_frontend` project, primarily focusing on unit tests written with Vitest and Vue Test Utils.

## Core Testing Tools

*   **Test Runner & Framework:** Vitest (`describe`, `it`, `expect`, `vi`)
*   **Vue Component Testing:** Vue Test Utils (`mount`)
*   **Mocking:** Vitest's `vi.mock` and `vi.fn` are used extensively.
*   **Internationalization (i18n) Mocking:** `createI18n` from `vue-i18n` is used to provide a mock i18n instance for components.

## API Layer Testing (`src/api/*.test.ts`)

API layer tests focus on verifying the correct behavior of functions that interact with the backend.

*   **Mocking `apiRequest`:**
    *   The `apiRequest` utility (e.g., `axios` instance or custom wrapper) is consistently mocked.
    *   `vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })` simulates successful API responses.
    *   `vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))` simulates API failures.
*   **Test Coverage:**
    1.  **Successful Response & Data Transformation:**
        *   Verifies that the API function calls the correct `apiRequest` method with the correct URL and parameters.
        *   Asserts that the returned data is transformed as expected (e.g., snake\_case to camelCase, date formatting, default values).
        *   Example:
            ```typescript
            it('should return transformed data when request is successful', async () => {
              const mockData = { /* ... API response ... */ };
              vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData });
              const result = await getAccountInfo({ hall_id: 123, session_id: '456' });
              expect(result).toEqual({ /* ... expected transformed data ... */ });
            });
            ```
    2.  **Request Failure:**
        *   Ensures that the API function correctly handles and propagates errors when `apiRequest` rejects.
        *   `await expect(...).rejects.toThrow('Network Error')` is commonly used.
    3.  **Maintenance Mode:**
        *   Tests the scenario where the API indicates a maintenance period.
        *   Asserts that the function returns the standardized maintenance object (e.g., `{ maintain: true, maintainInfo: '...' }`).
*   **Helper Mocks:**
    *   Utilities like `@/utils/maintaintxt`, `@/utils/apiUrl`, and `@/language/i18n` are often mocked to isolate the unit under test.
*   **Date Handling:** `dayjs` with timezone plugins (`utc`, `timezone`) is frequently initialized in test files if the corresponding API module performs date manipulations.
*   **Setup and Teardown:** `beforeEach(() => { vi.clearAllMocks() })` is used in some files (e.g., `favorite.test.ts`) to ensure mocks are reset between tests.

## Vue Component Testing (`src/components/**/*.spec.ts`)

Component tests focus on rendering, user interactions, prop handling, and event emissions.

*   **Mounting Components:** `mount` from `@vue/test-utils` is used to create a wrapper around the component.
*   **Global Mocks & Plugins:**
    *   `i18n` is provided globally using `createI18n`.
    *   Vue Router is sometimes provided if components use `useRoute` or `<router-link>`.
    *   Pinia stores are set up using `setActivePinia(createPinia())` when testing components that interact with stores (e.g., `CardIndex.spec.ts`).
*   **Test Coverage:**
    1.  **Rendering:**
        *   Verifies that the component renders correctly based on its props and internal state.
        *   `wrapper.find()`, `wrapper.findAll()`, `wrapper.findComponent()` are used to locate elements/components.
        *   Assertions on text content (`.text()`), attributes (`.attributes()`), and classes (`.classes()`).
    2.  **Prop Handling:**
        *   Tests how the component behaves with different prop values.
        *   `wrapper.setProps()` can be used to update props.
    3.  **User Interactions & Event Emission:**
        *   Simulates user events like clicks (`.trigger('click')`) or input (`.setValue()`).
        *   Asserts that the component emits the correct events with the expected payload (`wrapper.emitted()`).
        *   Verifies that methods are called (`vi.spyOn(vm, 'methodName')`).
    4.  **API Calls (within components):**
        *   If components make direct API calls (or via store actions that make API calls), these are mocked similarly to API layer tests.
        *   `flushPromises()` or `await nextTick()` are crucial for waiting for asynchronous operations (like API calls or state updates) to complete before making assertions.
    5.  **Conditional Rendering:** Tests different branches of `v-if`/`v-else` logic.
    6.  **List Rendering:** Verifies that `v-for` loops render the correct number of items with the correct data.
    7.  **Dialogs/Modals:**
        *   Tests opening and closing behavior.
        *   Verifies content within the dialog.
        *   Mocks child components like `ElDialog` if necessary or interacts with them via `findComponent`.
*   **Specific Component Test Examples:**
    *   **`LiveMenu.spec.ts`:** Tests rendering of menu items and the `openLiveHall` method, including spying on `window.open`.
    *   **`LiveRule.spec.ts`:** Tests rendering of rule items and the `openRule` method, verifying the constructed URL. Mocks `localStorage`.
    *   **`MaintainDialog.spec.ts`:** Tests prop rendering and dialog close behavior. Uses `await nextTick()` for DOM updates.
    *   **`CardIndex.spec.ts`, `CasinoIndex.spec.ts`, `FishingIndex.spec.ts`, `LiveIndex.spec.ts`:** These are more complex "index" components that orchestrate search, table display, and pagination.
        *   They mock child components (Search, Table, Pagination).
        *   They mock API calls made during their lifecycle or in response to events.
        *   They test event handling from child components (e.g., `@search`, `@currentChange`).
        *   They mock Pinia stores and their actions/getters.
    *   **`LotteryDetailDialog.spec.ts`, `SportCompleteDialog.spec.ts`:** Test complex dialogs with internal API calls, select dropdowns, and table displays. They extensively use `flushPromises` and `nextTick`.

## General Testing Practices

*   **Isolation:** Unit tests aim to test one unit (function or component) in isolation, mocking its dependencies.
*   **Readability:** Test descriptions (`describe`, `it`) are clear and indicative of what's being tested.
*   **AAA Pattern (Arrange, Act, Assert):** Tests generally follow this structure.
*   **Error Handling Tests:** For components that fetch data, tests often include scenarios for API errors and how the component displays these errors (e.g., using `ElMessage.error`).
*   **Maintenance State Tests:** Components that depend on API data also test how they render when the API indicates a maintenance state.

## Potential Areas for Improvement/Consideration

*   **Snapshot Testing:** Could be considered for components with complex templates to easily detect unintended UI changes, though it's not currently used.
*   **End-to-End Testing:** While unit and component tests are valuable, E2E tests (e.g., with Cypress or Playwright) would provide broader coverage of user flows.
*   **Coverage Reports:** Regularly checking test coverage can help identify untested parts of the codebase.
