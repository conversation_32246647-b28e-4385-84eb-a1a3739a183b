---
path: doc/vue-components.md
---
# Vue Component Design and Conventions

This document describes the common patterns, programming style, and conventions observed in the Vue 3 components within the `bbgp_frontend` project.

## Core Vue 3 Features

*   **`<script setup lang="ts">`:** This is the standard way of writing component logic, offering a more concise and performant approach compared to the Options API.
*   **Composition API:**
    *   `ref` and `reactive` are used for creating reactive state.
    *   `computed` properties are used for deriving state.
    *   `watch` and `watchEffect` (though `watch` is more explicitly seen in the provided snippets, e.g., in `LotteryDetailDialog.vue`) are used for reacting to state changes.
    *   Lifecycle hooks like `onMounted` (implied by initial data fetching, e.g., `LiveIndex.vue`'s `init` function) are used for setup and teardown logic.
*   **Props:**
    *   Defined using `defineProps` with TypeScript for type safety.
    *   `PropType` is used for complex prop types (e.g., `Array as PropType<ILiveGameMenu[]>`).
    *   Default values and `required` flags are specified.
*   **Models (`v-model`):**
    *   `defineModel` is used for creating `v-model` bindings on custom components, simplifying two-way data binding (e.g., `MaintainDialog.vue`, `SubGameMenu.vue`).
*   **Events:**
    *   Custom events are declared using `defineEmits`.
    *   `emit('eventName', payload)` is used to emit events to parent components.
*   **Slots:** (Not explicitly shown in detail in the provided snippets, but Element Plus components heavily rely on them, e.g., `<template #error>` in `GamePic.vue`, `<template #footer>` in `MaintainDialog.vue`).
*   **Suspense:** Used in `App.vue` to handle asynchronous component loading with `RouterView`.

## Component Structure

A typical component file (`.vue`) follows this structure:

```vue
<script setup lang="ts">
// Imports (Vue, libraries, types, API calls, stores, composables)
// i18n setup (useI18n)
// Cookie handling (useCookies)
// Router/Route access (useRouter, useRoute)
// Store access (useSomeStore)

// Props definition (defineProps)
// Model definition (defineModel)
// Emits definition (defineEmits)

// Reactive state (ref, reactive)
// Computed properties (computed)

// Methods / Event Handlers (async functions, simple functions)
// Watchers (watch, watchEffect)

// Lifecycle hooks (onMounted, etc., often via an init function called directly)
</script>

<template>
  <!-- HTML structure with Vue directives (v-if, v-for, @click, :prop, etc.) -->
  <!-- Element Plus components -->
  <!-- Child components -->
</template>

<style lang="scss" scoped>
/* SCSS styles, scoped to the component */
/* Use of CSS variables for theming */
/* :deep() selector for styling child component internals */
</style>
