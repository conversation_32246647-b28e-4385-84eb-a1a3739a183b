## 命名規範

### 檔案命名

- 元件檔案使用 PascalCase：`GameCard.vue`
- 工具函數使用 camelCase：`useGetImgUrl.ts`
- 測試檔案加上 .test 或 .spec：`GameCard.test.ts`
- 型別定義檔案使用 PascalCase：`BetRecord.ts`

### 元件命名

- 元件名稱使用 PascalCase
- 頁面元件加上 Page 後綴：`GameListPage.vue`
- 共用元件使用描述性名稱：`GameCard.vue`
- 布局元件加上 Layout 後綴：`DefaultLayout.vue`

## TypeScript 規範

### 型別定義

- 介面名稱以 I 開頭：`IGameInfo`
- 型別名稱使用 PascalCase：`GameType`
- 使用 type 而非 interface 定義簡單型別
- 必須為所有 props 定義型別

### Props 定義

```typescript
const props = defineProps<{
  gameList: IGameInfo[]
  isLoading?: boolean
}>()
```

### Emits 定義

```typescript
const emit = defineEmits<{
  (e: 'update', value: string): void
  (e: 'select', item: IGameInfo): void
}>()
```

## 樣式規範

### SCSS 使用規範

- 使用 scoped 樣式
- 使用 BEM 命名規範
- 避免過深的選擇器巢狀
- 使用變數管理主題色

```scss
.game-card {
  &__title {
    font-size: var(--font-size-lg);
  }

  &__content {
    padding: var(--spacing-md);
  }
}
```

## API 請求規範

### API 函數命名

- 使用動詞開頭：`getGameList`、`updateUserInfo`
- 回傳 Promise 型別
- 使用 TypeScript 型別註解

```typescript
async function getGameList(): Promise<IGameInfo[]> {
  const response = await request.get('/api/games')
  return response.data
}
```

### src/api/ 標準 API 呼叫方式

- 統一使用 `apiRequest`（`@/utils/apiRequest`）作為 axios 實例
- 需匯入型別與轉換函式（如有）
- 失敗時直接 `Promise.reject(error)`
- 回傳資料前可用 transform 處理
- 例：

```typescript
import apiRequest from '@/utils/apiRequest'
import type { IGameInfo } from '@/types/game'

const gameListTransform = (data: any): IGameInfo[] => {
  // ...transform logic
  return data as IGameInfo[]
}

export const getGameList = async (params: { lang: string }): Promise<IGameInfo[]> => {
  try {
    const res = await apiRequest.get('/api/game/list', { params })
    return gameListTransform(res.data.data)
  } catch (error: any) {
    return Promise.reject(error)
  }
}
```

- 支援多種 HTTP 方法：`get`、`post`、`delete` ...
- 若需 baseURL，會自動由 `configStore.apiUrl` 注入
- 錯誤訊息與 session 處理已於 apiRequest 實作
- 只需專注於資料轉換與型別註解

## 狀態管理規範

### Pinia Store 規範

- Store 檔案名使用 camelCase
- 定義明確的 state 型別
- Actions 使用 async/await
- 提供 getter 存取計算屬性

```typescript
export const useGameStore = defineStore('game', {
  state: () => ({
    list: [] as IGameInfo[],
    loading: false
  }),
  getters: {
    sortedGames: (state) => [...state.list].sort((a, b) => b.score - a.score)
  },
  actions: {
    async fetchGames() {
      this.loading = true
      try {
        this.list = await getGameList()
      } finally {
        this.loading = false
      }
    }
  }
})
```

## 測試規範

### 單元測試

- 測試檔案與源碼檔案同目錄
- 使用 describe 描述測試套件
- 使用 it/test 描述測試案例
- 測試覆蓋重要業務邏輯
- 使用 Vitest + @vue/test-utils
- 組件測試用 mount，API/mock 用 vi.mock
- 斷言用 expect
- 常見 mock：api、store、i18n、router、window、Element Plus
- 支援 async/await、emit、spy、錯誤處理
- 建議型別註解 wrapper
- 可用 nextTick/flushPromises 處理異步
- 事件/emit 驗證
- snapshot 視需求

#### 組件測試範例

```typescript
import { mount } from '@vue/test-utils'
import { describe, it, expect, vi } from 'vitest'
import MyComponent from '@/components/MyComponent.vue'
import { createI18n } from 'vue-i18n'

const i18n = createI18n({ locale: 'en', messages: { en: { hello: 'Hello' } } })

describe('MyComponent', () => {
  it('renders with props', () => {
    const wrapper = mount(MyComponent, {
      props: { msg: 'test' },
      global: { plugins: [i18n] }
    })
    expect(wrapper.text()).toContain('test')
  })

  it('emits event', async () => {
    const wrapper = mount(MyComponent)
    await wrapper.find('button').trigger('click')
    expect(wrapper.emitted('myEvent')).toBeTruthy()
  })
})
```

#### API/mock 測試範例

```typescript
import apiRequest from '@/utils/apiRequest'
import { myApi } from '@/api/myApi'
import { describe, it, expect, vi } from 'vitest'

vi.mock('@/utils/apiRequest', () => ({
  default: { get: vi.fn() }
}))

describe('myApi', () => {
  it('success', async () => {
    vi.mocked(apiRequest.get).mockResolvedValue({ data: { code: 0, data: [1, 2, 3] } })
    const result = await myApi()
    expect(result).toEqual([1, 2, 3])
  })
  it('error', async () => {
    vi.mocked(apiRequest.get).mockRejectedValue(new Error('fail'))
    await expect(myApi()).rejects.toThrow('fail')
  })
})
```

#### Pinia/store mock 範例

```typescript
import { setActivePinia, createPinia } from 'pinia'
beforeEach(() => {
  setActivePinia(createPinia())
})
```

#### i18n mock 範例

```typescript
vi.mock('vue-i18n', () => ({
  useI18n: () => ({ t: (key: string) => key })
}))
```

#### 全域 plugin/router mock

```typescript
import { createRouter, createWebHistory } from 'vue-router'
const router = createRouter({ history: createWebHistory(), routes: [] })
const wrapper = mount(MyComponent, { global: { plugins: [router] } })
```

#### 斷言/spy/emit/異步

```typescript
const spy = vi.spyOn(window, 'open').mockImplementation(vi.fn())
expect(wrapper.emitted('update:modelValue')).toEqual([[true]])
await nextTick()
spy.mockRestore()
```

## Git 提交規範

### Commit Message 格式

```
<type>(<scope>): <subject>

<body>

<footer>
```

- type: feat, fix, docs, style, refactor, test, chore
- scope: 影響範圍
- subject: 簡短描述
- body: 詳細描述
- footer: Breaking changes 或 Closes issues

## 效能優化規範

### 元件優化

- 使用 computed 代替複雜的模板運算
- 大型列表使用虛擬滾動
- 使用 v-show 代替頻繁切換的 v-if
- 適當使用 keep-alive 快取元件

### 資源優化

- 圖片使用適當格式和大小
- 使用 lazy loading 延遲載入
- 設定適當的快取策略
- 使用 code splitting 分割程式碼
