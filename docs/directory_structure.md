## 目錄結構規範

```
PROJECT_ROOT
├── public/          # 公開的靜態資源
├── src/
│   ├── api/         # API 請求相關
│   ├── assets/      # 靜態資源
│   ├── components/  # 共用元件
│   ├── composables/ # 組合式函數
│   ├── config/      # 配置文件
│   ├── language/    # 多語系文件
│   ├── layout/      # 布局元件
│   ├── router/      # 路由配置
│   ├── stores/      # 狀態管理
│   ├── styles/      # 樣式文件
│   ├── types/       # TypeScript 型別定義
│   ├── utils/       # 工具函數
│   └── views/       # 頁面元件
└── public/          # 公開的靜態資源
```
