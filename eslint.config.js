import globals from 'globals'
import pluginJs from '@eslint/js'
import tseslint from 'typescript-eslint'
import pluginVue from 'eslint-plugin-vue'
import { readFile } from 'node:fs/promises'
import eslint<PERSON>onfig<PERSON>rettier from 'eslint-config-prettier'

const autoImport = JSON.parse(
  await readFile(new URL('./.eslintrc-auto-import.json', import.meta.url))
)

export default [
  {
    files: ['**/*.{js,mjs,cjs,ts,mts,jsx,tsx}']
  },
  {
    files: ['**/*.js'],
    languageOptions: {
      sourceType: 'commonjs'
    }
  },
  {
    files: ['*.vue', '**/*.vue'],
    languageOptions: {
      parserOptions: {
        parser: tseslint.parser
      }
    }
  },
  {
    languageOptions: {
      globals: { ...globals.browser, ...globals.node, ...autoImport.globals }
    }
  },
  {
    ignores: ['**/node_modules']
  },
  pluginJs.configs.recommended,
  ...tseslint.configs.recommended,
  ...pluginVue.configs['flat/strongly-recommended'],
  eslintConfigPrettier,
  {
    rules: {
      'vue/multi-word-component-names': 'off',
      '@typescript-eslint/no-explicit-any': 'off'
    }
  }
]
