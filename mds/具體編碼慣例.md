# 5. 具體編碼慣例

本文檔列出了專案中更具體的編碼慣例和約定，旨在補充通用的程式碼風格指南。

## 5.1. Vue 組件特定慣例

*   **組件檔案命名**:
    *   使用 **PascalCase** (大駝峰命名法)。
    *   例如: `UserProfile.vue`, `BaseButton.vue`。
    *   對於僅在特定父組件內部使用的子組件，可以在父組件名稱前加上前綴，例如 `ArticleItem.vue` 在 `ArticleList.vue` 內部使用。
*   **組件 `name` 選項 (在 `<script setup>` 中通常不需要手動設置，Vue 會自動推斷)**:
    *   如果手動設置 (例如在 Options API 或需要特定名稱用於 Vue Devtools 時)，應與檔案名保持一致 (PascalCase)。
    *   例如: `name: 'UserProfile'`。
*   **Props 命名**:
    *   在 `<script setup>` (JavaScript/TypeScript) 中定義時使用 **camelCase**。
        ```typescript
        // <script setup lang="ts">
        defineProps<{ userId: string }>();
        ```
    *   在模板中使用時，對應的 attribute 使用 **kebab-case**。
        ```html
        <!-- <template> -->
        <UserProfile user-id="123" />
        ```
*   **事件命名 (Emitted Events)**:
    *   觸發事件 (`emit`) 時使用 **kebab-case**。
        ```typescript
        // <script setup lang="ts">
        const emit = defineEmits(['update:modelValue', 'form-submit']);
        emit('form-submit', payload);
        ```
    *   在父組件中監聽事件時，也使用 **kebab-case**。
        ```html
        <MyForm @form-submit="handleSubmit" />
        ```
    *   對於 `v-model` 的更新事件，固定使用 `update:modelValue` (或 `update:customPropName` for `v-model:customPropName`)。
*   **組件導入順序 (建議)**:
    1.  外部庫導入 (e.g., `import { ref } from 'vue'`)
    2.  內部工具/服務/Store 導入 (e.g., `import { useUserStore } from '@/store/user'`)
    3.  子組件導入 (e.g., `import UserAvatar from './UserAvatar.vue'`)
    4.  類型導入 (e.g., `import type { User } from '@/types'`)
*   **`<template>`, `<script>`, `<style>` 標籤順序**:
    *   推薦順序: `<script setup>`, `<template>`, `<style>`。
        ```vue
        <script setup lang="ts">
        // ... logic
        </script>

        <template>
          <!-- ... markup -->
        </template>

        <style scoped>
        /* ... styles */
        </style>
        ```
    *   或者 `<template>`, `<script setup>`, `<style>` 也是常見的。團隊應統一。
*   **`v-for` 中的 `key` 使用**:
    *   **必須**為 `v-for` 的每個元素提供唯一的 `key`。
    *   避免使用 `index` 作為 `key`，除非列表是靜態的且不會重新排序或過濾。優先使用數據項中唯一的 ID。
*   **`v-if` vs `v-show`**:
    *   `v-if`: 條件性渲染，有較高的切換開銷。適用於運行時條件很少改變的場景。
    *   `v-show`: 條件性顯示 (基於 CSS `display`)，有較高的初始渲染開銷。適用於需要頻繁切換的場景。
*   **組件屬性順序 (在 `<script setup>` 中通過 `defineProps`, `defineEmits` 等定義，順序較為自然)**:
    *   若使用 Options API，建議順序: `name`, `components`, `props`, `emits`, `data`, `computed`, `watch`, `methods`, 生命周期鉤子。
*   **樣式作用域 (Scoped Styles)**:
    *   默認情況下，組件的 `<style>` 標籤應添加 `scoped` 屬性，以避免樣式洩漏到全局或其他組件。
    *   對於需要穿透到子組件樣式的場景，謹慎使用 `:deep()` 或 `::v-deep`。

## 5.2. API 請求封裝

*   **請求庫**: [例如 Axios]
*   **統一封裝**: 在 `src/services/` 或 `src/api/` 目錄下創建 API 服務模組。
    *   例如: `userService.ts`, `productService.ts`。
    *   每個服務模組封裝相關的 API 請求函數。
*   **請求攔截與響應攔截**:
    *   **請求攔截器**: 用於統一添加認證 Token (e.g., JWT)、設置 `Content-Type` 等。
    *   **響應攔截器**: 用於統一處理 API 錯誤 (e.g., 401 未授權跳轉登錄頁，500 服務器錯誤提示)、數據轉換等。
*   **基地址 (Base URL)**: API 的基地址應通過環境變數配置。

## 5.3. 環境變數管理

*   使用 `.env` 文件系列管理環境變數 (e.g., `.env`, `.env.development`, `.env.production`)。
*   Vite/Vue CLI 會自動加載這些文件。變數必須以 `VITE_` (for Vite) 或 `VUE_APP_` (for Vue CLI) 開頭才能在客戶端程式碼中訪問。
*   敏感信息不應提交到版本控制系統。使用 `.env.local` 或類似文件，並將其加入 `.gitignore`。

## 5.4. 國際化 (i18n - if applicable)

*   **庫選擇**: [例如 `vue-i18n@next`]
*   **翻譯文件結構**: 在 `src/locales/` 目錄下按語言組織翻譯文件 (e.g., `en.json`, `zh-CN.json`)。
*   **Key 命名**: 採用一致的 Key 命名策略 (e.g., `page.section.label`)。

## 5.5. Git 工作流程

*   **分支策略**:
    *   [請填寫：例如 Gitflow (main, develop, feature/xxx, release/xxx, hotfix/xxx) 或 GitHub Flow (main, feature/xxx)]。
    *   `main` (或 `master`) 分支應始終是可部署的穩定版本。
    *   `develop` 分支作為日常開發的集成主線。
    *   新功能或錯誤修復應在單獨的 `feature/` 或 `fix/` 分支上進行。
*   **Commit Message 規範**:
    *   **推薦使用 Conventional Commits 規範**: `<type>(<scope>): <subject>`
        *   `type`: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`, `perf` 等。
        *   `scope`: 可選，表示影響範圍 (e.g., `auth`, `user-profile`)。
        *   `subject`: 簡潔描述。
    *   例如: `feat(auth): add password reset functionality`
    *   `fix: correct minor typos in docs`
*   **Code Review 流程**:
    *   所有程式碼合併到 `develop` 或 `main` 分支前，必須經過至少一位其他團隊成員的 Code Review。
    *   使用 Pull Requests (PRs) / Merge Requests (MRs) 進行 Code Review。
    *   Reviewer 關注點：程式碼風格、邏輯正確性、可讀性、可維護性、測試覆蓋等。

## 5.6. 依賴管理

*   **`package.json`**:
    *   `dependencies`: 應用運行時必需的依賴。
    *   `devDependencies`: 開發和建構過程中需要的依賴 (e.g., Vite, ESLint, testing libraries)。
*   **版本鎖定**:
    *   使用 `package-lock.json` (npm) 或 `yarn.lock` (Yarn) 或 `pnpm-lock.yaml` (pnpm) 來鎖定依賴版本，確保團隊成員和部署環境使用一致的依賴版本。
    *   定期更新依賴，並測試兼容性。

## 5.7. 其他約定

*   [請根據專案需要補充其他特定約定，例如：]
*   **第三方庫的引入和封裝策略**
*   **性能優化注意事項** (e.g., 圖片優化、代碼分割、虛擬滾動等)
*   **可訪問性 (a11y) 指南**