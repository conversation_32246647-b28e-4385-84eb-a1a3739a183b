# 3. 測試碼寫法風格

本文檔概述了專案中測試碼的編寫規範和策略，旨在確保程式碼的品質和穩定性。

## 3.1. 測試框架與工具

*   **單元測試 (Unit Testing)**:
    *   **框架**: Vitest / Jest [請根據專案實際情況選擇]
    *   **輔助庫**: [若有，例如 `sinon` for spies/stubs/mocks]
*   **組件測試 (Component Testing)**:
    *   **框架**: Vitest / Jest 配合 Vue Test Utils (`@vue/test-utils`)
*   **端到端測試 (E2E Testing)**:
    *   **框架**: Cypress / Playwright [請根據專案實際情況選擇]
*   **測試覆蓋率工具**:
    *   Vitest/Jest 內建覆蓋率報告 (e.g., Istanbul)

## 3.2. 測試覆蓋率目標

*   [請填寫：例如，整體覆蓋率 > 80%，核心模組 > 90%]
*   持續監控測試覆蓋率，並在新功能開發和程式碼重構時保持或提升覆蓋率。

## 3.3. 單元測試 (Unit Tests)

*   **測試目標**:
    *   測試純函數、工具函數 (utils)、可組合函數 (composables) 的邏輯。
    *   測試 Pinia stores 中的 getters 和 actions (不涉及 UI)。
*   **編寫原則**:
    *   **獨立性**: 測試案例應相互獨立，不應有執行順序依賴。
    *   **原子性**: 每個測試案例只驗證一個具體的點。
    *   **可重複性**: 測試結果在任何環境下都應一致。
    *   **快速執行**: 單元測試應能快速執行完畢。
*   **斷言庫**: 使用測試框架內建的斷言 (e.g., Vitest/Jest's `expect`).
*   **Mocking/Stubbing**:
    *   對於外部依賴 (如 API 請求、全局對象)，使用 mock 或 stub 進行隔離。
    *   Vitest/Jest 提供了 `vi.mock`, `vi.fn`, `vi.spyOn` 等 mock 功能。
*   **測試檔案命名與結構**:
    *   測試檔案通常與被測試的源文件同級，並以 `.spec.ts` 或 `.test.ts` (或 `.js`) 結尾 (e.g., `utils/math.ts` -> `utils/math.spec.ts`)。
    *   使用 `describe` 組織相關的測試案例，使用 `it` 或 `test` 定義單個測試案例。

## 3.4. 組件測試 (Component Tests)

*   **測試目標**:
    *   **渲染輸出**: 驗證組件是否按預期渲染 (DOM 結構、文本內容)。
    *   **Props 交互**: 測試不同 props 輸入下組件的行為和渲染。
    *   **事件觸發**: 驗證組件是否能正確觸發事件及其 payload。
    *   **Slots 內容**: 測試插槽內容是否正確渲染。
    *   **用戶交互**: 模擬用戶操作 (點擊、輸入等) 並驗證組件響應。
    *   **生命週期和響應式**: 驗證組件在生命週期鉤子和響應式數據變化時的行為。
*   **Vue Test Utils API 使用**:
    *   使用 `mount` 或 `shallowMount` 掛載組件。
    *   使用 `wrapper.find()`, `wrapper.props()`, `wrapper.emitted()`, `wrapper.trigger()` 等 API 進行交互和斷言。
*   **快照測試 (Snapshot Testing)**:
    *   謹慎使用。適用於驗證大型組件結構或 UI 不經常變動的部分。
    *   快照應作為輔助，不應替代具體的行為斷言。

## 3.5. 端到端測試 (E2E Tests)

*   **測試場景**:
    *   測試關鍵的用戶流程 (User Flows)，例如用戶註冊、登錄、購物流程、表單提交等。
    *   從用戶視角驗證整個應用的集成功能。
*   **Page Object Model (POM)**:
    *   推薦使用 POM 模式組織 E2E 測試，提高可維護性和可讀性。
*   **選擇器策略**:
    *   優先使用穩定的選擇器，如 `data-testid` 屬性，避免使用易變的 CSS class 或 DOM 結構。
*   **異步處理**:
    *   E2E 測試中需要妥善處理異步操作和頁面加載等待。

## 3.6. 測試運行

*   **本地運行**: 開發者應在本地頻繁運行測試，尤其是在提交程式碼前。
*   **CI/CD 集成**:
    *   將測試（單元、組件、E2E）集成到 CI/CD 流水線中。
    *   測試失敗應阻止程式碼合併或部署。