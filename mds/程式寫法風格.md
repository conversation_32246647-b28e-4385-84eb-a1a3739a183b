# 2. 程式寫法風格

本文檔詳細說明在 Vue 3 專案中應遵循的程式寫法風格和最佳實踐。

## 2.1. Vue 3 特性使用

*   **Composition API**:
    *   **優先使用 Composition API** 配合 `<script setup>` 進行組件開發，以獲得更好的邏輯組織、可重用性和 TypeScript 支持。
    *   Options API 僅在維護舊有程式碼或特定簡單場景下考慮使用。
*   **`<script setup>`**:
    *   所有新組件**強制使用 `<script setup>`** 語法糖，以簡化代碼並提高開發效率。
*   **響應式系統 (Reactivity System)**:
    *   `ref`: 用於定義基本類型和單一對象的響應式數據。
    *   `reactive`: 用於定義複雜對象（Object, Array）的響應式數據。
    *   `computed`: 用於派生響應式數據，避免在模板中進行複雜計算。確保 `computed` 屬性是純函數且無副作用。
    *   `watch` / `watchEffect`:
        *   `watch`: 用於觀察特定數據源的變化並執行副作用。明確指定依賴。
        *   `watchEffect`: 用於自動追蹤其回調函數中使用的響應式依賴，並在依賴變化時重新執行。適用於副作用與多個依賴相關的場景。
        *   注意清理副作用（例如，在 `onUnmounted` 中清除定時器或事件監聽器）。
*   **生命週期鉤子 (Lifecycle Hooks)**:
    *   在 `<script setup>` 中使用 `onMounted`, `onUpdated`, `onUnmounted` 等 Composition API 提供的生命週期鉤子。
    *   鉤子函數應保持簡潔，複雜邏輯應抽取到單獨函數中。

## 2.2. 組件設計 (Component Design)

*   **組件拆分原則**:
    *   **單一職責原則 (Single Responsibility Principle)**: 每個組件只做一件事情。
    *   **可重用性 (Reusability)**: 盡可能設計通用、可配置的組件。
    *   **高內聚，低耦合**: 組件內部邏輯緊密相關，組件間依賴盡可能少。
    *   考慮將大型組件拆分為更小的、易於管理的子組件。
*   **Props**:
    *   使用 `defineProps` 進行定義。
    *   **明確類型**: 為所有 props 提供明確的類型定義（TypeScript 或 `type` 選項）。
    *   **必要性**: 使用 `required: true` 標記必需的 props。
    *   **預設值**: 為非必需的 props 提供合理的 `default` 值。
    *   **校驗器 (Validator)**: 對複雜的 props 使用 `validator` 函數進行校驗。
    *   Props 命名：在 JavaScript/TypeScript 中使用 camelCase，在模板中使用 kebab-case。
*   **Events**:
    *   使用 `defineEmits` 進行定義，並聲明事件名稱及可選的 payload 類型。
    *   事件命名：使用 kebab-case 觸發 (`emit('my-event')`)，在父組件監聽時也建議使用 kebab-case (`@my-event="handler"`)。
    *   事件處理函數命名：通常以 `handle` 或 `on` 開頭，例如 `handleUpdateValue` 或 `onItemClick`。
*   **Slots**:
    *   合理使用默認插槽和具名插槽 (`<slot name="header"></slot>`) 來增強組件的靈活性和內容分發能力。
    *   作用域插槽 (`<slot :item="data"></slot>`) 用於向父組件傳遞數據。
*   **Provide/Inject**:
    *   謹慎使用 `provide` 和 `inject`，主要用於深層次組件間的數據傳遞，避免過度使用導致數據流混亂。
    *   優先考慮 Props 和 Events 進行父子組件通信。
    *   為 `provide` 的值使用 `Symbol` 作為 key，以避免命名衝突。

## 2.3. 狀態管理 (State Management - e.g., Pinia)

*   **Store 結構**:
    *   按功能模組劃分 Store (e.g., `userStore.ts`, `productStore.ts`)。
    *   每個 Store 包含 `state`, `getters`, `actions`。
*   **State**:
    *   定義為返回初始狀態的函數：`state: () => ({ count: 0 })`。
    *   State 應盡可能扁平化。
*   **Getters**:
    *   類似於組件的 `computed` 屬性，用於派生狀態。
    *   Getter 函數接收 `state` 作為第一個參數。
*   **Actions**:
    *   用於處理異步操作或批量的同步操作來變更 state。
    *   Action 可以是異步的 (`async/await`)。
    *   通過 `this` 訪問 Store 實例。

## 2.4. 路由 (Routing - e.g., Vue Router)

*   **路由配置**:
    *   集中管理路由配置 (e.g., `router/index.ts`)。
    *   使用具名路由 (`name: 'UserProfile'`) 方便導航和重構。
*   **路由守衛 (Navigation Guards)**:
    *   用於實現權限控制、登錄驗證等。
    *   合理使用全局守衛 (`beforeEach`, `afterEach`)、路由獨享守衛 (`beforeEnter`) 和組件內守衛 (`onBeforeRouteLeave`, `onBeforeRouteUpdate`)。
*   **動態路由與懶加載 (Lazy Loading)**:
    *   對視圖組件使用路由懶加載 (`component: () => import('@/views/AboutView.vue')`) 以優化初始加載性能。
    *   動態路由用於匹配模式相似但內容不同的頁面 (e.g., `/users/:id`)。

## 2.5. 異步操作 (Asynchronous Operations)

*   **`async/await`**:
    *   優先使用 `async/await` 處理異步邏輯，使代碼更易讀和維護。
*   **錯誤處理**:
    *   在 `async` 函數中使用 `try...catch` 捕獲和處理異步錯誤。
    *   對於 API 請求，應有統一的錯誤處理機制（例如，通過 Axios 攔截器）。

## 2.6. TypeScript/JavaScript 使用

*   **TypeScript (若使用)**:
    *   **強類型**: 盡可能為變數、函數參數、返回值等添加明確的類型定義。
    *   **接口 (Interfaces) / 類型別名 (Type Aliases)**: 用於定義複雜數據結構的類型。
    *   利用 TypeScript 的靜態檢查優勢，減少運行時錯誤。
*   **JavaScript (若使用 ES6+)**:
    *   使用 `let` 和 `const` 代替 `var`。
    *   優先使用箭頭函數，除非需要 `this` 指向特定上下文。
    *   使用模塊化 (`import`/`export`)。
    *   善用解構賦值、擴展運算符等現代 JavaScript 特性。