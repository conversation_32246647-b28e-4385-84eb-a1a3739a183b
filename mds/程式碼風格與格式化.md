# 4. 程式碼風格與格式化

本文檔定義了專案的程式碼風格指南、格式化工具和 Linter 配置，以確保程式碼的一致性和可讀性。

## 4.1. 格式化工具 (Formatting Tools)

*   **Prettier**:
    *   **強制使用 Prettier** 進行程式碼自動格式化。
    *   專案根目錄下應包含 `.prettierrc.js` (或 `.json`, `.yaml`) 配置文件。
    *   **配置示例 (可根據團隊偏好調整)**:
        ```json
        {
          "singleQuote": true,
          "semi": true,
          "trailingComma": "es5",
          "printWidth": 100,
          "tabWidth": 2,
          "vueIndentScriptAndStyle": true
        }
        ```
    *   建議在 IDE 中配置保存時自動格式化，並在 CI 流程中加入格式檢查。
*   **EditorConfig**:
    *   使用 `.editorconfig` 文件來幫助在不同的編輯器和 IDE 之間保持一致的編碼風格（如縮進風格、行尾符等）。
    *   **配置示例**:
        ```ini
        root = true

        [*]
        indent_style = space
        indent_size = 2
        end_of_line = lf
        charset = utf-8
        trim_trailing_whitespace = true
        insert_final_newline = true

        [*.md]
        trim_trailing_whitespace = false
        ```

## 4.2. Linter 工具 (Linting Tools)

*   **ESLint**:
    *   **強制使用 ESLint** 進行 JavaScript/TypeScript 程式碼質量檢查。
    *   專案根目錄下應包含 `.eslintrc.js` (或 `.json`, `.yaml`) 配置文件。
    *   **推薦配置**:
        *   `eslint:recommended`
        *   `plugin:vue/vue3-recommended` (for Vue 3 specific rules)
        *   `@typescript-eslint/eslint-plugin` 和 `@typescript-eslint/parser` (if using TypeScript)
        *   `eslint-config-prettier` (關閉與 Prettier 衝突的規則)
    *   應修復所有 ESLint 報錯和警告。
*   **Stylelint (若使用)**:
    *   用於 CSS/SCSS/LESS 程式碼的風格檢查和自動修復。
    *   專案根目錄下應包含 `.stylelintrc.js` (或 `.json`, `.yaml`) 配置文件。
    *   推薦使用 `stylelint-config-standard-scss` (for SCSS) 或 `stylelint-config-standard`。

## 4.3. 通用命名慣例 (General Naming Conventions)

*   **變數 (Variables) 和函數名 (Functions)**: 使用 `camelCase`。
    *   例如: `userName`, `calculateTotalPrice()`
*   **常數 (Constants)**: 使用 `UPPER_CASE_SNAKE_CASE` (全大寫，下劃線分隔)。
    *   例如: `MAX_USERS`, `API_BASE_URL`
*   **類 (Classes) 和 TypeScript 接口 (Interfaces) / 類型別名 (Type Aliases)**: 使用 `PascalCase`。
    *   例如: `UserService`, `interface UserProfile`, `type ProductId`
*   **檔案命名**:
    *   JavaScript/TypeScript 檔案: `camelCase.js` 或 `kebab-case.ts` (根據團隊約定，Vue 組件除外)。
    *   Vue 組件檔案: `PascalCase.vue` (詳見 `05_coding_conventions.md`)。
    *   CSS/SCSS 檔案: `kebab-case.scss`。
*   **CSS 類名 (CSS Classes)**:
    *   推薦使用 `kebab-case` (例如: `user-profile__avatar`)。
    *   可考慮 BEM (Block Element Modifier) 命名法，但需團隊達成一致。

## 4.4. 註解 (Comments)

*   **何時寫註解**:
    *   對於複雜的邏輯、演算法或業務規則。
    *   對於不直觀的程式碼。
    *   公開的 API (函數、類、模組) 應有 JSDoc/TSDoc 風格的註解。
    *   `// TODO:` 或 `// FIXME:` 用於標記待辦事項或需要修復的問題，並簡要說明。
*   **註解風格**:
    *   單行註解: `// comment`
    *   多行註解: `/* ... */`
    *   JSDoc/TSDoc: 用於函數、類、接口等，提供參數、返回值、描述等信息。
        ```typescript
        /**
         * Calculates the sum of two numbers.
         * @param a - The first number.
         * @param b - The second number.
         * @returns The sum of a and b.
         */
        function sum(a: number, b: number): number {
          return a + b;
        }
        ```
*   **避免不必要的註解**: 對於簡單、自解釋的程式碼，無需添加註解。程式碼本身應盡可能清晰。

## 4.5. 文件結構 (File Structure - General)

*   **`src/` 目錄結構 (推薦)**:
    ```
    src/
    ├── assets/         # 靜態資源 (圖片, 字體等)
    ├── components/     # 全局/共享組件
    │   ├── common/     # 非常通用的基礎組件 (Button, Input, Modal)
    │   └── layout/     # 佈局相關組件 (Header, Footer, Sidebar)
    ├── composables/    # Vue Composition API 可組合函數
    ├── constants/      # 常量定義
    ├── layouts/        # 頁面佈局組件 (e.g., DefaultLayout.vue, AuthLayout.vue)
    ├── locales/        # i18n 語言包 (若有)
    ├── plugins/        # Vue 插件
    ├── router/         # Vue Router 配置
    │   └── index.ts
    ├── services/       # API 請求服務封裝
    ├── store/          # Pinia/Vuex 狀態管理
    │   ├── index.ts
    │   └── modules/
    ├── styles/         # 全局樣式, SCSS 變數, mixins
    │   ├── _variables.scss
    │   ├── _mixins.scss
    │   └── main.scss
    ├── types/          # TypeScript 類型定義
    │   └── index.d.ts
    ├── utils/          # 工具函數
    ├── views/          # 頁面級組件 (路由對應的組件)
    │   ├── HomeView.vue
    │   └── AboutView.vue
    ├── App.vue         # 根組件
    └── main.ts         # 應用入口文件
    ```
    *   此結構僅為建議，可根據專案規模和團隊習慣進行調整。
    *   按功能模組 (feature-based) 組織也是一種常見且推薦的方式，尤其對於大型專案。