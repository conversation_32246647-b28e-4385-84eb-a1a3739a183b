# Product Context

## Purpose

- Provide a modern, maintainable frontend for a gaming/betting platform (客端界面)
- Enable rapid feature development and theme/language customization

## Problems Solved

- Outdated or inconsistent UI/UX across platforms
- Difficulties in scaling and maintaining legacy code
- Inefficient state management and API integration
- Lack of clear documentation and testing standards

## User Experience Goals

- Seamless, responsive experience on both mobile and PC
- Fast load times and smooth navigation
- Consistent look and feel across themes and languages
- Easy access to core features (betting, account, news, etc.)
