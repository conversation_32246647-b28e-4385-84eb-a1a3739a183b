# Project Brief

## Core Requirements

- Frontend project for a client interface (客端界面)
- Built with Nuxt 3, Vue 3, TypeScript, Pinia, Tailwind, Element Plus, VueUse
- Modular structure: components, composables, API, stores, layouts, etc.
- Follows strict naming and code style conventions (see development guide)
- Supports multi-language and theme customization
- Uses file-based routing and auto-imports
- Optimized for performance, mobile-first, and SEO

## Goals

- Deliver a maintainable, scalable, and high-performance frontend
- Provide a clean, modern UI/UX for multiple platforms (mobile/PC)
- Ensure robust state management and API integration
- Maintain comprehensive documentation and testing
