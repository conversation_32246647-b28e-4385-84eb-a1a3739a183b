# System Patterns

## Architecture

- Nuxt 3 (Vue 3, TypeScript) SPA/SSR
- Modular: components, composables, API, stores, layouts, themes
- File-based routing, auto-imports, Pinia for state

## Key Technical Decisions

- Use of composition API and <script setup>
- Tailwind for utility-first styling
- Shadcn Vue & Radix Vue for UI components
- VueUse for composables
- Strict naming and code style conventions

## Design Patterns

- Modular, reusable components
- Separation of concerns: API, state, UI, logic
- Mobile-first, responsive design
- Centralized API request handling
- Theming and localization via directory structure
