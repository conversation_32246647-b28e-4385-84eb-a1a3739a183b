# Tech Context

## Technologies Used

- Nuxt 3, Vue 3, TypeScript
- Pi<PERSON>, Tailwind CSS, Element Plus, VueUse
- Vitest, @vue/test-utils for testing

## Development Setup

- File-based routing (pages/)
- Auto-imports for components/composables
- Modular src/ structure
- Multi-language and theme support

## Technical Constraints

- Must follow strict naming/code style (see development guide)
- Mobile-first, SEO-optimized
- Maintainable and scalable codebase

## Dependencies

- See package.json for full list
