{"name": "bbgp-frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "lint": "eslint .", "format": "prettier --write src/", "test": "vitest run -c vite.config.js", "coverage": "vitest run -c vite.config.js --coverage"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@icon-park/vue-next": "^1.4.2", "@pinia/testing": "^0.1.7", "axios": "^1.7.2", "dayjs": "^1.11.13", "element-plus": "^2.9.0", "gsap": "^3.12.7", "pinia": "^2.1.7", "vue": "^3.4.29", "vue-i18n": "^10.0.0-beta.1", "vue-router": "^4.3.3", "vue3-cookies": "^1.0.6", "vue3-spinners": "^1.2.2"}, "devDependencies": {"@eslint/js": "^9.11.1", "@iconify-json/ep": "^1.1.15", "@tsconfig/node20": "^20.1.4", "@types/node": "^20.14.5", "@vitejs/plugin-vue": "^5.0.5", "@vitest/coverage-v8": "^2.1.5", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.5.1", "eslint": "^9.11.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-vue": "^9.28.0", "globals": "^15.10.0", "jsdom": "^25.0.1", "npm-run-all2": "^6.2.0", "prettier": "^3.3.3", "sass": "^1.77.6", "typescript": "~5.4.0", "typescript-eslint": "^8.8.0", "unplugin-auto-import": "^0.17.6", "unplugin-icons": "^0.19.0", "unplugin-vue-components": "^0.27.2", "vite": "^5.3.1", "vite-plugin-html": "^3.2.2", "vue-tsc": "^2.0.21"}}