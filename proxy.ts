import { loadEnv } from 'vite'

const env = loadEnv(process.env.NODE_ENV || 'development', process.cwd(), '')

const convertCookies = (cookies: Record<string, string>) => {
  return Object.entries(cookies)
    .map(([key, value]) => `${key}=${value}`)
    .join('; ') + ';';
};

const cookies = {
  SESSION_ID: env.VITE_PROXY_SESSION_ID || 'guest',
  BBSESSID: env.VITE_PROXY_SESSION_ID || 'guest',
  LOGINCHK: env.VITE_PROXY_LOGINCHK || 'N',
  tpl: env.VITE_PROXY_TPL || 'bbinbgp',
  lang: env.VITE_PROXY_LANG || 'zh-cn',
  langx: env.VITE_PROXY_LANGX || 'big5'
};

const headers = {
  Cookie: convertCookies(cookies)
};

const createProxyConfig = (target: string, additionalHeaders: Record<string, string> = {}) => ({
  target,
  changeOrigin: true,
  headers: { ...headers, ...additionalHeaders }
});

const createWebsocketProxyConfig = (target: string, additionalHeaders: Record<string, string> = {}) => ({
  target,
  changeOrigin: true,
  headers: { ...headers, ...additionalHeaders },
  ws: true
});

const createLobbyProxyConfig = (target: string, additionalHeaders: Record<string, string> = {}) => ({
  target,
  changeOrigin: true,
  headers: { ...headers, ...additionalHeaders },
  configure: (proxy: any) => {
    proxy.on('proxyReq', (proxyReq: any) => {
        const tempBase = 'http://localhost'
        const url = new URL(proxyReq.path, tempBase)
        const params = url.searchParams

        params.set('domain_url', target)
    })
  }
});

const targetUrl = env.VITE_PROXY_HOST || 'http://777.vir777.net/';

const proxySetting = {
  '/api/sport/lobby_link': createLobbyProxyConfig(targetUrl),
  '/api/live/lobby_link': createLobbyProxyConfig(targetUrl),
  '/api/lottery/lobby_link': createLobbyProxyConfig(targetUrl),
  '/api': createProxyConfig(targetUrl, { SITE_SERVER_ALIAS: 'bbinbgp' }),
  '/fxGM': createWebsocketProxyConfig(targetUrl)
};

export { proxySetting }; 