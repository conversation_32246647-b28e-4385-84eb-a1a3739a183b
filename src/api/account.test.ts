import apiRequest from '@/utils/apiRequest'
import { describe, it, expect, vi } from 'vitest'
import { getAccountInfo } from './account'

vi.mock('@/utils/apiRequest', () => {
  return {
    default: {
      get: vi.fn(),
    }
  }
})

describe('getAccountInfo', () => {
  it('should return transformed data when request is successful', async () => {
    const mockData = {
      code: 0,
      message: '',
      data: {
        alias: 'winnie',
        balance: 1002613.5,
        bankrupt: false,
        block: false,
        currency: 'CNY',
        enable: true,
        test: false,
        username: 'winnie'
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })

    const result = await getAccountInfo({ hall_id: 123, session_id: '456' })

    expect(result).toEqual({
      alias: 'winnie',
      balance: 1002613.5,
      bankrupt: false,
      block: false,
      currency: 'CNY',
      enable: true,
      test: false,
      username: 'winnie',
      isLogin: true
    })
  })

  it('should reject when request fails', async () => {
    vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))

    await expect(getAccountInfo({ hall_id: 123, session_id: '456' })).rejects.toThrow('Network Error')
  })
})
