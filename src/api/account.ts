import apiRequest from '@/utils/apiRequest'
import type { IAccountInfo, IGetAccountInfoParams } from '@/types/account'

export const getAccountInfo = async(params: IGetAccountInfoParams) => {
  try {
    const res = await apiRequest.get('/api/account/info', { params })
    const resData = res.data
    return { 
      ...resData.data,
      isLogin: true
    } as IAccountInfo
  } catch (error: any) {
    return Promise.reject(error)
  }
}
