import { describe, it, expect, vi } from 'vitest'
import apiRequest from '@/utils/apiRequest'
import { getBBfishingList, getBBfishingIntro } from '@/api/bbfish'

vi.mock('@/utils/apiRequest', () => {
  return {
    default: {
      get: vi.fn(),
    }
  }
})

vi.mock('@/utils/apiUrl', () => ({
  useApiUrl: vi.fn(() => ref('')),
}));

describe('getBBfishingList', () => {
  it('should return data when request is successful', async () => {
    const mockData = {
      data: {
        game_list: [
          {
              game_id: 1,
              name: 'Test Game',
              icon: 'icon.png',
              link: 'link.com',
              game_kind: 38,
              rule_link: 'rule.com',
          }
        ]
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })

    const result = await getBBfishingList({is_mobile: true, lang: 'en', hall_id: 0, session_id: 'session_id' })

    expect(result).toEqual([
      {
        gameId: 1,
        name: 'Test Game',
        icon: 'icon.png',
        link: 'link.com',
        gameKind: 38,
        ruleLink: 'rule.com',
        nickname: 'BB',
      }
    ])
  })

  it('should reject when request fails', async () => {
    vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))

    await expect(getBBfishingList({ is_mobile: true, lang: 'en', hall_id: 0, session_id: 'session_id' })).rejects.toThrow('Network Error')
  })
})

describe('getBBfishingIntro', () => {
  it('should return intro data when request is successful', async () => {
    const mockData = {
      code: 0,
      message: '',
      data: [
        {
          game_id: 38001,
          feature: ['38001_4', '38001_0', '38001_1', '38001_2', '38001_3'],
          ratio: ['38001_0', '38001_1'],
          video: true
        }
      ]
    }
    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })

    const result = await getBBfishingIntro()

    expect(result).toEqual({
      introData: [{
        gameId: 38001,
        feature: ['38001_4', '38001_0', '38001_1', '38001_2', '38001_3'],
        ratio: ['38001_0', '38001_1'],
        video: true
      }]
    })
  })

  it('should reject when request fails', async () => {
    vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))

    await expect(getBBfishingIntro()).rejects.toThrow('Network Error')
  })
})
