import apiRequest from '@/utils/apiRequest'
import type { 
  IGameInfo,
  IGameIntro,
  IGetGameListParams
} from '@/types/game'

export const getBBfishingList = async(params: IGetGameListParams ) => {
  try {
    const response = await apiRequest.get('/api/fish/game_lobby_menu_link', { params })
    const responseData = response.data

    return responseData.data.game_list.map((item: any) => ({
      gameId: item.game_id,
      name: item.name,
      icon: item.icon,
      link: item.link,
      gameKind: item.game_kind,
      ruleLink: item.rule_link,
      nickname: 'BB',
    })) as IGameInfo[]
  } catch (error: any) {
    return Promise.reject(error)
  }
}

export const getBBfishingIntro = async() => {
  try {
    const response = await apiRequest.get(`/api/fish/introduction_images_info`)
    const responseData = response.data
    return {
      introData: responseData.data.map((item: any) => ({
        gameId: item.game_id,
        feature: item.feature,
        ratio: item.ratio,
        video: item.video
      })) as IGameIntro[]
    }
  } catch (error: any) {
    return Promise.reject(error)
  }
}
