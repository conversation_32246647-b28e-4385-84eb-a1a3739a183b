import apiRequest from '@/utils/apiRequest'
import { describe, expect, it, vi } from 'vitest'
import { getLiveBetInfo, getLotteryBetInfo, getSportBetInfo } from './betinfo'

vi.mock('@/utils/request', () => {
  return {
    default: {
      get: vi.fn(),
    }
  }
})

vi.mock('@/utils/apiRequest', () => {
  return {
    default: {
      get: vi.fn(),
    }
  }
})

describe('getLiveBetInfo', () => {
  it('should return live bet info when the response is successful', async () => {
    const mockResponse = {
      data: {
        code: 0,
        message: '',
        data: {
          game_limit_list: [
            {
              name: '百家樂',
              gameplay_limit_list: [
                {
                  limit_name: '單注最低限額',
                  limit_value: 28
                }
              ]
            },
            {
              name: '龍虎鬥',
              gameplay_limit_list: [
                {
                  limit_name: '單注最低限額',
                  limit_value: 28
                }
              ]
            }
          ],
          maintain_info: []
        }
      }
    }

    vi.mocked(apiRequest.get).mockResolvedValue(mockResponse)

    const result = await getLiveBetInfo({
      hall_id: 123,
      session_id: '456',
      lang: 'zh-cn'
    })

    expect(result).toEqual({
      gameLimitList: [{
        name: '百家樂',
        gamePlayLimitList: [{
          limitName: '單注最低限額',
          limitValue: 28
        }]
      }, {
        name: '龍虎鬥',
        gamePlayLimitList: [{
          limitName: '單注最低限額',
          limitValue: 28
        }]
      }],
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should reject with an error when the axios request fails', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)

    await expect(getLiveBetInfo({
      hall_id: 123,
      session_id: '456',
      lang: 'zh-cn'
    })).rejects.toThrow('Network Error')
  })
})

describe('getSportBetInfo', () => {
  it('should return sport bet info when the response is successful', async () => {
    const mockResponse = {
      data: {
        code: 0,
        message: '',
        data: {
          category_list: [
            {
              id: 1,
              name: '足球'
            }
          ],
          game_limit_list: {
            group_name: [
              {
                key: '1',
                value: '讓球'
              }
            ],
            setting: [
              {
                key: '1',
                value: {
                  bet_limit: 1000000,
                  game_limit: 10000000
                }
              }
            ]
          },
          is_maintain: false,
          maintain_info: []
        }
      }
    }

    vi.mocked(apiRequest.get).mockResolvedValue(mockResponse)

    const params = {
      hall_id: 123,
      session_id: '456',
      lang: 'zh-cn',
      category_id: 1
    }
    const result = await getSportBetInfo(params)

    expect(result).toEqual({
      categoryList: [
        {
          id: 1,
          name: '足球'
        }
      ],
      gameLimitList: [{
        groupName: '讓球',
        betLimit: 1000000,
        gameLimit: 10000000
      }],
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should reject with an error when the axios request fails', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)

    const params = {
      hall_id: 123,
      session_id: '456',
      lang: 'zh-cn',
      category_id: 1
    }
    await expect(getSportBetInfo(params)).rejects.toThrow('Network Error')
  })
})

describe('getLotteryBetInfo', () => {
  it('should return lottery bet info when the response is successful', async () => {
    const mockResponse = {
      data: {
        code: 0,
        message: '',
        data: {
          game_list: [
            {
              id: 'HKLT',
              name: '香港六合彩'
            }
          ],
          game_limit_list: [
            {
              title: '特別號',
              single_credit: 4160,
              single_odds: 2080
            }
          ],
          is_maintain: false,
          maintain_info: []
        }
      }
    }
  
    vi.mocked(apiRequest.get).mockResolvedValue(mockResponse)

    const result = await getLotteryBetInfo({
      hall_id: 123,
      session_id: '456',
      lang: 'zh-cn',
      game_id: 'HKLT'
    })

    expect(result).toEqual({
      gameLimitList: [{
        title: '特別號',
        singleCredit: 4160,
        singleOdds: 2080
      }],
      gameList: [{
        id: 'HKLT',
        name: '香港六合彩'
      }],
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should reject with an error when the axios request fails', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)

    await expect(getLotteryBetInfo({
      hall_id: 123,
      session_id: '456',
      lang: 'zh-cn',
      game_id: 'HKLT'
    })).rejects.toThrow('Network Error')
  })
})