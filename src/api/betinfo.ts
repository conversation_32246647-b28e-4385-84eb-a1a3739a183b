import apiRequest from '@/utils/apiRequest'
import type { IGetLiveBetInfoParams, ILiveBetInfo, IGetSportBetInfoParams, ISportBetInfo, IGetLotteryBetInfoParams, ILotteryBetInfo } from '@/types/mcenter'
import { maintainText } from '@/utils/maintaintxt'

// BB LIVE
export const getLiveBetInfo = async (params: IGetLiveBetInfoParams) => {
  try {
    const res = await apiRequest.get('/api/account/live_game_bet_limit', { params })
    const resData = res.data
    let result = undefined

    if (resData.data.is_maintain) {
      result = {
        maintain: true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else {
      result = {
        gameLimitList: resData.data.game_limit_list?.map((item: { name: string, gameplay_limit_list: { limit_name: string, limit_value: number }[]  }) => ({
          name: item.name,
          gamePlayLimitList: item.gameplay_limit_list?.map(subItem => ({
            limitName: subItem.limit_name,
            limitValue: subItem.limit_value
          })) || []
        })) || [],
        maintain: false,
        maintainInfo: ''
      }
    }

    return result as ILiveBetInfo
  } catch (error: any) {
    return Promise.reject(error)
  }
}

// NEW BB SPORTS
export const getSportBetInfo = async (params: IGetSportBetInfoParams) => {
  try {
    const res = await apiRequest.get('/api/account/sport_game_bet_limit', { params })
    const resData = res.data
    let result = undefined

    if (resData.data.is_maintain) {
      result = {
        maintain: true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else {
      const {
        category_list = [],
        game_limit_list = { group_name: [], setting: [] }
      } = resData.data

      const groupNameMap = game_limit_list.group_name.reduce((acc: { [key: string]: string }, item: { key: string, value: string }) => {
        acc[item.key] = item.value
        return acc
      }, {})

      result = {
        categoryList: category_list,
        gameLimitList: game_limit_list.setting.map((item: { key: string, value: { bet_limit: number, game_limit: number } }) => ({
          groupName: groupNameMap[item.key] || '',
          betLimit: item.value.bet_limit,
          gameLimit: item.value.game_limit
        })) || [],
        maintain: false,
        maintainInfo: ''
      }
    }

    return result as ISportBetInfo
  } catch (error: any) {
    return Promise.reject(error)
  }
}

// BB LOTTERY
export const getLotteryBetInfo = async (params: IGetLotteryBetInfoParams) => {
  try {
    const res = await apiRequest.get('/api/account/lottery_game_bet_limit', { params })
    const resData = res.data
    let result = undefined

    if (resData.data.is_maintain) {
      result = {
        maintain: true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else {
      const {
        game_list = [],
        game_limit_list = []
      } = resData.data

      result = {
        gameList: game_list, 
        gameLimitList: game_limit_list?.map((item: { title: string, single_credit: number, single_odds: number }) => ({
          title: item.title,
          singleCredit: item.single_credit,
          singleOdds: item.single_odds
        })),
        maintain: false,
        maintainInfo: ''
      }
    }

    return result as ILotteryBetInfo
  } catch (error: any) {
    return Promise.reject(error)
  }
}
