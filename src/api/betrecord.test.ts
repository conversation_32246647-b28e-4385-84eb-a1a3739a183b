import apiRequest from '@/utils/apiRequest'
import { describe, expect, it, vi } from 'vitest'
import {
  getCardRecord,
  getCasinoGame,
  getCasinoDateRecord,
  getCasinoComboRecord,
  getFishingRecord,
  getLiveRecordByDate,
  getLiveRecordByRoundSerial,
  getLotteryComplete,
  getLotteryCompleteDetail,
  getLotteryUncomplete,
  getLotteryUnCompleteDetail,
  getLotteryCompleteByGame,
  getSportComplete,
  getSportCompleteContent,
  getSportCompleteDetail,
  getSportUncomplete,
  getSportUnCompleteDetail
} from './betrecord'
import { maintainInfo as ImaintainInfo } from '@/types/mcenter'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

dayjs.extend(timezone)
dayjs.extend(utc)

vi.mock('@/utils/request', () => {
  return {
    default: {
      get: vi.fn(),
    }
  }
})

vi.mock('@/utils/apiRequest', () => {
  return {
    default: {
      get: vi.fn(),
    }
  }
})

vi.mock('@/language/i18n', () => ({
  default: {
    global: {
      t: (key: string) => key
    }
  }
}))

vi.mock('@/utils/maintaintxt', () => ({
  maintainText: (maintainInfo: ImaintainInfo) => maintainInfo.message
}))

describe('getLiveRecordByRoundSerial', () => {
  it('should return live record when the response is successful', async () => {
    const mockLiveRecordResponse = {
      data: {
        code: 0,
        message: '',
        data: {
          is_maintain: false,
          search_range: [
            {
              start_date: '2024-11-27',
              end_date: '2024-12-04'
            }
          ],
          wagers_list: [
            {
              id: 520000094787,
              wagers_date: '2024-12-02T04:58:13-04:00',
              game_type: 3001,
              round_serial: 325197364,
              round_no: '5-10',
              table_code: 'EU1',
              bet_amount: '20.00',
              commissionable: '20.00',
              payoff: '-20.00'
            }
          ],
          total: [
            {
              total_bet_amount: '420.00',
              total_commissionable: '420.00',
              total_payoff: '83.20'
            }
          ],
          pagination: [
            {
              current_page: 1,
              page_limit: 5,
              total: 21,
              total_page: 5
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockLiveRecordResponse)

    const result = await getLiveRecordByRoundSerial({
      hall_id: 123,
      session_id: '456',
      roundserial: 123,
      lang: 'zh-cn',
      page: 1,
      scheme: 'https'
    })

    expect(result).toEqual({
      isMaintain: false,
      searchRange: {
        startDate: '2024-11-27',
        endDate: '2024-12-04'
      },
      wagersList: [
        {
          id: 520000094787,
          wagersDate: '2024-12-02 04:58:13',
          gameType: 3001,
          roundSerial: 325197364,
          roundNo: '5-10',
          tableCode: 'EU1',
          betAmount: '20.00',
          commissionable: '20.00',
          payoff: '-20.00'
        }
      ],
      total: {
        totalCount: 21,
        totalBetAmount: '420.00',
        totalCommissionable: '420.00',
        totalPayoff: '83.20'
      },
      pagination: {
        currentPage: 1,
        pageLimit: 5,
        total: 21,
        totalPage: 5
      },
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should return live record when the response is maintain', async () => {
    const mockMaintainLiveRecordResponse = {
      data: {
        code: 561000008,
        message: 'maintain',
        data: {
          is_maintain: true,
          maintain_info: [
            {
              game_kind: 3,
              start_time: '2024-12-04 03:05:00-04:00',
              end_time: '2024-12-04 03:15:00-04:00',
              message: 'text'
            }
          ]
        }
      }
    }

    vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainLiveRecordResponse)

    const result = await getLiveRecordByRoundSerial({
      hall_id: 123,
      session_id: '456',
      roundserial: 123,
      lang: 'zh-cn',
      page: 1,
      scheme: 'https'
    })

    expect(result).toEqual({
      maintain: true,
      maintainInfo: 'text'
    })
  })

  it('should return live record when the response is error', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)

    await expect(() =>
      getLiveRecordByRoundSerial({
        hall_id: 123,
        session_id: '456',
        roundserial: 123,
        lang: 'zh-cn',
        page: 1,
        scheme: 'https'
      })
    ).rejects.toThrowError(mockError.message)
  })
})

describe('getLiveRecordByDate', () => {
  it('should return live record when the response is successful', async () => {
    const mockLiveRecordResponse = {
      data: {
        code: 0,
        message: '',
        data: {
          is_maintain: false,
          search_range: [
            {
              start_date: '2024-11-27',
              end_date: '2024-12-04'
            }
          ],
          wagers_list: [
            {
              id: 520000094787,
              wagers_date: '2024-12-02T04:58:13-04:00',
              game_type: 3001,
              round_serial: 325197364,
              round_no: '5-10',
              table_code: 'EU1',
              bet_amount: '20.00',
              commissionable: '20.00',
              payoff: '-20.00'
            }
          ],
          total: [
            {
              total_bet_amount: '420.00',
              total_commissionable: '420.00',
              total_payoff: '83.20'
            }
          ],
          pagination: [
            {
              current_page: 1,
              page_limit: 5,
              total: 21,
              total_page: 5
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockLiveRecordResponse)

    const result = await getLiveRecordByDate({
      hall_id: 123,
      session_id: '456',
      start_date: '2025-02-20',
      end_date: '2025-02-20',
      lang: 'zh-cn',
      page: 1,
      scheme: 'https'
    })

    expect(result).toEqual({
      isMaintain: false,
      searchRange: {
        startDate: '2024-11-27',
        endDate: '2024-12-04'
      },
      wagersList: [
        {
          id: 520000094787,
          wagersDate: '2024-12-02 04:58:13',
          gameType: 3001,
          roundSerial: 325197364,
          roundNo: '5-10',
          tableCode: 'EU1',
          betAmount: '20.00',
          commissionable: '20.00',
          payoff: '-20.00'
        }
      ],
      total: {
        totalCount: 21,
        totalBetAmount: '420.00',
        totalCommissionable: '420.00',
        totalPayoff: '83.20'
      },
      pagination: {
        currentPage: 1,
        pageLimit: 5,
        total: 21,
        totalPage: 5
      },
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should return live record when the response is maintain', async () => {
    const mockMaintainLiveRecordResponse = {
      data: {
        code: 561000008,
        message: 'maintain',
        data: {
          is_maintain: true,
          maintain_info: [
            {
              game_kind: 3,
              start_time: '2024-12-04 03:05:00-04:00',
              end_time: '2024-12-04 03:15:00-04:00',
              message: 'text'
            }
          ]
        }
      }
    }

    vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainLiveRecordResponse)

    const result = await getLiveRecordByDate({
      hall_id: 123,
      session_id: '456',
      start_date: '2025-02-20',
      end_date: '2025-02-20',
      lang: 'zh-cn',
      page: 1,
      scheme: 'https'
    })

    expect(result).toEqual({
      maintain: true,
      maintainInfo: 'text'
    })
  })

  it('should return live record when the response is error', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)

    await expect(() =>
      getLiveRecordByDate({
        hall_id: 123,
        session_id: '456',
        start_date: '2025-02-20',
        end_date: '2025-02-20',
        lang: 'zh-cn',
        page: 1,
        scheme: 'https'
      })
    ).rejects.toThrowError(mockError.message)
  })
})

describe('getCasinoGame', () => {
  it('should return casino game when the response is successful', async () => {
    const mockCasinoGameResponse = {
      data: {
        code: 0,
        message: '',
        data: {
          search_range: [
            {
              start_date: '2025-04-04',
              end_date: '2025-04-11'
            }
          ],
          game_list: {
            bb_record: [
              {
                id: 5094,
                name: '金瓶梅2'
              },
              {
                id: 5909,
                name: '開心消消樂'
              },
              {
                id: 5251,
                name: '一桿清台'
              }
            ],
            bb_combo_record: [
              {
                id: 5122,
                name: '球球大作戰'
              },
              {
                id: 5143,
                name: '糖果派對3'
              },
              {
                id: 5150,
                name: '寶石傳奇'
              }
            ]
          }
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockCasinoGameResponse)

    const result = await getCasinoGame({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123
    })

    expect(result).toEqual({
      searchRange: {
        startDate: '2025-04-04',
        endDate: '2025-04-11',
      },
      gameList: {
        bbRecord: [
          {
            id: 5094,
            name: '金瓶梅2'
          },
          {
            id: 5909,
            name: '開心消消樂'
          },
          {
            id: 5251,
            name: '一桿清台'
          }
        ],
        bbComboRecord: [
          {
            id: 5122,
            name: '球球大作戰'
          },
          {
            id: 5143,
            name: '糖果派對3'
          },
          {
            id: 5150,
            name: '寶石傳奇'
          }
        ]
      },
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should return casino game when the response is error', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)

    await expect(() => getCasinoGame({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123
    })).rejects.toThrowError(mockError.message)
  })
})

describe('getCasinoComboRecord', () => {
  it('should return casino record when the response is successful', async () => {
    const mockCasinoRecordResponse = {
      data: {
        code: 0,
        message: '',
        data: {
          is_maintain: false,
          search_range: [
            {
              start_date: '2024-12-02',
              end_date: '2024-12-09'
            }
          ],
          wagers_list: [
            {
              id: 5200008107339,
              wagers_date: '2024-12-06T07:17:32-04:00',
              game_type: 5143,
              times: 5
            }
          ],
          pagination: [
            {
              current_page: 1,
              page_limit: 20,
              total: 178,
              total_page: 9
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockCasinoRecordResponse)

    const result = await getCasinoComboRecord({
      hall_id: 123,
      session_id: '456',
      game_id: 1,
      start_date: '2025-03-20',
      end_date: '2025-03-20',
      times: 5,
      page: 1
    })

    expect(result).toEqual({
      isMaintain: false,
      searchRange: {
        startDate: '2024-12-02',
        endDate: '2024-12-09'
      },
      wagersList: [
        {
          id: 5200008107339,
          wagersDate: '2024-12-06T07:17:32-04:00',
          gameType: 5143,
          times: 5
        }
      ],
      pagination: {
        currentPage: 1,
        pageLimit: 20,
        total: 178,
        totalPage: 9
      },
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should return casino record when the response is maintain', async () => {
    const mockMaintainCasinoRecordResponse = {
      data: {
        code: 561000008,
        message: 'maintain',
        data: {
          is_maintain: true,
          maintain_info: [
            {
              game_kind: 5,
              start_time: '2024-12-04 03:05:00-04:00',
              end_time: '2024-12-04 03:15:00-04:00',
              message: 'text'
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainCasinoRecordResponse)

    const result = await getCasinoComboRecord({
      hall_id: 123,
      session_id: '456',
      game_id: 1,
      start_date: '2025-03-20',
      end_date: '2025-03-20',
      times: 5,
      page: 1
    })

    expect(result).toEqual({
      maintain: true,
      maintainInfo: 'text'
    })
  })

  it('should return casino record when the response is error', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)

    await expect(() => getCasinoComboRecord({
      hall_id: 123,
      session_id: '456',
      game_id: 1,
      start_date: '2025-03-20',
      end_date: '2025-03-20',
      times: 5,
      page: 1
    })).rejects.toThrowError(mockError.message)
  })
})

describe('getCasinoDateRecord', () => {
  it('should return casino record when the response is successful', async () => {
    const mockCasinoRecordResponse = {
      data: {
        code: 0,
        message: '',
        data: {
          is_maintain: false,
          search_range: [
            {
              start_date: '2024-12-02',
              end_date: '2024-12-09'
            }
          ],
          wagers_list: [
            {
              id: 5200008107339,
              wagers_date: '2024-12-06T07:17:32-04:00',
              game_type: 5143,
              bet_amount: '5.00',
              payoff: '-5.00',
              jp_amount: '￥ 0.00'
            }
          ],
          total: [
            {
              total_bet_amount: '755.00',
              total_payoff: '833.10',
              total_jp_amount: '￥ 0.00'
            }
          ],
          pagination: [
            {
              current_page: 1,
              page_limit: 20,
              total: 178,
              total_page: 9
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockCasinoRecordResponse)

    const result = await getCasinoDateRecord({
      hall_id: 123,
      session_id: '456',
      game_id: 1,
      start_date: '2025-03-20',
      end_date: '2025-03-20',
      lang: 'zh-cn',
      page: 1,
      scheme: 'https'
    })

    expect(result).toEqual({
      isMaintain: false,
      searchRange: {
        startDate: '2024-12-02',
        endDate: '2024-12-09'
      },
      wagersList: [
        {
          id: 5200008107339,
          wagersDate: '2024-12-06 07:17:32',
          gameType: 5143,
          betAmount: '5.00',
          payoff: '-5.00',
          jpAmount: '￥ 0.00'
        }
      ],
      total: {
        totalCount: 178,
        totalBetAmount: '755.00',
        totalPayoff: '833.10',
        totalJpAmount: '￥ 0.00'
      },
      pagination: {
        currentPage: 1,
        pageLimit: 20,
        total: 178,
        totalPage: 9
      },
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should return casino record when the response is maintain', async () => {
    const mockMaintainCasinoRecordResponse = {
      data: {
        code: 561000008,
        message: 'maintain',
        data: {
          is_maintain: true,
          maintain_info: [
            {
              game_kind: 3,
              start_time: '2024-12-04 03:05:00-04:00',
              end_time: '2024-12-04 03:15:00-04:00',
              message: 'text'
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainCasinoRecordResponse)

    const result = await getCasinoDateRecord({
      hall_id: 123,
      session_id: '456',
      game_id: 1,
      start_date: '2025-03-20',
      end_date: '2025-03-20',
      lang: 'zh-cn',
      page: 1,
      scheme: 'https'
    })

    expect(result).toEqual({
      maintain: true,
      maintainInfo: 'text'
    })
  })

  it('should return casino record when the response is error', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)

    await expect(() => getCasinoDateRecord({
      hall_id: 123,
      session_id: '456',
      game_id: 1,
      start_date: '2025-03-20',
      end_date: '2025-03-20',
      lang: 'zh-cn',
      page: 1,
      scheme: 'https'
    })).rejects.toThrowError(mockError.message)
  })
})

describe('getFishingRecord', () => {
  it('should return fishing record when the response is successful', async () => {
    const mockFishingRecordResponse = {
      data: {
        data: {
          is_maintain: false,
          search_range: [
            {
              start_date: '2025-04-18',
              end_date: '2025-04-25'
            }
          ],
          wagers_list: [
            {
              id: 595701053,
              wagers_date: '2025-04-20T22:12:28-04:00',
              game_type: 38001,
              bet_amount: '22.00',
              payoff: '-14.00',
              wagers_detail_url:
                'https://fisher-test.cc/bet-record/fish/platform/wager/detail?pf=1&token=eyJpdiI6IjZTaWE2UEJYZWVLTFdKY2RxeHRHcVE9PSIsInZhbHVlIjoiOEhUNmprN1ZjUGhtM2dZWHZONFcwbHdtRjUzbHNwakM0TU9aNlRJUktKbVMwTjdTbUV2N2JrN0M0UXpRY1RaNlhJQVVIZnFwckJPVkxqUEJ1dHhIRHc9PSIsIm1hYyI6IjcwZDQ5YWQ2NTE0ZWEyM2ZlZjhmNzk0MDc4YjkwMzFlODcwMDI0MWQyZTg0YzQzZTA3NmY1MGVhM2NmNDRmYWYiLCJ0YWciOiIifQ==&lang=zh-cn'
            }
          ],
          total: [
            {
              total_bet_amount: '82.00',
              total_payoff: '-48.00'
            }
          ],
          pagination: [
            {
              current_page: 1,
              page_limit: 20,
              total: 2,
              total_page: 1
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockFishingRecordResponse)

    const result = await getFishingRecord({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      game_id: 38001,
      start_date: '2025-04-18',
      end_date: '2025-04-18',
      page: 1
    })

    expect(result).toEqual({
      searchRange: {
        startDate: '2025-04-18',
        endDate: '2025-04-25'
      },
      wagersList: [
        {
          id: 595701053,
          wagersDate: '2025-04-20 22:12:28',
          gameType: 38001,
          betAmount: '22.00',
          payoff: '-14.00',
          wagersDetailUrl:
            'https://fisher-test.cc/bet-record/fish/platform/wager/detail?pf=1&token=eyJpdiI6IjZTaWE2UEJYZWVLTFdKY2RxeHRHcVE9PSIsInZhbHVlIjoiOEhUNmprN1ZjUGhtM2dZWHZONFcwbHdtRjUzbHNwakM0TU9aNlRJUktKbVMwTjdTbUV2N2JrN0M0UXpRY1RaNlhJQVVIZnFwckJPVkxqUEJ1dHhIRHc9PSIsIm1hYyI6IjcwZDQ5YWQ2NTE0ZWEyM2ZlZjhmNzk0MDc4YjkwMzFlODcwMDI0MWQyZTg0YzQzZTA3NmY1MGVhM2NmNDRmYWYiLCJ0YWciOiIifQ==&lang=zh-cn'
        }
      ],
      total: {
        totalBetAmount: '82.00',
        totalPayoff: '-48.00'
      },
      pagination: {
        currentPage: 1,
        pageLimit: 20,
        total: 2,
        totalPage: 1
      },
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should return fishing record when the response is maintain', async () => {
    const mockMaintainFishingRecordResponse = {
      data: {
        code: 561000008,
        message: 'maintain',
        data: {
          is_maintain: true,
          maintain_info: [
            {
              game_kind: 38,
              start_time: '2024-12-04 03:05:00-04:00',
              end_time: '2024-12-04 03:15:00-04:00',
              message: 'text'
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainFishingRecordResponse)

    const result = await getFishingRecord({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      game_id: 38001,
      start_date: '2025-04-18',
      end_date: '2025-04-18',
      page: 1
    })

    expect(result).toEqual({
      maintain: true,
      maintainInfo: 'text'
    })
  })

  it('should return fishing record when the response is error', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)

    await expect(() => getFishingRecord({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      game_id: 38001,
      start_date: '2025-04-18',
      end_date: '2025-04-18',
      page: 1
    })).rejects.toThrowError(mockError.message)
  })
})

describe('getCardRecord', () => {
  it('should return card record when the response is successful', async () => {
    const mockCardRecordResponse = {
      data: {
        code: 0,
        message: '',
        data: {
          is_maintain: false,
          search_range: [
            {
              start_date: '2025-04-28',
              end_date: '2025-05-05'
            }
          ],
          wagers_list: [
            {
              id: 73264,
              wagers_date: '2025-04-28',
              game_type: 66104,
              result_status: 'LOSE',
              bet_amount: '10.00',
              payoff: '-10.00',
              wagers_detail_url:
                'https://pokerworldbattle.com/BetRecord/manager/managerPage.php?userID=9430&wagersID=2055141&data=8UaxQ4VX3o2f8XM%252BczOD26tlF6bnVmZAH2dIqZJ0nfbIMh9sMuQMtLRG0OGPGMSH&lang=zh-cn&GameType=12027&gmt=0&fromWeb='
            }
          ],
          total: [
            {
              total_bet_amount: '180.00',
              total_payoff: '110.90'
            }
          ],
          pagination: [
            {
              current_page: 1,
              page_limit: 20,
              total: 2,
              total_page: 1
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockCardRecordResponse)

    const result = await getCardRecord({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      game_id: 38001,
      start_date: '2025-04-18',
      end_date: '2025-04-18',
      page: 1
    })

    expect(result).toEqual({
      searchRange: {
        startDate: '2025-04-28',
        endDate: '2025-05-05'
      },
      wagersList: [
        {
          id: 73264,
          wagersDate: '2025-04-28',
          gameType: 66104,
          resultStatus: 'LOSE',
          betAmount: '10.00',
          payoff: '-10.00',
          wagersDetailUrl:
            'https://pokerworldbattle.com/BetRecord/manager/managerPage.php?userID=9430&wagersID=2055141&data=8UaxQ4VX3o2f8XM%252BczOD26tlF6bnVmZAH2dIqZJ0nfbIMh9sMuQMtLRG0OGPGMSH&lang=zh-cn&GameType=12027&gmt=0&fromWeb='
        }
      ],
      total: {
        totalBetAmount: '180.00',
        totalPayoff: '110.90'
      },
      pagination: {
        currentPage: 1,
        pageLimit: 20,
        total: 2,
        totalPage: 1
      },
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should return card record when the response is maintain', async () => {
    const mockMaintainCardRecordResponse = {
      data: {
        code: 561000008,
        message: 'maintain',
        data: {
          is_maintain: true,
          maintain_info: [
            {
              game_kind: 66,
              start_time: '2024-12-04 03:05:00-04:00',
              end_time: '2024-12-04 03:15:00-04:00',
              message: 'text'
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainCardRecordResponse)

    const result = await getCardRecord({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      game_id: 38001,
      start_date: '2025-04-18',
      end_date: '2025-04-18',
      page: 1
    })

    expect(result).toEqual({
      maintain: true,
      maintainInfo: 'text',
    })
  })

  it('should return card record when the response is error', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)

    await expect(() => getCardRecord({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      game_id: 38001,
      start_date: '2025-04-18',
      end_date: '2025-04-18',
      page: 1
    })).rejects.toThrowError(mockError.message)
  })
})

describe('getLotteryComplete', () => {
  it('should return lottery complete when the response is successful', async () => {
    const mockLotteryCompleteResponse = {
      data: {
        code: 0,
        message: '',
        data: {
          is_maintain: false,
          list: [
            {
              round_date: '2024-11-20',
              bet_amount: '0.00',
              commissionable: '0.00',
              payoff: '0.00'
            }
          ],
          total: [
            {
              total_bet_amount: '575.00',
              total_commissionable: '575.00',
              total_payoff: '-53.95'
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockLotteryCompleteResponse)

    const result = await getLotteryComplete({
      session_id: '456',
      hall_id: 123
    })

    expect(result).toEqual({
      list: [{
        roundDate: '2024-11-20',
        betAmount: '0.00',
        commissionable: '0.00',
        payoff: '0.00'
      }],
      total: {
        totalBetAmount: '575.00',
        totalCommissionable: '575.00',
        totalPayoff: '-53.95'
      },
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should return lottery complete when the response is maintain', async () => {
    const mockMaintainLotteryCompleteResponse = {
      data: {
        code: 561000008,
        message: 'maintain',
        data: {
          is_maintain: true,
          maintain_info: [
            {
              game_kind: 3,
              start_time: '2024-12-04 03:05:00-04:00',
              end_time: '2024-12-04 03:15:00-04:00',
              message: 'text'
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainLotteryCompleteResponse)

    const result = await getLotteryComplete({
      session_id: '456',
      hall_id: 123
    })

    expect(result).toEqual({
      maintain: true,
      maintainInfo: 'text'
    })
  })

  it('should return lottery complete when the response is error', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)

    await expect(() => getLotteryComplete({
      session_id: '456',
      hall_id: 123
    })).rejects.toThrowError(mockError.message)
  })
})

describe('getLotteryCompleteDetail', () => {
  it('should return lottery complete detail when the response is successful', async () => {
    const mockLotteryCompleteDetailResponse = {
      data: {
        code: 0,
        message: '',
        data: {
          is_maintain: false,
          win_type_list: [
            {
              id: 'L001',
              name: '定位'
            }
          ],
          wagers_list: [
            {
              id: 9215028160,
              wagers_date: '2024-11-28T00:29:51-04:00',
              game_type: 'B128',
              win_type: '定位',
              bet_detail:
                '<font color="red"></font> <font class="show_result" data-title="15,70,37,69,08,39,52,54,07,14,29,75,79,10,38,50,33,03,60,67,07,06,02" data-bingo="">第202411280031期</font> <font color="red"></font><font color="red">定位 1</font>@<strong><font color="red">320.00</font></strong>',
              bet_amount: '5.00',
              commissionable: '5.00',
              payoff: '-5.00'
            }
          ],
          total: [
            {
              total_bet_amount: '10.00',
              total_commissionable: '10.00',
              total_payoff: '-10.00'
            }
          ],
          pagination: [
            {
              current_page: 1,
              total_page: 1
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockLotteryCompleteDetailResponse)

    const result = await getLotteryCompleteDetail({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      game_id: '1',
      win_type_id: '1',
      date: '2025-03-20'
    })

    expect(result).toEqual({
      winTypeList: [
        {
          id: "L001",
          name: "定位",
        },
      ],
      wagersList: [
        {
          id: 9215028160,
          wagersDate: "2024-11-28 00:29:51",
          gameType: "B128",
          winType: "定位",
          betDetail: '<font color="red"></font> <font class="show_result" data-title="15,70,37,69,08,39,52,54,07,14,29,75,79,10,38,50,33,03,60,67,07,06,02" data-bingo="">第202411280031期</font> <font color="red"></font><font color="red">定位 1</font>@<strong><font color="red">320.00</font></strong>',
          betAmount: "5.00",
          commissionable: '5.00',
          payoff: "-5.00",
        },
      ],
      total: {
        totalBetAmount: "10.00",
        totalCommissionable: "10.00",
        totalPayoff: "-10.00"
      },
      pagination: {
        currentPage: 1,
        totalPage: 1,
      },
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should return lottery complete detail when the response is maintain', async () => {
    const mockMaintainLotteryCompleteDetailResponse = {
      data: {
        code: 561000008,
        message: 'maintain',
        data: {
          is_maintain: true,
          maintain_info: [
            {
              game_kind: 3,
              start_time: '2024-12-04 03:05:00-04:00',
              end_time: '2024-12-04 03:15:00-04:00',
              message: 'text'
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainLotteryCompleteDetailResponse)

    const result = await getLotteryCompleteDetail({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      game_id: '1',
      win_type_id: '1',
      date: '2025-03-20'
    })

    expect(result).toEqual({
      maintain: true,
      maintainInfo: 'text'
    })
  })

  it('should return lottery complete detail when the response is error', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)

    await expect(() => getLotteryCompleteDetail({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      game_id: '1',
      win_type_id: '1',
      date: '2025-03-20'
    })).rejects.toThrowError(mockError.message)
  })
})

describe('getLotteryUnCompleteDetail', () => {
  it('should return lottery uncomplete detail when the response is successful', async () => {
    const mockLotteryUnCompleteDetailResponse = {
      data: {
        code: 0,
        message: '',
        data: {
          is_maintain: false,
          win_type_list: [
            {
              id: 'L001',
              name: '特別號'
            }
          ],
          wagers_list: [
            {
              id: 9215027912,
              wagers_date: '2024-11-18T04:50:41-04:00',
              game_type: 'HKLT',
              win_type: '特別號',
              bet_detail:
                '<font color="red"></font> <font class="show_result" data-title="" data-bingo="">第124期</font> <font color="red"></font><font color="red">特別號01</font>@<strong><font color="red">48.00</font></strong>',
              bet_amount: '5.00',
              max_payoff: '235.00'
            }
          ],
          total: [
            {
              total_bet_amount: '5.00',
              total_max_payoff: '235.00'
            }
          ],
          pagination: [
            {
              current_page: 1,
              total_page: 1
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockLotteryUnCompleteDetailResponse)

    const result = await getLotteryUnCompleteDetail({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      game_id: 'HKLT',
      win_type_id: 'L001'
    })

    expect(result).toEqual({
      winTypeList: [
        {
          id: "L001",
          name: "特別號",
        },
      ],
      wagersList: [
        {
          id: 9215027912,
          wagersDate: "2024-11-18 04:50:41",
          gameType: "HKLT",
          winType: "特別號",
          betDetail: "<font color=\"red\"></font> <font class=\"show_result\" data-title=\"\" data-bingo=\"\">第124期</font> <font color=\"red\"></font><font color=\"red\">特別號01</font>@<strong><font color=\"red\">48.00</font></strong>",
          betAmount: "5.00",
          maxPayoff: "235.00",
        },
      ],
      total: {
        totalBetAmount: "5.00",
        totalMaxPayoff: "235.00",
      },
      pagination: {
        currentPage: 1,
        totalPage: 1,
      },
      maintain: false,
      maintainInfo: "",
    })
  })

  it('should return lottery uncomplete detail when the response is maintain', async () => {
    const mockMaintainLotteryUnCompleteDetailResponse = {
      data: {
        code: 561000008,
        message: 'Game is under maintenance',
        data: {
          is_maintain: true,
          maintain_info: [
            {
              game_kind: 12,
              start_time: '2024-03-06T12:00:00-04:00',
              end_time: '2024-03-06T23:30:00-04:00',
              message: 'text'
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainLotteryUnCompleteDetailResponse)

    const result = await getLotteryUnCompleteDetail({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      game_id: 'HKLT',
      win_type_id: 'L001'
    })

    expect(result).toEqual({
      maintain: true,
      maintainInfo: 'text'
    })
  })

  it('should return lottery uncomplete detail when the response is error', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)

    await expect(() => getLotteryUnCompleteDetail({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      game_id: 'HKLT',
      win_type_id: 'L001'
    })).rejects.toThrowError(mockError.message)
  })
})

describe('getLotteryUncomplete', () => {
  it('should return lottery uncomplete when the response is successful', async () => {
    const mockLotteryUnCompleteResponse = {
      data: {
        code: 0,
        message: '',
        data: {
          is_maintain: false,
          list: [
            {
              game_type: 'HKLT',
              game_name: '香港六合彩',
              count: 1,
              bet_amount: '5.00'
            }
          ],
          total: [
            {
              total_count: 1,
              total_bet_amount: '5.00'
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockLotteryUnCompleteResponse)

    const result = await getLotteryUncomplete({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123
    })

    expect(result).toEqual({
      list: [
        {
          gameType: 'HKLT',
          gameName: '香港六合彩',
          count: 1,
          betAmount: '5.00'
        }
      ],
      total: {
        totalCount: 1,
        totalBetAmount: '5.00'
      },
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should return lottery uncomplete when the response is maintain', async () => {
    const mockMaintainLotteryUnCompleteResponse = {
      data: {
        code: 561000008,
        message: 'maintain',
        data: {
          is_maintain: true,
          maintain_info: [
            {
              game_kind: 3,
              start_time: '2024-12-04 03:05:00-04:00',
              end_time: '2024-12-04 03:15:00-04:00',
              message: 'text'
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainLotteryUnCompleteResponse)

    const result = await getLotteryUncomplete({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123
    })

    expect(result).toEqual({
      maintain: true,
      maintainInfo: 'text'
    })
  })

  it('should return lottery uncomplete when the response is error', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)

    await expect(() => getLotteryUncomplete({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123
    })).rejects.toThrowError(mockError.message)
  })
})

describe('getLotteryCompleteByGame', () => {
  it('should return lottery complete when the response is successful', async () => {
    const mockLotteryCompleteResponse = {
      data: {
        code: 0,
        message: '',
        data: {
          is_maintain: false,
          list: [
            {
              game_type: 'BJ3D',
              game_name: '3D彩',
              bet_amount: '300.00',
              commissionable: '300.00',
              payoff: '3.30'
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockLotteryCompleteResponse)

    const result = await getLotteryCompleteByGame({
      hall_id: 123,
      session_id: '456',
      lang: 'zh-cn',
      rounddate: '2025-03-20'
    })

    expect(result).toEqual({
      list: [{
        gameType: 'BJ3D',
        gameName: '3D彩',
        betAmount: '300.00',
        commissionable: '300.00',
        payoff: '3.30'
      }],
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should return lottery complete when the response is maintain', async () => {
    const mockMaintainLotteryCompleteResponse = {
      data: {
        code: 561000008,
        message: 'maintain',
        data: {
          is_maintain: true,
          maintain_info: [
            {
              game_kind: 3,
              start_time: '2024-12-04 03:05:00-04:00',
              end_time: '2024-12-04 03:15:00-04:00',
              message: 'text'
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainLotteryCompleteResponse)

    const result = await getLotteryCompleteByGame({
      hall_id: 123,
      session_id: '456',
      lang: 'zh-cn',
      rounddate: '2025-03-20'
    })

    expect(result).toEqual({
      maintain: true,
      maintainInfo: 'text'
    })
  })

  it('should return lottery complete when the response is error', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)

    await expect(() => getLotteryCompleteByGame({
      hall_id: 123,
      session_id: '456',
      lang: 'zh-cn',
      rounddate: '2025-03-20'
    })).rejects.toThrowError(mockError.message)
  })
})

describe('getSportComplete', () => {
  it('should return sport complete when the response is successful', async () => {
    const mockSportCompleteResponse = {
      data: {
        code: 0,
        message: '',
        data: {
          is_maintain: false,
          wagers: [
            {
              date: '2024-11-20',
              weekday: 'Wed',
              statis_list: [
                {
                  id: 11,
                  name: '足球',
                  count: 2,
                  bet_amount: '20.00',
                  payoff: '-0.20',
                  commissionable: '19.80'
                },
                {
                  id: 2,
                  name: '複式過關',
                  count: 1,
                  bet_amount: '10.00',
                  payoff: '-10.00',
                  commissionable: '10.00'
                }
              ],
              subtotal: {
                bet_amount: '30.00',
                payoff: '-10.20',
                commissionable: '29.80'
              }
            }
          ],
          total: {
            bet_amount: '140.00',
            payoff: '61.80',
            commissionable: '121.80'
          }
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockSportCompleteResponse)

    const result = await getSportComplete({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123
    })

    expect(result).toEqual({
      wagers: [
        {
          date: '2024-11-20',
          weekday: 'Wed',
          statisList: [
            {
              id: 11,
              name: '足球',
              count: 2,
              betAmount: '20.00',
              payoff: '-0.20',
              commissionable: '19.80'
            },
            {
              id: 2,
              name: '複式過關',
              count: 1,
              betAmount: '10.00',
              payoff: '-10.00',
              commissionable: '10.00'
            }
          ],
          subtotal: {
            betAmount: '30.00',
            payoff: '-10.20',
            commissionable: '29.80'
          }
        }
      ],
      total: {
        betAmount: '140.00',
        payoff: '61.80',
        commissionable: '121.80'
      },
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should return sport complete when the response is maintain', async () => {
    const mockMaintainSportCompleteResponse = {
      data: {
        code: 561000008,
        message: 'maintain',
        data: {
          is_maintain: true,
          maintain_info: [
            {
              game_kind: 3,
              start_time: '2024-12-04 03:05:00-04:00',
              end_time: '2024-12-04 03:15:00-04:00',
              message: 'text'
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainSportCompleteResponse)

    const result = await getSportComplete({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123
    })

    expect(result).toEqual({
      maintain: true,
      maintainInfo: 'text'
    })
  })

  it('should return sport complete when the response is error', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)

    await expect(() => getSportComplete({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123
    })).rejects.toThrowError(mockError.message)
  })
})

describe('getSportCompleteDetail', () => {
  it('should return sport complete detail when the response is successful', async () => {
    const mockSportCompleteDetailResponse = {
      data: {
        code: 0,
        message: '',
        data: {
          is_maintain: false,
          game_list: [
            {
              id: 2,
              name: '複式過關'
            },
            {
              id: 11,
              name: '足球'
            }
          ],
          wagers: [
            {
              wagers_id: 4651403822,
              sport_name: '足球',
              add_date: '2024-11-20 20:59:47',
              odd_type: '歐洲盤',
              bet_state: '贏',
              bet_amount: '10.00',
              payoff: '9.80',
              commissionable: '9.80'
            },
            {
              wagers_id: 4651404011,
              sport_name: '足球',
              add_date: '2024-11-20 20:59:57',
              odd_type: '歐洲盤',
              bet_state: '輸',
              bet_amount: '10.00',
              payoff: '-10.00',
              commissionable: '10.00'
            }
          ],
          total: {
            bet_amount: '20.00',
            payoff: '-0.20',
            commissionable: '19.80'
          }
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockSportCompleteDetailResponse)

    const result = await getSportCompleteDetail({
      session_id: '456',
      hall_id: 123,
      lang: 'zh-cn',
      date: '2025-03-20',
      game_id: 1
    })

    expect(result).toEqual({
      gameList: [
        { id: 2, name: '複式過關' },
        { id: 11, name: '足球' }
      ],
      wagers: [
        {
          wagersId: 4651403822,
          sportName: '足球',
          addDate: '2024-11-20 20:59:47',
          oddType: '歐洲盤',
          betState: '贏',
          betAmount: '10.00',
          payoff: '9.80',
          commissionable: '9.80'
        },
        {
          wagersId: 4651404011,
          sportName: '足球',
          addDate: '2024-11-20 20:59:57',
          oddType: '歐洲盤',
          betState: '輸',
          betAmount: '10.00',
          payoff: '-10.00',
          commissionable: '10.00'
        }
      ],
      total: { betAmount: '20.00', payoff: '-0.20', commissionable: '19.80' },
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should return sport complete detail when the response is maintain', async () => {
    const mockMaintainSportCompleteDetailResponse = {
      data: {
        code: 561000008,
        message: 'maintain',
        data: {
          is_maintain: true,
          maintain_info: [
            {
              game_kind: 3,
              start_time: '2024-12-04 03:05:00-04:00',
              end_time: '2024-12-04 03:15:00-04:00',
              message: 'text'
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainSportCompleteDetailResponse)

    const result = await getSportCompleteDetail({
      session_id: '456',
      hall_id: 123,
      lang: 'zh-cn',
      date: '2025-03-20',
      game_id: 1
    })

    expect(result).toEqual({
      maintain: true,
      maintainInfo: 'text'
    })
  })

  it('should return sport complete detail when the response is error', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)

    await expect(() => getSportCompleteDetail({
      session_id: '456',
      hall_id: 123,
      lang: 'zh-cn',
      date: '2025-03-20',
      game_id: 1
    })).rejects.toThrowError(mockError.message)
  })
})

describe('getSportCompleteContent', () => {
  it('should return sport complete content when the response is successful', async () => {
    const mockSportCompleteContentResponse = {
      data: {
        code: 0,
        message: '',
        data: {
          is_maintain: false,
          content: [
            {
              match_date: '2025-05-03 13:00',
              sport_name: '越野摩托車',
              competition_name: '世界冠軍賽-冠軍',
              region_name: '世界',
              match_name: '2025 年世界錦標賽 - ',
              market_name: '冠軍',
              selection_name: 'Dominik Kubera',
              price: 17,
              match_info: '',
              state: 'N',
              state_name: '',
              selection_score: '',
              resettlement_reason: ''
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockSportCompleteContentResponse)

    const result = await getSportCompleteContent({
      session_id: '456',
      hall_id: 123,
      lang: 'zh-cn',
      wagers_id: 1
    })

    expect(result).toEqual({
      content: `
    越野摩托車 - 世界 - 世界冠軍賽-冠軍
    <p style=""></p>
    <p>2025 年世界錦標賽 - </p>
    <p>
      <span style="color:red;">冠軍</span> - 
      <span style="color:red;">Dominik Kubera</span> @ 
      <span style="color:red;">17</span>
    </p>
    2025-05-03 13:00
  `,
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should return sport complete content when the bet_state is maintain', async () => {
    const mockMaintainSportCompleteContentResponse = {
      data: {
        code: 561000008,
        message: 'maintain',
        data: {
          is_maintain: true,
          maintain_info: [
            {
              game_kind: 3,
              start_time: '2024-12-04 03:05:00-04:00',
              end_time: '2024-12-04 03:15:00-04:00',
              message: 'text'
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainSportCompleteContentResponse)

    const result = await getSportCompleteContent({
      session_id: '456',
      hall_id: 123,
      lang: 'zh-cn',
      wagers_id: 1
    })

    expect(result).toEqual({
      maintain: true,
      maintainInfo: 'text'
    })
  })

  it('should return sport complete content when the state is L', async () => {
    const mockSportCompleteContentResponse = {
      data: {
        code: 0,
        message: '',
        data: {
          is_maintain: false,
          content: [
            {
              match_date: '2025-05-03 13:00',
              sport_name: '越野摩托車',
              competition_name: '世界冠軍賽-冠軍',
              region_name: '世界',
              match_name: '2025 年世界錦標賽 - ',
              market_name: '冠軍',
              selection_name: 'Dominik Kubera',
              price: 17,
              match_info: '',
              state: 'L',
              state_name: '',
              selection_score: '',
              resettlement_reason: ''
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockSportCompleteContentResponse)

    const result = await getSportCompleteContent({
      session_id: '456',
      hall_id: 123,
      lang: 'zh-cn',
      wagers_id: 1
    })

    expect(result).toEqual({
      content: `
    越野摩托車 - 世界 - 世界冠軍賽-冠軍
    <p style="color: red;"></p>
    <p>2025 年世界錦標賽 - </p>
    <p>
      <span style="color:red;">冠軍</span> - 
      <span style="color:red;">Dominik Kubera</span> @ 
      <span style="color:red;">17</span>
    </p>
    2025-05-03 13:00
  `,
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should return sport complete content when the state is R', async () => {
    const mockSportCompleteContentResponse = {
      data: {
        code: 0,
        message: '',
        data: {
          is_maintain: false,
          content: [
            {
              match_date: '2025-05-03 13:00',
              sport_name: '越野摩托車',
              competition_name: '世界冠軍賽-冠軍',
              region_name: '世界',
              match_name: '2025 年世界錦標賽 - ',
              market_name: '冠軍',
              selection_name: 'Dominik Kubera',
              price: 17,
              match_info: '',
              state: 'R',
              state_name: '',
              selection_score: '',
              resettlement_reason: ''
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockSportCompleteContentResponse)

    const result = await getSportCompleteContent({
      session_id: '456',
      hall_id: 123,
      lang: 'zh-cn',
      wagers_id: 1
    })

    expect(result).toEqual({
      content: `
    越野摩托車 - 世界 - 世界冠軍賽-冠軍
    <p style="color: gray;"></p>
    <p>2025 年世界錦標賽 - </p>
    <p>
      <span style="color:red;">冠軍</span> - 
      <span style="color:red;">Dominik Kubera</span> @ 
      <span style="color:red;">17</span>
    </p>
    2025-05-03 13:00
  `,
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should return sport complete content when the state is W', async () => {
    const mockSportCompleteContentResponse = {
      data: {
        code: 0,
        message: '',
        data: {
          is_maintain: false,
          content: [
            {
              match_date: '2025-05-03 13:00',
              sport_name: '越野摩托車',
              competition_name: '世界冠軍賽-冠軍',
              region_name: '世界',
              match_name: '2025 年世界錦標賽 - ',
              market_name: '冠軍',
              selection_name: 'Dominik Kubera',
              price: 17,
              match_info: '',
              state: 'W',
              state_name: '',
              selection_score: '',
              resettlement_reason: ''
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockSportCompleteContentResponse)

    const result = await getSportCompleteContent({
      session_id: '456',
      hall_id: 123,
      lang: 'zh-cn',
      wagers_id: 1
    })

    expect(result).toEqual({
      content: `
    越野摩托車 - 世界 - 世界冠軍賽-冠軍
    <p style="color: #00FF00;"></p>
    <p>2025 年世界錦標賽 - </p>
    <p>
      <span style="color:red;">冠軍</span> - 
      <span style="color:red;">Dominik Kubera</span> @ 
      <span style="color:red;">17</span>
    </p>
    2025-05-03 13:00
  `,
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should return sport complete content when the response is error', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)

    await expect(() => getSportCompleteContent({
      session_id: '456',
      hall_id: 123,
      lang: 'zh-cn',
      wagers_id: 1
    })).rejects.toThrowError(mockError.message)
  })
})

describe('getSportUncomplete', () => {
  it('should return sport uncomplete when the response is successful', async () => {
    const mockSportUncompleteResponse = {
      data: {
        code: 0,
        message: '',
        data: {
          is_maintain: false,
          statis_list: [
            {
              id: 187,
              name: '越野摩托車',
              count: 1,
              bet_amount: '10.00'
            }
          ],
          total: {
            count: 1,
            bet_amount: '10.00'
          }
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockSportUncompleteResponse)

    const result = await getSportUncomplete({
      hall_id: 123,
      session_id: '456',
      lang: 'zh-cn'
    })

    expect(result).toEqual({
      statisList: [{
        id: 187,
        name: '越野摩托車',
        count: 1,
        betAmount: '10.00'
      }],
      total: {
        count: 1,
        betAmount: '10.00'
      },
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should return sport uncomplete when the response is maintain', async () => {
    const mockMaintainSportUncompleteResponse = {
      data: {
        code: 561000008,
        message: 'maintain',
        data: {
          is_maintain: true,
          maintain_info: [
            {
              game_kind: 3,
              start_time: '2024-12-04 03:05:00-04:00',
              end_time: '2024-12-04 03:15:00-04:00',
              message: 'text'
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainSportUncompleteResponse)

    const result = await getSportUncomplete({
      hall_id: 123,
      session_id: '456',
      lang: 'zh-cn'
    })

    expect(result).toEqual({
      maintain: true,
      maintainInfo: 'text'
    })
  })

  it('should return sport uncomplete when the response is error', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)

    await expect(() => getSportUncomplete({
      hall_id: 123,
      session_id: '456',
      lang: 'zh-cn'
    })).rejects.toThrowError(mockError.message)
  })
})

describe('getSportUnCompleteDetail', () => {
  it('should return sport uncomplete detail when the response is successful', async () => {
    const mockSportUnCompleteDetailResponse = {
      data: {
        code: 0,
        message: '',
        data: {
          is_maintain: false,
          game_list: [
            {
              id: 187,
              name: '越野摩托車'
            }
          ],
          wagers: [
            {
              wagers_id: 4550916018,
              sport_name: '越野摩托車',
              add_date: '2024-10-20 23:46:00',
              odd_type: '歐洲盤',
              bet_state: '未結算',
              bet_amount: '10.00'
            }
          ],
          total: {
            count: 1,
            bet_amount: '10.00'
          }
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockSportUnCompleteDetailResponse)

    const result = await getSportUnCompleteDetail({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      game_id: 1
    })

    expect(result).toEqual({
      gameList: [
        {
          id: 187,
          name: '越野摩托車'
        }
      ],
      wagers: [
        {
          wagersId: 4550916018,
          sportName: '越野摩托車',
          addDate: '2024-10-20 23:46:00',
          oddType: '歐洲盤',
          betState: '未結算',
          betAmount: '10.00'
        }
      ],
      total: {
        count: 1,
        betAmount: '10.00'
      },
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should return sport uncomplete detail when the response is maintain', async () => {
    const mockMaintainSportUnCompleteDetailResponse = {
      data: {
        code: 561000008,
        message: 'maintain',
        data: {
          is_maintain: true,
          maintain_info: [
            {
              game_kind: 3,
              start_time: '2024-12-04 03:05:00-04:00',
              end_time: '2024-12-04 03:15:00-04:00',
              message: 'text'
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainSportUnCompleteDetailResponse)

    const result = await getSportUnCompleteDetail({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      game_id: 1
    })

    expect(result).toEqual({
      maintain: true,
      maintainInfo: 'text'
    })
  })

  it('should return sport uncomplete detail when the response is error', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)

    await expect(() => getSportUnCompleteDetail({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      game_id: 1
    })).rejects.toThrowError(mockError.message)
  })
})
