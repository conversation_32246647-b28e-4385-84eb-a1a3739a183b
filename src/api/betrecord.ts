import apiRequest from '@/utils/apiRequest'
import { 
  ILiveRecord,
  IGetLiveDateRecordParam,
  IGetLiveRoundSerialRecordParam,
  IGetCasinoDateRecordParam,
  ICasinoDateRecord,
  IGetCasinoComboRecordParam,
  ICasinoComboRecord,
  IGetLotteryUncompleteParams,
  ILotteryUncomplete,
  IGetLotteryUnCompleteDetailParams,
  ILotteryUnCompleteDetail,
  IGetLotteryCompleteParams,
  ILotteryComplete,
  IGetLotteryCompleteByGameParams,
  ILotteryCompleteByGame,
  IGetLotteryCompleteDetailParams,
  ILotteryCompleteDetail,
  IGetSportUncompleteParams,
  ISportUncomplete,
  IGetSportUnCompleteDetailParams,
  ISportUnCompleteDetail,
  IGetSportCompleteParams,
  ISportComplete,
  IGetSportCompleteDetailParams,
  ISportCompleteDetail,
  IGetSportCompleteContentParams,
  ISportCompleteContent,
  IGetCasinoGameParams,
  ICasinoGame,
  IGetFishingRecordParams,
  IFishRecord,
  IGetCardRecordParams,
  ICardRecord
} from '@/types/betrecord'
import { maintainText } from '@/utils/maintaintxt'
import dayjs from 'dayjs'

const liveDataTransForm = (data: any) => {
  const {
    is_maintain,
    search_range = [{ start_date: '', end_date: '' }],
    wagers_list = [],
    total = [{ total_bet_amount: '', total_commissionable: '', total_payoff: '' }],
    pagination = [{ current_page: 1, page_limit: 20, total: 0, total_page: 1 }]
  } = data
  
  return {
    isMaintain: is_maintain,
    searchRange: {
      startDate: search_range[0].start_date,
      endDate: search_range[0].end_date
    },
    wagersList: wagers_list.map((item: any) => ({
      id: item.id,
      wagersDate: dayjs(item.wagers_date).tz('Etc/GMT+4').format('YYYY-MM-DD HH:mm:ss'),
      gameType: item.game_type,
      roundSerial: item.round_serial,
      roundNo: item.round_no,
      tableCode: item.table_code,
      betAmount: item.bet_amount,
      commissionable: item.commissionable,
      payoff: item.payoff,
      wagersDetailUrl: item.wagers_detail_url
    })),
    total: {
      totalCount: pagination[0].total,
      totalBetAmount: total[0].total_bet_amount,
      totalCommissionable: total[0].total_commissionable,
      totalPayoff: total[0].total_payoff
    },
    pagination: {
      currentPage: pagination[0].current_page,
      pageLimit: pagination[0].page_limit,
      total: pagination[0].total,
      totalPage: pagination[0].total_page
    },
    maintain: false,
    maintainInfo: ''
  }
}

const casinoGameTransForm = (data: any) => {
  const {
    search_range = [],
    game_list = { bb_record: [], bb_combo_record: []  },
  } = data

  return {
    searchRange: {
      startDate: search_range[0].start_date || '',
      endDate: search_range[0].end_date || '',
    },
    gameList: {
      bbRecord: game_list.bb_record,
      bbComboRecord: game_list.bb_combo_record,
    }
  }
}

const casinoDateDataTransForm = (data: any) => {
  const {
    is_maintain,
    search_range = [{ start_date: '', end_date: '' }],
    wagers_list = [],
    total = [{ total_bet_amount: '',  total_payoff: '', total_jp_amount: '' }],
    pagination = [{ current_page: 1, page_limit: 20, total: 0, total_page: 1 }]
  } = data
  
  return {
    isMaintain: is_maintain,
    searchRange: {
      startDate: search_range[0].start_date,
      endDate: search_range[0].end_date
    },
    wagersList: wagers_list.map((item: any) => ({
      id: item.id,
      wagersDate: dayjs(item.wagers_date).tz('Etc/GMT+4').format('YYYY-MM-DD HH:mm:ss'),
      gameType: item.game_type,
      betAmount: item.bet_amount,
      payoff: item.payoff,
      jpAmount: item.jp_amount,
      wagersDetailUrl: item.wagers_detail_url
    })),
    total: {
      totalCount: pagination[0].total,
      totalBetAmount: total[0].total_bet_amount,
      totalPayoff: total[0].total_payoff,
      totalJpAmount: total[0].total_jp_amount
    },
    pagination: {
      currentPage: pagination[0].current_page,
      pageLimit: pagination[0].page_limit,
      total: pagination[0].total,
      totalPage: pagination[0].total_page
    },
    maintain: false,
    maintainInfo: ''
  }
}

const casinComboDataTransForm = (data: any) => {
  const {
    is_maintain,
    search_range = [{ start_date: '', end_date: '' }],
    wagers_list = [],
    pagination = [{ current_page: 1, page_limit: 20, total: 0, total_page: 1 }]
  } = data
  
  return {
    isMaintain: is_maintain,
    searchRange: {
      startDate: search_range[0].start_date,
      endDate: search_range[0].end_date
    },
    wagersList: wagers_list.map((item: any) => ({
      id: item.id,
      wagersDate: item.wagers_date,
      gameType: item.game_type,
      times: item.times
    })),
    pagination: {
      currentPage: pagination[0].current_page,
      pageLimit: pagination[0].page_limit,
      total: pagination[0].total,
      totalPage: pagination[0].total_page
    },
    maintain: false,
    maintainInfo: ''
  }
}

const lotteryCompleteTransForm = (data: any) => {
  const {
    list = [],
    total = []
  } = data

  return {
    list: list.map((item: any) => ({
      roundDate: item.round_date,
      betAmount: item.bet_amount,
      commissionable: item.commissionable,
      payoff: item.payoff
    })),
    total: {
      totalBetAmount: total[0].total_bet_amount,
      totalCommissionable: total[0].total_commissionable,
      totalPayoff: total[0].total_payoff
    }
  }
}

const lotteryCompleteDetailTransForm = (data: any) => {
  const {
    win_type_list = [],
    wagers_list = [],
    total = [{}],
    pagination = [{}]
  } = data

  return {
    winTypeList: win_type_list,
    wagersList: wagers_list.map((item: any) => ({
      id: item.id,
      wagersDate: dayjs(item.wagers_date).tz('Etc/GMT+4').format('YYYY-MM-DD HH:mm:ss'),
      gameType: item.game_type,
      winType: item.win_type,
      betDetail: item.bet_detail,
      betAmount: item.bet_amount,
      commissionable: item.commissionable,
      payoff: item.payoff
    })),
    total: {
      totalBetAmount: total[0]?.total_bet_amount || '--',
      totalCommissionable: total[0]?.total_commissionable || '--',
      totalPayoff: total[0]?.total_payoff || '--'
    },
    pagination: {
      currentPage: pagination[0]?.current_page || 1,
      totalPage: pagination[0]?.total_page || 1
    }
  }
}

const lotteryUncompleteTransForm = (data: any) => {
  const {
    list = [],
    total = []
  } = data

  return {
    list: list.map((item: any) => ({
      gameType: item.game_type,
      gameName: item.game_name,
      count: item.count,
      betAmount: item.bet_amount
    })),
    total: {
      totalCount: total[0]?.total_count || 0,
      totalBetAmount: total[0]?.total_bet_amount || 0
    }
  }
}

const lotteryUnCompDetailTransform = (data: any) => {
  const {
    win_type_list = [],
    wagers_list = [],
    total = [],
    pagination = []
  } = data

  return {
    winTypeList: win_type_list,
    wagersList: wagers_list.map((item: any) => ({
      id: item.id,
      wagersDate: dayjs(item.wagers_date).tz('Etc/GMT+4').format('YYYY-MM-DD HH:mm:ss'),
      gameType: item.game_type,
      winType: item.win_type,
      betDetail: item.bet_detail,
      betAmount: item.bet_amount,
      maxPayoff: item.max_payoff
    })),
    total: {
      totalBetAmount: total[0]?.total_bet_amount || '--',
      totalMaxPayoff: total[0]?.total_max_payoff || '--'
    },
    pagination: {
      currentPage: pagination[0]?.current_page || 1,
      totalPage: pagination[0]?.total_page || 1
    }
  }
}

const sportCompleteTransForm = (data: any) => {
  const {
    wagers = [],
    total = {}
  } = data

  return {
    wagers: wagers.map((item: any) => ({
      date: item.date,
      weekday: item.weekday,
      statisList: item.statis_list.map((listItem: any) => ({
        id: listItem.id,
        name: listItem.name,
        count: listItem.count,
        betAmount: listItem.bet_amount,
        payoff: listItem.payoff,
        commissionable: listItem.commissionable,
      })),
      subtotal: {
        betAmount: item.subtotal.bet_amount,
        payoff: item.subtotal.payoff,
        commissionable: item.subtotal.commissionable
      }
    })),
    total: {
      betAmount: total.bet_amount,
      payoff: total.payoff,
      commissionable: total.commissionable
    }
  }
}

const sportUncompleteTransForm = (data: any) => {
  const {
    statis_list = [],
    total = {}
  } = data

  return {
    statisList: statis_list.map((item: any) => ({
      id: item.id,
      name: item.name,
      count: item.count,
      betAmount: item.bet_amount
    })),
    total: {
      count: total.count,
      betAmount: total.bet_amount
    }
  }
}

const sportCompleteDetailTransForm = (data: any) => {
  const {
    game_list = [],
    wagers = [],
    total = {}
  } = data

  return {
    gameList: game_list,
    wagers: wagers.map((item: any) => ({
      wagersId: item.wagers_id,
      sportName: item.sport_name,
      addDate: item.add_date,
      oddType: item.odd_type,
      betState: item.bet_state,
      betAmount: item.bet_amount,
      payoff: item.payoff,
      commissionable: item.commissionable
    })),
    total: {
      betAmount: total.bet_amount,
      payoff: total.payoff,
      commissionable: total.commissionable
    }
  }
}


const sportUncompDetailTransform = (data: any) => {
  const {
    game_list = [],
    wagers = [],
    total = {}
  } = data

  return {
    gameList: game_list,
    wagers: wagers.map((item: any) => ({
      wagersId: item.wagers_id,
      sportName: item.sport_name,
      addDate: item.add_date,
      oddType: item.odd_type,
      betState: item.bet_state,
      betAmount: item.bet_amount
    })),
    total: {
      count: total.count,
      betAmount: total.bet_amount
    }
  }
}

const resultStatus = (result: string) => {
  switch (result) {
    case 'L':
      return 'color: red;'
    case 'R':
      return 'color: gray;'
    case 'W':
      return 'color: #00FF00;'
    default:
      return ''
  }
}
const sportContentTransform = (data: any) => {
  const {
    sport_name,
    region_name,
    competition_name,
    state,
    state_name,
    match_name,
    market_name,
    selection_name,
    price,
    match_date,
  } = data[0]

  const content = `
    ${sport_name} - ${region_name} - ${competition_name}
    <p style="${resultStatus(state)}">${state_name}</p>
    <p>${match_name}</p>
    <p>
      <span style="color:red;">${market_name}</span> - 
      <span style="color:red;">${selection_name}</span> @ 
      <span style="color:red;">${price}</span>
    </p>
    ${match_date}
  `

  return {
    content
  }
}

const fishDataTransForm = (data: any) => {
  const {
    search_range = [],
    wagers_list = [],
    total = [],
    pagination = []
  } = data

  return {
    searchRange: {
      startDate: search_range[0].start_date,
      endDate: search_range[0].end_date
    },
    wagersList: wagers_list.map((item: any) => ({
      id: item.id,
      wagersDate: dayjs(item.wagers_date).tz('Etc/GMT+4').format('YYYY-MM-DD HH:mm:ss'),
      gameType: item.game_type,
      betAmount: item.bet_amount,
      payoff: item.payoff,
      wagersDetailUrl: item.wagers_detail_url
    })),
    total: {
      totalBetAmount: total[0].total_bet_amount,
      totalPayoff: total[0].total_payoff
    },
    pagination: {
      currentPage: pagination[0].current_page,
      pageLimit: pagination[0].page_limit,
      total: pagination[0].total,
      totalPage: pagination[0].total_page
    }
  }
}

const cardDataTransForm = (data: any) => {
  const {
    search_range = [],
    wagers_list = [],
    total = [],
    pagination = []
  } = data

  return {
    searchRange: {
      startDate: search_range[0].start_date,
      endDate: search_range[0].end_date
    },
    wagersList: wagers_list.map((item: any) => ({
      id: item.id,
      wagersDate: item.wagers_date,
      gameType: item.game_type,
      resultStatus: item.result_status,
      betAmount: item.bet_amount,
      payoff: item.payoff,
      wagersDetailUrl: item.wagers_detail_url
    })),
    total: {
      totalBetAmount: total[0].total_bet_amount,
      totalPayoff: total[0].total_payoff
    },
    pagination: {
      currentPage: pagination[0].current_page,
      pageLimit: pagination[0].page_limit,
      total: pagination[0].total,
      totalPage: pagination[0].total_page
    }
  }
}
export const getLiveRecordByDate = async (params: IGetLiveDateRecordParam) => {
  try {
    const res = await apiRequest.get('/api/live/wagers_by_date', { params })
    const resData = res.data
    let result = undefined

    if (resData.data.is_maintain) {
      result = {
        maintain: true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else {
      result = liveDataTransForm(resData.data)
    }

    return result as ILiveRecord
  } catch (error) {
    return Promise.reject(error)
  }
}

export const getLiveRecordByRoundSerial = async (params: IGetLiveRoundSerialRecordParam) => {
  try {
    const res = await apiRequest.get('/api/live/wagers_by_round_serial', { params })
    const resData = res.data
    let result = undefined

    if (resData.data.is_maintain) {
      result = {
        maintain: true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else {
      result = liveDataTransForm(resData.data)
    }

    return result as ILiveRecord
  } catch (error) {
    return Promise.reject(error)
  }
}


export const getCasinoGame = async (params: IGetCasinoGameParams) => {
  try {
    const res = await apiRequest.get('/api/slot/wager_search_bar', { params })
    const resData = res.data
    let result = undefined

    if (resData.data.is_maintain) {
      result = {
        maintain: true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else {
      result = {
        ...casinoGameTransForm(resData.data),
        maintain: false,
        maintainInfo: ''
      }
    }

    return result as ICasinoGame
  } catch (error) {
    return Promise.reject(error)
  }
}

export const getCasinoComboRecord = async (params: IGetCasinoComboRecordParam) => {
  try {
    const res = await apiRequest.get('/api/slot/crash_combo_wagers_by_date', { params })
    const resData = res.data
    let result = undefined

    if (resData.data.is_maintain) {
      result = {
        maintain: true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else {
      result = casinComboDataTransForm(resData.data)
    }

    return result as ICasinoComboRecord
  } catch (error) {
    return Promise.reject(error)
  }
}

export const getCasinoDateRecord = async (params: IGetCasinoDateRecordParam) => {
  try {
    const res = await apiRequest.get('/api/slot/wagers_by_date', { params })
    const resData = res.data
    let result = undefined

    if (resData.data.is_maintain) {
      result = {
        maintain: true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else {
      result = casinoDateDataTransForm(resData.data)
    }

    return result as ICasinoDateRecord
  } catch (error) {
    return Promise.reject(error)
  }
}

export const getFishingRecord = async (params: IGetFishingRecordParams) => {
  try {
    const res = await apiRequest.get('/api/fish/wagers_by_date', { params })
    const resData = res.data
    let result = undefined

    if (resData.data.is_maintain) {
      result = {
        maintain: true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else {
      result = {
        ...fishDataTransForm(resData.data),
        maintain: false,
        maintainInfo: ''
      }
    }

    return result as IFishRecord
  } catch (error) {
    return Promise.reject(error)
  }
}

export const getCardRecord = async (params: IGetCardRecordParams) => {
  try {
    const res = await apiRequest.get('/api/battle/wagers_by_date', { params })
    const resData = res.data
    let result = undefined

    if (resData.data.is_maintain) {
      result = {
        maintain: true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else {
      result = {
        ...cardDataTransForm(resData.data),
        maintain: false,
        maintainInfo: ''
      }
    }

    return result as ICardRecord
  } catch (error) {
    return Promise.reject(error)
  }
}

export const getLotteryCompleteByGame = async (params: IGetLotteryCompleteByGameParams) => {
  try {
    const res = await apiRequest.get('/api/lottery/finish_statis_by_game', { params })
    const resData = res.data
    let result = undefined

    if (resData.data.is_maintain) {
      result = {
        maintain: true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else {
      result = {
        list: resData.data.list.map((item: any) => ({
          gameType: item.game_type,
          gameName: item.game_name,
          betAmount: item.bet_amount,
          commissionable: item.commissionable,
          payoff: item.payoff
        })),
        maintain: false,
        maintainInfo: ''
      }
    }

    return result as ILotteryCompleteByGame
  } catch (error) {
    return Promise.reject(error)
  }
}

export const getLotteryComplete = async (params: IGetLotteryCompleteParams) => {
  try {
    const res = await apiRequest.get('/api/lottery/finish_statis_by_date', { params })
    const resData = res.data
    let result = undefined

    if (resData.data.is_maintain) {
      result = {
        maintain: true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else {
      result = {
        ...lotteryCompleteTransForm(resData.data),
        maintain: false,
        maintainInfo: ''
      }
    }

    return result as ILotteryComplete
  } catch (error) {
    return Promise.reject(error)
  }
}

export const getLotteryCompleteDetail = async (params: IGetLotteryCompleteDetailParams) => {
  try {
    const res = await apiRequest.get('/api/lottery/finish_wagers_detail', {params})
    const resData = res.data
    let result = undefined

    if (resData.data.is_maintain) {
      result = {
        maintain: true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else {
      result = {
        ...lotteryCompleteDetailTransForm(resData.data),
        maintain: false,
        maintainInfo: ''
      }
    }

    return result as ILotteryCompleteDetail
  } catch (error) {
    return Promise.reject(error)
  }
}

export const getLotteryUnCompleteDetail = async (params: IGetLotteryUnCompleteDetailParams) => {
  try {
    const res = await apiRequest.get('/api/lottery/unfinish_wagers_detail', {params})
    const resData = res.data
    let result = undefined

    if (resData.data.is_maintain) {
      result = {
        maintain: true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else {
      result = {
        ...lotteryUnCompDetailTransform(resData.data),
        maintain: false,
        maintainInfo: ''
      }
    }

    return result as ILotteryUnCompleteDetail
  } catch (error) {
    return Promise.reject(error)
  }
}

export const getLotteryUncomplete = async (params: IGetLotteryUncompleteParams) => {
  try {
    const res = await apiRequest.get('/api/lottery/unfinish_statis', { params })
    const resData = res.data
    let result = undefined

    if (resData.data.is_maintain) {
      result = {
        maintain: true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else {
      result = {
        ...lotteryUncompleteTransForm(resData.data),
        maintain: false,
        maintainInfo: ''
      }
    }

    return result as ILotteryUncomplete
  } catch (error) {
    return Promise.reject(error)
  }
}

export const getSportComplete = async (params: IGetSportCompleteParams) => {
  try {
    const res = await apiRequest.get('/api/sport/finish_statis', { params })
    const resData = res.data
    let result = undefined

    if (resData.data.is_maintain) {
      result = {
        maintain: true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else {
      result = {
        ...sportCompleteTransForm(resData.data),
        maintain: false,
        maintainInfo: ''
      }
    }

    return result as ISportComplete
  } catch (error) {
    return Promise.reject(error)
  }
}

export const getSportCompleteDetail = async (params: IGetSportCompleteDetailParams) => {
  try { 
    const res = await apiRequest.get('/api/sport/finish_statis_by_game', {params})
    const resData = res.data
    let result = undefined

    if (resData.data.is_maintain) {
      result = {
        maintain: true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else {
      result = {
        ...sportCompleteDetailTransForm(resData.data),
        maintain: false,
        maintainInfo: ''
      }
    }

    return result as ISportCompleteDetail
  } catch (error) {
    return Promise.reject(error)
  }
}

export const getSportCompleteContent = async (params: IGetSportCompleteContentParams) => {
  try {
    const res = await apiRequest.get('/api/sport/wagers_detail', {params})
    const resData = res.data
    let result = undefined

    if (resData.data.is_maintain) {
      result = {
        maintain: true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else {
      result = {
        ...sportContentTransform(resData.data.content),
        maintain: false,
        maintainInfo: ''
      }
    }

    return result as ISportCompleteContent
  } catch (error) {
    return Promise.reject(error)
  }
}

export const getSportUncomplete = async (params: IGetSportUncompleteParams) => {
  try {
    const res = await apiRequest.get('/api/sport/unfinish_statis', {params})
    const resData = res.data
    let result = undefined

    if (resData.data.is_maintain) {
      result = {
        maintain: true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else {
      result = {
        ...sportUncompleteTransForm(resData.data),
        maintain: false,
        maintainInfo: ''
      }
    }

    return result as ISportUncomplete
  } catch (error) {
    return Promise.reject(error)
  }
}

export const getSportUnCompleteDetail = async (params: IGetSportUnCompleteDetailParams) => {
  try { 
    const res = await apiRequest.get('/api/sport/unfinish_statis_by_game', {params})
    const resData = res.data
    let result = undefined

    if (resData.data.is_maintain) {
      result = {
        maintain: true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else {
      result = {
        ...sportUncompDetailTransform(resData.data),
        maintain: false,
        maintainInfo: ''
      }
    }

    return result as ISportUnCompleteDetail
  } catch (error) {
    return Promise.reject(error)
  }
}

