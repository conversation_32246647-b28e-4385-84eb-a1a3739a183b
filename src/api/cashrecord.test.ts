import apiRequest from '@/utils/apiRequest';
import { describe, it, expect, vi } from 'vitest'
import { getCashRecord } from '@/api/cashrecord';
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

dayjs.extend(timezone)
dayjs.extend(utc)

vi.mock('@/utils/apiRequest', () => {
  return {
    default: {
      get: vi.fn(),
    }
  }
})

const params = {
  hall_id: 123,
  session_id: '456',
  start_date: '2025-03-17',
  end_date: '2025-03-17',
  sort: 'desc',
  page: 1
}

describe('getCashRecord', () => {
  it('should return transformed data when request is successful', async () => {
    const mockResponse = {
      data: {
        code: 0,
        message: '',
        data: {
          transaction_record: [
            {
              amount: '-50.00',
              balance: '2010258461.68',
              create_time: '2024-11-08T01:44:31-04:00',
              op_code: 40000,
              note: {
                ref_id: '520000093878',
                result: ''
              }
            },
            {
              amount: '-50.00',
              balance: '2010258511.68',
              create_time: '2024-11-08T01:44:13-04:00',
              op_code: 40000,
              note: {
                ref_id: '520000093877',
                result: ''
              }
            }
          ],
          pagination: {
            page: 1,
            page_limit: 30,
            total_number: 2,
            total_page: 1
          },
          total: {
            amount: '-100.00'
          },
          limit_date: '2021-08-01',
          maintain_info: [
            {
              game_kind: 66,
              start_time: '2024-11-20T22:40:00-04:00',
              end_time: '2024-11-20T22:45:00-04:00',
              message: 'hanna test'
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockResponse)
    const result = await getCashRecord(params)

    expect(result).toEqual({
      transactionRecord: [
        {
          amount: '-50.00',
          balance: '2010258461.68',
          createTime: '2024-11-08 01:44:31',
          opCode: 40000,
          note: {
            refId: '520000093878',
            result: ''
          }
        },
        {
          amount: '-50.00',
          balance: '2010258511.68',
          createTime: '2024-11-08 01:44:13',
          opCode: 40000,
          note: {
            refId: '520000093877',
            result: ''
          }
        }
      ],
      pagination: {
        page: 1,
        pageLimit: 30,
        totalNumber: 2,
        totalPage: 1
      },
      total: {
        amount: '-100.00'
      },
      limitDate: '2021-08-01',
      maintainInfo: [
        {
          gameKind: 66,
          startTime: '2024-11-20 22:40:00',
          endTime: '2024-11-20 22:45:00',
          message: 'hanna test'
        }
      ]
    })
  })

  it('should reject when request fails', async () => {
    vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))

    await expect(getCashRecord(params)).rejects.toThrow('Network Error')
  })
})
