import apiRequest from '@/utils/apiRequest'
import type { IGetCashRecordParams, ICashRecordData } from '@/types/cashrecord'
import dayjs from 'dayjs'

const cashRecordDataTransform = (data: any) => {
  const {
    transaction_record,
    pagination,
    total,
    limit_date,
    maintain_info
  } = data
  const result = {
    transactionRecord: transaction_record.map((record: any) => ({
      amount: record.amount,
      balance: record.balance,
      createTime: dayjs(record.create_time).tz('Etc/GMT+4').format('YYYY-MM-DD HH:mm:ss'),
      opCode: record.op_code,
      note: {
        refId: record.note.ref_id,
        result: record.note.result
      }
    })),
    pagination: {
      page: pagination.page,
      pageLimit: pagination.page_limit,
      totalNumber: pagination.total_number,
      totalPage: pagination.total_page
    },
    total,
    limitDate: limit_date,
    maintainInfo: maintain_info.map((info: any) => ({
      gameKind: info.game_kind,
      startTime: dayjs(info.start_time).tz('Etc/GMT+4').format('YYYY-MM-DD HH:mm:ss'),
      endTime: dayjs(info.end_time).tz('Etc/GMT+4').format('YYYY-MM-DD HH:mm:ss'),
      message: info.message
    })) 
  } as ICashRecordData
  return result
}

export const getCashRecord = async (params: IGetCashRecordParams) => {
  try {
    const res = await apiRequest.get('/api/account/transaction_record', { params })
    const resData = res.data
    return cashRecordDataTransform(resData.data)
  } catch (error: any) {
    return Promise.reject(error)
  }
}
