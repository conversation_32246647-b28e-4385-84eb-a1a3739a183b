import { describe, it, expect, vi } from 'vitest';
import apiRequest from '@/utils/apiRequest'
import { getEffectiveBetting } from '@/api/effectivebetting';
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

dayjs.extend(timezone)
dayjs.extend(utc)

vi.mock('@/utils/apiRequest', () => {
  return {
    default: {
      get: vi.fn(),
    }
  }
})

const params = {
  hall_id: 123,
  session_id: '456',
  start_date: '2025-02-04',
  end_date: '2025-02-04'
}

describe('getEffectiveBetting', () => {

  it('should return transformed data when request is successful', async () => {
    const mockResponse = {
      data: {
        code: 0,
        message: "",
        data: {
          game_bet_list: [
            {
              game_kind: 3,
              bet: 0,
              payoff: 0
            }
          ],
          bet_total: 3347,
          payoff_total: -747.5,
          is_maintain: true,
          maintain_info: [
            {
              game_kind: 5,
              start_time: '2024-09-05T04:54:00-04:00',
              end_time: '2024-09-05T04:59:00-04:00',
              message: 'test'
            }
          ]
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockResponse)

    const result = await getEffectiveBetting(params)

    expect(result).toEqual({
      gameBetList: [{
        gameKind: 3,
        bet: 0,
        payoff: 0
      }],
      betTotal: 3347,
      payoffTotal: -747.5,
      isMaintain: true,
      maintainInfo: [{
        gameKind: 5,
        startTime: '2024-09-05 04:54:00',
        endTime: '2024-09-05 04:59:00',
        message: 'test'
      }]
    })
  })

  it('should reject when request fails', async () => {
    vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))
    await expect(getEffectiveBetting(params)).rejects.toThrow('Network Error')
  })
})
