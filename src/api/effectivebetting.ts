import apiRequest from '@/utils/apiRequest'
import { IEffectiveBetting, IGetEffectiveBettingParams } from '@/types/effectivebetting'
import dayjs from 'dayjs'

const effectiveBettingTransform = (data: any) => {
  const {
    game_bet_list,
    bet_total,
    payoff_total,
    is_maintain,
    maintain_info
  } = data
  const result = {
    gameBetList: game_bet_list.map((item: any) => ({
      gameKind: item.game_kind,
      bet: item.bet,
      payoff: item.payoff
    })),
    betTotal: bet_total,
    payoffTotal: payoff_total,
    isMaintain: is_maintain,
    maintainInfo: maintain_info.map((item: any) => ({
      gameKind: item.game_kind,
      startTime: dayjs(item.start_time).tz('Etc/GMT+4').format('YYYY-MM-DD HH:mm:ss'),
      endTime: dayjs(item.end_time).tz('Etc/GMT+4').format('YYYY-MM-DD HH:mm:ss'),
      message: item.message
    }))
  } as IEffectiveBetting
  return result
}

export const getEffectiveBetting = async (params: IGetEffectiveBettingParams) => {
  try { 
    const res = await apiRequest.get('/api/account/effective_betting', {params})
    return effectiveBettingTransform(res.data.data)
  } catch (error) {
    return Promise.reject(error)
  }
}
