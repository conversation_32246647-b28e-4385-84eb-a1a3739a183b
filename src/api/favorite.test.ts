import { beforeEach, describe, it, expect, vi } from 'vitest'
import apiRequest from '@/utils/apiRequest'
import { addFavoriteGameList, getFavoriteGameList, removeFavoriteGameList } from '@/api/favorite'

vi.mock('@/utils/apiRequest', () => {
  return {
    default: {
      get: vi.fn(),
      post: vi.fn(),
      delete: vi.fn()
    }
  }
})

describe('getFavoriteGameList', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should return message on success', async () => {
    const mockResponse = {
      data: {
        code: 0,
        message: '',
        data: [1, 2]
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue(mockResponse)

    const params = { hall_id: 6, session_id: 'session_id', game_kind: 5 }
    const result = await getFavoriteGameList(params)

    expect(result).toEqual([1, 2])
    expect(apiRequest.get).toHaveBeenCalledWith('/api/account/favorite_game', { params })
  })

  it('should throw error when request fails', async () => {
    vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))

    const params = { hall_id: 6, session_id: 'session_id', game_kind: 5 }

    await expect(getFavoriteGameList(params)).rejects.toThrow('Network Error')
  })
})

describe('addFavoriteGameList', () => {
  it('should return message on success', async () => {
    const mockResponse = {
      data: {
        code: 0,
        message: ''
      }
    }
    vi.mocked(apiRequest.post).mockResolvedValue(mockResponse)

    const params = { hall_id: 6, session_id: 'session_id', game_kind: 1, game_id: 1 }
    await addFavoriteGameList(params)

    expect(apiRequest.post).toHaveBeenCalledWith('/api/account/favorite_game', params)
  })

  it('should throw error when request fails', async () => {
    vi.mocked(apiRequest.post).mockRejectedValue(new Error('Network Error'))

    const params = { hall_id: 6, session_id: 'session_id', game_kind: 1, game_id: 1 }

    await expect(addFavoriteGameList(params)).rejects.toThrow('Network Error')
  })
})

describe('removeFavoriteGameList', () => {
  it('should return message on success', async () => {
    const mockResponse = {
      data: {
        code: 0,
        message: ''
      }
    }
    vi.mocked(apiRequest.delete).mockResolvedValue(mockResponse)

    const params = { hall_id: 6, session_id: 'session_id', game_kind: 1, game_id: 1 }
    await removeFavoriteGameList(params)

    expect(apiRequest.delete).toHaveBeenCalledWith('/api/account/favorite_game', { params })
  })

  it('should throw error when request fails', async () => {
    vi.mocked(apiRequest.delete).mockRejectedValue(new Error('Network Error'))

    const params = { hall_id: 6, session_id: 'session_id', game_kind: 1, game_id: 1 }

    await expect(removeFavoriteGameList(params)).rejects.toThrow('Network Error')
  })
})