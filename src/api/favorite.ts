import apiRequest from '@/utils/apiRequest'
import type { IFavoriteParams, IGetFavoriteGameListParams } from '@/types/game'

export const getFavoriteGameList = async(params: IGetFavoriteGameListParams) => {
  try {
    const response = await apiRequest.get('/api/account/favorite_game', { params })

    return response.data.data
  } catch (error: any) {
    return Promise.reject(error)
  }
}

export const addFavoriteGameList = async(params: IFavoriteParams) => {
  try {
    await apiRequest.post('/api/account/favorite_game', params)
  } catch (error: any) {
    return Promise.reject(error)
  }
}

export const removeFavoriteGameList = async(params: IFavoriteParams) => {
  try {
    await apiRequest.delete('/api/account/favorite_game', { params })
  } catch (error: any) {
    return Promise.reject(error)
  }
}