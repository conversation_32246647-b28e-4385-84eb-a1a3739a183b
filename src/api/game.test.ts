import { describe, it, expect, vi, beforeEach } from 'vitest'
import apiRequest from '@/utils/apiRequest'
import {
  getCardGameList,
  getCardGameMenu,
  getCasinoGameList,
  getCasinoGameMenu,
  getLiveContentList,
  getLotteryList,
  getLiveLobbyLink,
  getLotteryLobbyLink,
  getSportLobbyLink,
  getCardLobbyLink,
  getLotteryGameList,
  getLiveGameList
} from '@/api/game'
import { testApiEndpoints } from '@/utils/testApiEndpoints'
import { maintainInfo as ImaintainInfo } from '@/types/mcenter'

vi.mock('@/utils/request', () => {
  return {
    default: {
      get: vi.fn(),
    }
  }
})

vi.mock('@/utils/apiRequest', () => {
  return {
    default: {
      get: vi.fn(),
    }
  }
})

vi.mock('@/utils/testApiEndpoints', () => {
  return {
    testApiEndpoints: vi.fn()
  }
})

vi.mock('@/utils/maintaintxt', () => ({
  maintainText: (maintainInfo: ImaintainInfo) => maintainInfo.message
}))

describe('getCasinoGameList', () => {
  it('should return transformed casino game list data', async () => {
    const mockData = {
      status: 'Y',
      data: {
        game_list: [{
          game_id: 1,
          game_kind: 5,
          icon: 'icon',
          link: 'link',
          name: 'name'
        }],
        jackpot_switch: true
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })

    const result = await getCasinoGameList({ session_id: 'session_id', lang: 'en', hall_id: 1, is_mobile: false })
    expect(result).toEqual({
      gameList: [
        {
          gameId: 1,
          name: 'name',
          icon: 'icon',
          link: 'link',
          gameKind: 5
        }
      ],
      jackpotSwitch: true
    })
  })

  it('should reject when request fails', async () => {
    vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))

    await expect(getCasinoGameList({ session_id: 'session_id', lang: 'en', hall_id: 1, is_mobile: false })).rejects.toThrow('Network Error')
  })
})

describe('getCasinoGameMenu', () => {
  it('should return transformed casino game menu data', async () => {
    const mockData = {
      status: 'Y',
      data: {
        game_menu: [{
          id: 1,
          sort: 1,
          name: 'name',
          games: [1],
          game_count: 1
        }]
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })

    const result = await getCasinoGameMenu({ session_id: 'session_id', lang: 'en', hall_id: 1, is_mobile: false })
    expect(result).toEqual([
      {
        id: 1,
        sort: 1,
        name: 'name',
        games: [1],
        gameCount: 1
      }
    ])
  })

  it('should reject when request fails', async () => {
    vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))

    await expect(getCasinoGameMenu({ session_id: 'session_id', lang: 'en', hall_id: 1, is_mobile: false })).rejects.toThrow('Network Error')
  })
})

describe('getCardGameMenu', () => {
  it('should return card game menu data', async () => {
    const mockData = {
      code: 0,
      message: '',
      data: {
        game_menu: [{
          id: 1,
          sort: 1,
          name: 'name',
          games: [],
          game_count: 0,
          game_kind: 66,
        }]
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })

    const result = await getCardGameMenu({ session_id: 'session_id', hall_id: 1, lang: 'en' })
    expect(result).toEqual([
      {
        id: 1,
        sort: 1,
        name: 'name',
        games: [],
        gameCount: 0
      }
    ])
  })

  it('should reject when request fails', async () => {
    vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))

    await expect(getCardGameMenu({ session_id: 'session_id', hall_id: 1, lang: 'en' })).rejects.toThrow('Network Error')
  })
})

describe('getCardGameList', () => {
  it('should return card game list data', async () => {
    const mockData = {
      code: 0,
      message: '',
      data: {
        game_list: [{
          game_id: 1,
          game_kind: 5,
          name: 'name',
          link: 'link',
          icon: 'icon',
          external_id: 'external_id',
        }],
        lang: 'en',
        lobby_entry: 'lobby_entry',
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })

    const result = await getCardGameList({ session_id: 'session_id', hall_id: 1, lang: 'en' })
    expect(result).toEqual({
      gameList: [
        {
          gameId: 1,
          gameKind: 5,
          name: 'name',
          link: 'link',
          icon: 'icon',
          externalId: 'external_id',
        }
      ],
      lang: 'en',
      lobbyEntry: 'lobby_entry',
    })
  })

  it('should reject when request fails', async () => {
    vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))

    await expect(getCardGameList({ session_id: 'session_id', hall_id: 1, lang: 'en' })).rejects.toThrow('Network Error')
  })
})

describe('getCardLobbyLink', () => {
  beforeEach(() => {
    vi.mocked(apiRequest.get).mockClear()
    vi.mocked(testApiEndpoints).mockClear()
  })

  it('should return card lobby link', async () => {
    const mockData = {
      code: 0,
      message: '',
      data: {
        domain: ['a.com', 'b.com'],
        link: '/test.json'
      }
    }

    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })
    vi.mocked(testApiEndpoints).mockResolvedValue({ domain: 'https://b.com', domainPath:'b.com', url: 'https://b.com/test.json' })

    const res = await getCardLobbyLink({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
    })

    expect(res).toEqual({
      link: 'https://b.com/test.json',
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should throw error if all domain fails', async () => {
    const mockData = {
      code: 0,
      message: '',
      data: {
        domain: ['a.com', 'b.com'],
        link: '/test.json'
      }
    }

    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })
    vi.mocked(testApiEndpoints).mockRejectedValue(new Error('Test Error'))

    await expect(
      getCardLobbyLink({
        session_id: '456',
        lang: 'zh-cn',
        hall_id: 123
      })
    ).resolves.toEqual({
      link: '',
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should return maintain data when the response is maintain', async () => {
    const mockData = {
      code: 561000008,
      message: 'maintain',
      data: {
        maintain_info: [
          {
            game_kind: 3,
            start_time: '2024-12-04 03:05:00-04:00',
            end_time: '2024-12-04 03:15:00-04:00',
            message: 'text'
          }
        ]
      }
    }

    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })

    const result = await getCardLobbyLink({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
    })

    expect(result).toEqual({
      maintain: true,
      maintainInfo: 'text'
    })
  })

  it('should reject when request fails', async () => {
    vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))

    await expect(getCardLobbyLink({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
    })).resolves.toEqual({
      link: "",
      maintain: false,
      maintainInfo: "",
    })
  })
})

describe('getLiveContentList', () => {
  it('should return live content list data', async () => {
    const mockData = {
      result: true,
      message: '',
      data: {
        platform_menu: [{ id: '1', name: 'Ultimate Hall' }],
        game_rule_list: [{ id: '1', name: 'Game 1' }]
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })

    const result = await getLiveContentList({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123
    })
    expect(result.platformMenu).toEqual([{ id: '1', name: 'Ultimate Hall' }])
    expect(result.gameRuleList).toEqual([{ id: '1', name: 'Game 1' }])
  })

  it('should reject when request fails', async () => {
    vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))

    await expect(getLiveContentList({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123
    })).rejects.toThrow('Network Error')
  })
})

describe('getLiveLobbyLink', () => {
  beforeEach(() => {
    vi.mocked(apiRequest.get).mockClear()
    vi.mocked(testApiEndpoints).mockClear()
  })

  it('should return live lobby link', async () => {
    const mockData = {
      code: 0,
      message: '',
      data: {
        domain: ['a.com', 'b.com'],
        link: '/test.json'
      }
    }

    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })
    vi.mocked(testApiEndpoints).mockResolvedValue({ domain: 'https://b.com', domainPath: 'b.com', url: 'https://b.com/test.json' })

    const res = await getLiveLobbyLink({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      domain_url: 'http://localhost/',
      is_mobile: false,
      enter_page: 'livedealer'
    })
    expect(res).toEqual({
      link: 'https://b.com/test.json',
      maintain: false,
      maintainInfo: ''
    })
  })

  it('should throw error if all domain fails', async () => {
    const mockData = {
      code: 0,
      message: '',
      data: {
        domain: ['https://a.com', 'https://b.com'],
        link: '/test.json'
      }
    }

    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })
    vi.mocked(testApiEndpoints).mockRejectedValue(new Error('Test Error'))

    await expect(getLiveLobbyLink({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      domain_url: 'http://localhost/',
      is_mobile: false,
      enter_page: 'livedealer'
    })).rejects.toThrow('Test Error')
  })

  it('should return maintain data when the response is maintain', async () => {
    const mockData = {
      code: 561000008,
      message: 'maintain',
      data: {
        maintain_info: [
          {
            game_kind: 3,
            start_time: '2024-12-04 03:05:00-04:00',
            end_time: '2024-12-04 03:15:00-04:00',
            message: 'text'
          }
        ]
      }
    }

    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })

    const result = await getLiveLobbyLink({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      domain_url: 'http://localhost/',
      is_mobile: false,
      enter_page: 'livedealer'
    })

    expect(result).toEqual({
      maintain: true,
      maintainInfo: 'text'
    })
  })

  it('should reject when request fails', async () => {
    vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))

    await expect(getLiveLobbyLink({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      domain_url: 'http://localhost/',
      is_mobile: false,
      enter_page: 'livedealer'
    })).rejects.toThrow('Network Error')
  })
})

describe('getLiveGameList', () => {
  it('should return transformed live game list data', async () => {
    const mockData = {
      code: 0,
      message: '',
      data: [
        {
          id: 3001,
          name: '百家樂'
        }
      ]
    }
    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })

    const result = await getLiveGameList({
      lang: 'zh-cn',
      hall_id: 123
    })
    expect(result).toEqual({
      3001: '百家樂'
    })
  })

  it('should reject when request fails', async () => {
    vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))

    await expect(getLiveGameList({
      lang: 'zh-cn',
      hall_id: 123,
    })).rejects.toThrow('Network Error')
  })
})

describe('getLotteryList', () => {
  it('should return transformed lottery list data', async () => {
    const mockData = {
      code: 0,
      message: '',
      data: {
        game_panel: {
          name: '香港六合彩',
          open_timestamp: 1732248000,
          close_timestamp: 1732248000,
          link: '/balv/bv/pilot?lang=zh-tw&referer_url=%2Fpt%2F..%2Fpantheon%2Favengers%2Favengers%2Forigin%2Fgame%2FHKLT',
          prev_num: '125',
          prev_result: {
            pre_info: [
              {
                key: '01',
                value: 32
              }
            ]
          },
          result_group: 'LT'
        },
        like_guess: [
          {
            platform_name: 'BB彩票',
            platform_key: 'bb_lottery',
            name: 'BB 十分彩',
            link: '/balv/bv/pilot?lang=zh-tw&referer_url=%2Fpt%2F..%2Fpantheon%2Favengers%2Favengers%2Forigin%2Fgame%2FB1SF'
          }
        ],
        official: [
          {
            group_name: '一般彩票',
            group_key: 'NORMAL',
            group_link:
              '/balv/bv/pilot?lang=zh-tw&referer_url=%2Fpantheon%2Favengers%2Favengers%2Ffashion%3Ffcdn%3DNORMAL',
            link: '/balv/bv/pilot?lang=zh-tw&referer_url=%2Fpantheon%2Favengers%2Favengers%2Fofficial%2Fgame%2FBQ3D%3Ftypes%3DBBQL%2CBBLT%2CHKLT%2CBJ3D%2CPL3D%2CB128',
            games: [
              {
                name: 'BB 競速3D',
                game_id: 'BQ3D',
                link: '/balv/bv/pilot?lang=zh-tw&referer_url=%2Fpantheon%2Favengers%2Favengers%2Fofficial%2Fgame%2FBQ3D'
              }
            ]
          }
        ],
        official_on: '1',
        tradition: [
          {
            num: '202411211351',
            game_id: 'B1SF',
            name: 'BB 十分彩',
            link: '/balv/bv/pilot?lang=zh-tw&referer_url=%2Fpt%2F..%2Fpantheon%2Favengers%2Favengers%2Forigin%2Fgame%2FB1SF',
            open_timestamp: 1732242541,
            close_timestamp: 1732242600,
            group: 'DEFAULT',
            tag: 'new'
          }
        ],
        tradition_entrance:
          '/balv/bv/pilot?lang=zh-tw&referer_url=%2Fpantheon%2Favengers%2Favengers%2Ffashion',
        server_timestamp: 1732242558,
        timezone: '-04',
        lt_cdn: 'https://lt-bbgp-qa.cfvn66.com',
        domain: ['bgp.bblotodemo.net']
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })
    vi.mocked(testApiEndpoints).mockResolvedValue({ domain: 'https://bgp.bblotodemo.net', domainPath:'bgp.bblotodemo.net', url: 'https://bgp.bblotodemo.net' })

    const result = await getLotteryList({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      filter: ''
    })
    expect(result).toEqual({
      domainPath: 'bgp.bblotodemo.net',
      ltCdn: 'https://lt-bbgp-qa.cfvn66.com',
      officialOn: '1',
      timezone: '-04',
      traditionEntrance:
        '/balv/bv/pilot?lang=zh-tw&referer_url=%2Fpantheon%2Favengers%2Favengers%2Ffashion',
      serverTimestamp: 1732242558,
      gamePanel: {
        name: '香港六合彩',
        openTimestamp: 1732248000,
        closeTimestamp: 1732248000,
        link: '/balv/bv/pilot?lang=zh-tw&referer_url=%2Fpt%2F..%2Fpantheon%2Favengers%2Favengers%2Forigin%2Fgame%2FHKLT',
        prevNum: '125',
        prevResult: [
          {
            key: '01',
            value: 32
          }
        ],
        resultGroup: 'LT',
        ruleLink: ''
      },
      tradition: [
        {
          num: '202411211351',
          openTimestamp: 1732242541,
          closeTimestamp: 1732242600,
          name: 'BB 十分彩',
          gameId: 'B1SF',
          group: 'DEFAULT',
          link: '/balv/bv/pilot?lang=zh-tw&referer_url=%2Fpt%2F..%2Fpantheon%2Favengers%2Favengers%2Forigin%2Fgame%2FB1SF',
          tag: 'new',
          ruleLink: ''
        }
      ],
      official: [
        {
          groupName: '一般彩票',
          groupKey: 'NORMAL',
          groupLink:
            '/balv/bv/pilot?lang=zh-tw&referer_url=%2Fpantheon%2Favengers%2Favengers%2Ffashion%3Ffcdn%3DNORMAL',
          link: '/balv/bv/pilot?lang=zh-tw&referer_url=%2Fpantheon%2Favengers%2Favengers%2Fofficial%2Fgame%2FBQ3D%3Ftypes%3DBBQL%2CBBLT%2CHKLT%2CBJ3D%2CPL3D%2CB128',
          games: [
            {
              name: 'BB 競速3D',
              gameId: 'BQ3D',
              link: '/balv/bv/pilot?lang=zh-tw&referer_url=%2Fpantheon%2Favengers%2Favengers%2Fofficial%2Fgame%2FBQ3D',
              isRecommand: undefined
            }
          ]
        }
      ],
      likeGuess: [
        {
          name: 'BB 十分彩',
          link: '/balv/bv/pilot?lang=zh-tw&referer_url=%2Fpt%2F..%2Fpantheon%2Favengers%2Favengers%2Forigin%2Fgame%2FB1SF',
          platformKey: 'bb_lottery',
          platformName: 'BB彩票'
        }
      ],
      leaderboard: []
    })
  })

  it('should reject when request fails', async () => {
    vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))

    await expect(getLotteryList({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      filter: ''
    })).rejects.toThrow('Network Error')
  })
})

describe('getLotteryGameList', () => {
  it('should return transformed lottery game list data', async () => {
    const mockData = {
      code: 0,
      message: '',
      data: {
        game_info: [
          {
            id: 'HKLT',
            name: '香港六合彩'
          }
        ]
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })

    const result = await getLotteryGameList({
      lang: 'zh-cn',
      hall_id: 123,
      list_type: 'report'
    })
    expect(result).toEqual({
      'HKLT': '香港六合彩'
    })
  })

  it('should reject when request fails', async () => {
    vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))

    await expect(getLotteryGameList({
      lang: 'zh-cn',
      hall_id: 123,
      list_type: 'report'
    })).rejects.toThrow('Network Error')
  })
})

describe('getLotteryLobbyLink', () => {
  it('should return lottery lobby link', async () => {
    const mockData = {
      code: 0,
      message: '',
      data: {
        domain: ['https://a.com', 'https://b.com'],
        link: '/test.json'
      }
    }

    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })

    const res = await getLotteryLobbyLink({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      domain_url: 'http://localhost/',
      is_mobile: false
    })

    expect(res).toEqual({
      domain: ['https://a.com', 'https://b.com'],
      link: '/test.json'
    })
  })

  it('should reject when the response is maintain', async () => {
    const mockMaintainLiveRecordResponse = {
      data: {
        code: 561000008,
        message: 'maintain',
        data: {
          maintain_info: [
            {
              game_kind: 3,
              start_time: '2024-12-04 03:05:00-04:00',
              end_time: '2024-12-04 03:15:00-04:00',
              message: 'text'
            }
          ]
        }
      }
    }

    vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainLiveRecordResponse)

    await expect(() =>
      getLotteryLobbyLink({
        session_id: '456',
        lang: 'zh-cn',
        hall_id: 123,
        domain_url: 'http://localhost/',
        is_mobile: false
      })
    ).rejects.toThrowError('text')
  })

  it('should reject when request fails', async () => {
    vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))

    await expect(getLotteryLobbyLink({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      domain_url: 'http://localhost/',
      is_mobile: false
    })).rejects.toThrow('Network Error')
  })
})

describe('getSportLobbyLink', () => {
  beforeEach(() => {
    vi.mocked(apiRequest.get).mockClear()
  })

  it('should return sport lobby link', async () => {
    const mockData = {
      code: 0,
      message: '',
      data: {
        link: '/test.json'
      }
    }

    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })

    const res = await getSportLobbyLink({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      domain_url: 'http://localhost/',
      is_mobile: false,
      enter_page: 'esports'
    })
    expect(res).toEqual({
      link: '/test.json',
      maintain: false,
      maintainInfo: "",
    })
  })

  it('should return maintain data when the response is maintain', async () => {
    const mockMaintainLiveRecordResponse = {
      data: {
        code: 561000008,
        message: 'maintain',
        data: {
          maintain_info: [
            {
              game_kind: 3,
              start_time: '2024-12-04 03:05:00-04:00',
              end_time: '2024-12-04 03:15:00-04:00',
              message: 'text'
            }
          ]
        }
      }
    }

    vi.mocked(apiRequest.get).mockResolvedValue(mockMaintainLiveRecordResponse)

    const result = await getSportLobbyLink({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      domain_url: 'http://localhost/',
      is_mobile: false,
      enter_page: 'esports'
    })

    expect(result).toEqual({
      maintain: true,
      maintainInfo: 'text'
    })
  })

  it('should reject when request fails', async () => {
    vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))

    await expect(getSportLobbyLink({
      session_id: '456',
      lang: 'zh-cn',
      hall_id: 123,
      domain_url: 'http://localhost/',
      is_mobile: false,
      enter_page: 'esports'
    })).rejects.toThrow('Network Error')
  })
})