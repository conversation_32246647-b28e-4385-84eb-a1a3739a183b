import apiRequest from '@/utils/apiRequest'
import type {
  IGameMenu,
  IGetGameMenuParams,
  IGetGameList,
  IGetGameListParams,
  IGetSportLobbyLinkParams,
  ILobbyLink,
  IGetCardLobbyLinkParams
} from '@/types/game'
import type {
  ILiveMainSetting,
  IGetLiveLobbyLinkParams,
  IGetLiveGameListParams,
  IGetLiveContentListParams
} from '@/types/live'
import type {
  ILotteryList,
  IGetLotteryListParams,
  IGetLotteryLobbyLinkParams,
  IGetLotteryGameListParams
} from '@/types/lottery'
import { GameType } from "@/types/game"
import { testApiEndpoints } from '@/utils/testApiEndpoints'
import { maintainText } from '@/utils/maintaintxt'

const lotteryListDataTransform = (data: any, domainPath: string) => {
  const {
    game_panel,
    tradition = [],
    official = [],
    like_guess = [],
    leaderboard = [],
  } = data

  return {
    gamePanel: {
      name: game_panel.name,
      openTimestamp: game_panel.open_timestamp,
      closeTimestamp: game_panel.close_timestamp,
      link: game_panel.link,
      prevNum: game_panel.prev_num,
      prevResult: game_panel.prev_result?.pre_info || [],
      resultGroup: game_panel.result_group,
      ruleLink: game_panel.rule_link ? `https://${domainPath}${game_panel.rule_link}` : ''
    },
    likeGuess: like_guess.map((item: any) => ({
      name: item.name,
      link: item.link,
      platformKey: item.platform_key,
      platformName: item.platform_name
    })),
    official: official.map((item: any) => ({
      groupName: item.group_name,
      groupKey: item.group_key,
      groupLink: item.group_link,
      link: item.link,
      games: item.games.map((gameItem: any) => ({
        name: gameItem.name,
        gameId: gameItem.game_id,
        link: gameItem.link,
        isRecommand: gameItem.is_recommand
      }))
    })),
    tradition: tradition.map((item: any) => ({
      num: item.num,
      openTimestamp: item.open_timestamp,
      closeTimestamp: item.close_timestamp,
      name: item.name,
      gameId: item.game_id,
      group: item.group,
      link: item.link,
      tag: item.tag,
      ruleLink: item.rule_link ? `https://${domainPath}${item.rule_link}` : ''
    })),
    leaderboard: leaderboard.map((item: any) => ({
      originProfit: item.origin_profit,
      userName: item.user_name,
      gameName: item.game_name,
      link: item.link,
    }))
  }
}

export const getCasinoGameList = async(params: IGetGameListParams) => {
  try {
    const response = await apiRequest.get('/api/slot/game_lobby_menu_link', { params })

    return {
      gameList: response.data.data.game_list.map((item: any) => ({
        gameId: item.game_id,
        gameKind: item.game_kind,
        name: item.name,
        link: item.link,
        demoLink: item.demo_link,
        icon: item.icon,
      })),
      jackpotSwitch: response.data.data.jackpot_switch,
    } as IGetGameList
  } catch (error: any) {
    return Promise.reject(error)
  }
}

export const getCasinoGameMenu = async(params: IGetGameMenuParams) => {
  try {
    const response = await apiRequest.get('/api/slot/game_lobby_menu', { params })

    return response.data.data.game_menu.map((item: any) => ({
      id: item.id,
      sort: item.sort,
      name: item.name,
      games: item.games,
      gameCount: item.game_count,
      lower: item.lower
    })) as IGameMenu[]
  } catch (error: any) {
    return Promise.reject(error)
  }
}

export const getCardGameMenu = async(params: IGetGameMenuParams) => {
  try {
    const response = await apiRequest.get('/api/battle/game_lobby_menu', { params })

    return response.data.data.game_menu.map((item: any) => ({
      id: item.id,
      sort: item.sort,
      name: item.name,
      games: item.games,
      gameCount: item.game_count,
    })) as IGameMenu[]
  } catch (error: any) {
    return Promise.reject(error)
  }
}

export const getCardGameList = async(params: IGetGameListParams) => {
  try {
    const response = await apiRequest.get('/api/battle/game_lobby_menu_link', { params })

    return {
      gameList: response.data.data.game_list.map((item: any) => ({
        gameId: item.game_id,
        gameKind: item.game_kind,
        name: item.name,
        link: item.link,
        icon: item.icon,
        externalId: item.external_id,
      })),
      lang: response.data.data.lang,
      lobbyEntry: response.data.data.lobby_entry,
    } as IGetGameList
  } catch (error: any) {
    return Promise.reject(error)
  }
}

export const getCardLobbyLink = async(params: IGetCardLobbyLinkParams) => {
  try {
    const res = await apiRequest.get('/api/battle/lobby_link', { params })
    const resData = res.data
    let result = undefined

    if (resData.code == 561000008) {
      result = {
        maintain:  true,
        maintainInfo: maintainText({
          ...resData.data.maintain_info[0],
          isHtml: false
        })
      }
    } else {
      const testResult = await testApiEndpoints({
        ...resData.data,
        gameType: GameType.Battle
      })
      
      result =  {
        link: testResult.url,
        maintain: false,
        maintainInfo: ''
      } 
    }
    return result as ILobbyLink
  } catch {
    return {
      link: '',
      maintain: false,
      maintainInfo: ''
    } as ILobbyLink
  }
}

export const getLiveContentList = async(params: IGetLiveContentListParams) => {
  try {
    const response = await apiRequest.get('/api/live/main_setting', { params })

    const responseData = response.data

    return {
      platformMenu: responseData.data.platform_menu,
      gameRuleList: responseData.data.game_rule_list,
    } as ILiveMainSetting
  } catch (error: any) {
    return Promise.reject(error)
  }
}

export const getLiveLobbyLink = async(params: IGetLiveLobbyLinkParams) => {
  try {
    const res = await apiRequest.get('/api/live/lobby_link', { params })
    const resData = res.data
    let result = undefined

    if (resData.code == 561000008) {
      result = {
        maintain: true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else { 
      const testResult = await testApiEndpoints({
        ...resData.data,
        gameType: GameType.BBlive
      })
      
      result =  {
        link: testResult.url,
        maintain: false,
        maintainInfo: ''
      } 
    }
    return result as ILobbyLink
  } catch (error: any) {
    return Promise.reject(error)
  }
}

export const getLiveGameList = async(params: IGetLiveGameListParams) => {
  try {
    const res = await apiRequest.get('/api/live/game_list', { params })
    const gameList = res.data.data
    return gameList.reduce((acc: { [key: string]: string }, item: { id: string, name: string }) => {
      acc[item.id] = item.name
      return acc
    }, {})
  } catch (error: any) {
    return Promise.reject(error)
  }
}

export const getLotteryList = async(params: IGetLotteryListParams) => {
  try {
    const response = await apiRequest.get('/api/lottery/game_lobby_menu_link', { params })
    const responseData = response.data
    const {
      official_on,
      tradition_entrance,
      server_timestamp,
      timezone,
      lt_cdn,
      domain
    } = responseData.data

    const testResult = await testApiEndpoints({
      domain,
      link: '',
      gameType: GameType.Lottery
    })

    const {
      gamePanel,
      tradition,
      official,
      likeGuess,
      leaderboard
    } = lotteryListDataTransform(responseData.data, testResult.domainPath)

    return {
      domainPath: testResult.domainPath,
      ltCdn: lt_cdn,
      officialOn: official_on,
      timezone,
      traditionEntrance: tradition_entrance,
      serverTimestamp: server_timestamp,
      gamePanel,
      tradition,
      official,
      likeGuess,
      leaderboard
    } as ILotteryList
  } catch (error: any) {
    return Promise.reject(error)
  }
}

export const getLotteryLobbyLink = async(params: IGetLotteryLobbyLinkParams) => {
  try {
    const res = await apiRequest.get('/api/lottery/lobby_link', { params })
    const resData = res.data

    if (resData.code == 561000008) {
      throw new Error(maintainText({
        ...resData.data.maintain_info[0],
        isHtml: false
      }))
    }
      
    return resData.data
  } catch (error: any) {
    return Promise.reject(error)
  }
}

export const getSportLobbyLink = async(params: IGetSportLobbyLinkParams) => {
  try {
    const res = await apiRequest.get('/api/sport/lobby_link', { params })
    const resData = res.data
    let result = undefined

    if (resData.code == 561000008) {
      result = {
        maintain:  true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else {
      result =  {
        link: resData.data.link,
        maintain: false,
        maintainInfo: ''
      } 
    }

    return result as ILobbyLink
  } catch (error: any) {
    return Promise.reject(error)
  }
}

export const getLotteryGameList = async(params: IGetLotteryGameListParams) => {
  try {
    const res = await apiRequest.get('/api/lottery/game_list', { params })
    const { game_info = [] } = res.data.data
    return game_info.reduce((acc: { [key: string]: string }, item: { id: string, name: string }) => {
      acc[item.id] = item.name
      return acc
    }, {})
  } catch (error: any) {
    return Promise.reject(error)
  }
}