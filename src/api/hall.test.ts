import apiRequest from '@/utils/apiRequest';
import { describe, it, expect, vi } from 'vitest'
import { getHallInfo } from './hall';

vi.mock('@/utils/apiRequest', () => {
  return {
    default: {
      get: vi.fn(),
    }
  }
})

describe('getHallInfo', () => {
  it('should return transformed data when request is successful', async () => {
    const mockData = {
      code: 0,
      message: '',
      data: {
        hall_id: 3820474,
        website: 'bbinbgp',
        name: 'BGP API 測試廳',
        login_code: 'bgp',
        enable: true
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })

    const result = await getHallInfo()

    expect(result).toEqual({
      hallId: 3820474,
      website: 'bbinbgp',
      name: 'BGP API 測試廳',
      loginCode: 'bgp',
      enable: true
    })
  })

  it('should reject when request fails', async () => {
    vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))

    await expect(getHallInfo()).rejects.toThrow('Network Error')
  })
})
