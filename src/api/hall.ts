import apiRequest from '@/utils/apiRequest'
import { HallInfo } from '@/types/hall'

export const getHallInfo = async() => {
  try {
    const res = await apiRequest.get('/api/hall/info')
    const resData = res.data

    return {
      hallId: resData.data.hall_id,
      website: resData.data.website,
      name: resData.data.name,
      loginCode: resData.data.login_code,
      enable: resData.data.enable
    } as HallInfo
  } catch (error: any) {
    return Promise.reject(error)
  }
}
