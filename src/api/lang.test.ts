import apiRequest from '@/utils/apiRequest';
import { describe, it, expect, vi } from 'vitest'
import { getLangList } from './lang';

vi.mock('@/utils/apiRequest', () => {
  return {
    default: {
      get: vi.fn(),
    }
  }
})

describe('getLangList', () => {
  it('should return transformed data when request is successful', async () => {
    const mockData = {
      code: 0,
      message: '',
      data: [
        {
          index: 'S_ACCOUNT_INQUIRY',
          translated: '往來記錄'
        },
        {
          index: 'S_ACOU',
          translated: '帳 號'
        }
      ]
    }
    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })

    const result = await getLangList({ lang: 'zh-cn' })

    expect(result).toEqual({
      'S_ACCOUNT_INQUIRY': '往來記錄',
      'S_ACOU': '帳 號'
    })
  })

  it('should reject when request fails', async () => {
    vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))

    await expect(getLangList({ lang: 'zh-cn' })).rejects.toThrow('Network Error')
  })
})
