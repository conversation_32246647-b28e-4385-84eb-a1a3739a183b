import apiRequest from '@/utils/apiRequest'
import type { LangData } from '@/types/lang'
import type { LangDict } from '@/types/dict'

const langListTransform = (langData: LangData[]) => {
  const result = langData.reduce((acc, data) => {
    acc[data.index] = data.translated
    return acc
  }, {} as LangDict)

  return result
}

export const getLangList = async(params: { lang: string }) => {
  try {
    const res = await apiRequest.get('/api/lang/list', { params })
    const resData = res.data

    return langListTransform(resData.data)
  } catch (error: any) {
    return Promise.reject(error)
  }
}
