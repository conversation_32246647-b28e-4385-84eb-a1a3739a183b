import apiRequest from '@/utils/apiRequest'
import { describe, it, expect, vi } from 'vitest'
import { getLobbySwitch } from './lobbyswitch'
import { maintainInfo as IMaintainInfo } from '@/types/mcenter'


vi.mock('@/utils/apiRequest', () => {
  return {
    default: {
      get: vi.fn(),
    }
  }
})

vi.mock('@/utils/maintaintxt', () => ({
  maintainText: (maintainInfo: IMaintainInfo) => maintainInfo.message
}))

describe('getLobbySwitch', () => {
  it('should return transformed data when request is successful', async () => {
    const mockData = {
      code: 0,
      message: '',
      data: [
        {
          game_kind: 3,
          switch: true,
          is_maintaining: true,
          maintain_info: {
            game_kind: 3,
            start_time: '2025-04-07T16:50:00+0800',
            end_time: '2025-04-07T16:55:00+0800',
            message: '全產品維護測試'
          }
        },
        {
          game_kind: 5,
          switch: true,
          is_maintaining: true,
          maintain_info: {
            game_kind: 5,
            start_time: '2025-04-07T16:50:00+0800',
            end_time: '2025-04-07T16:56:00+0800',
            message: '全產品維護測試'
          }
        }
      ]
    }
    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })

    const result = await getLobbySwitch({ hall_id: 123, session_id: '456' })

    expect(result).toEqual({
      lobbySwitch: {
        3: true,
        5: true
      },
      maintainInfo: {
        card: {
          maintain: false,
          message: ''
        },
        casino: {
          maintain: true,
          message: '全產品維護測試'
        },
        fish: {
          maintain: false,
          message: ''
        },
        live: {
          maintain: true,
          message: '全產品維護測試'
        },
        lottery: {
          maintain: false,
          message: ''
        },
        sport: {
          maintain: false,
          message: ''
        }
      }
    })
  })

  it('should reject when request fails', async () => {
    vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))

    await expect(getLobbySwitch({ hall_id: 123, session_id: '456' })).rejects.toThrow('Network Error')
  })
})
