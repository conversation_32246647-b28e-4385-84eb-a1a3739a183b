import apiRequest from '@/utils/apiRequest'
import type { ILobbySwitch, IGetLobbySwitchParams } from '@/types/lobbyswitch'
import { maintainText } from '@/utils/maintaintxt'

// 使用 'as const' 進行更嚴格的類型定義，以便推導出更精確的字面量類型
const gameKindNameMap = {
  3: 'live',
  5: 'casino',
  12: 'lottery',
  31: 'sport',
  38: 'fish',
  66: 'card'
} as const;

type GameKindNumeric = keyof typeof gameKindNameMap;

interface ILobbyItem {
  game_kind: GameKindNumeric;
  is_maintaining: boolean;
  maintain_info: any;
  switch: boolean;
}

const lobbySwitchTransForm = (data: ILobbyItem[] = []): ILobbySwitch => {
  const defaultGameMaintain = {
    maintain: false,
    message: ''
  }

  return data.reduce((acc: ILobbySwitch, item: ILobbyItem) => {
    const {
      game_kind,
      is_maintaining,
      maintain_info,
      switch: itemSwitch
    } = item;

    const gameName = gameKindNameMap[game_kind];

    acc.lobbySwitch[game_kind] = itemSwitch;
    acc.maintainInfo[gameName] = {
      maintain: is_maintaining,
      message: is_maintaining ? maintainText(maintain_info) : ''
    };

    return acc;
  }, {
    lobbySwitch: {},
    maintainInfo: {
      live: { ...defaultGameMaintain },
      casino: { ...defaultGameMaintain },
      lottery: { ...defaultGameMaintain },
      sport: { ...defaultGameMaintain },
      fish: { ...defaultGameMaintain },
      card: { ...defaultGameMaintain }
    }
  } as ILobbySwitch);
};

export const getLobbySwitch = async(params: IGetLobbySwitchParams) => {
  try {
    const res = await apiRequest.get('/api/account/lobby_switch', { params })
    const resData = res.data
    return lobbySwitchTransForm(resData.data)
  } catch (error: any) {
    return Promise.reject(error)
  }
}
