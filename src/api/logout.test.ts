import { describe, it, expect, vi } from 'vitest'
import { logout } from './logout'
import apiRequest from '@/utils/apiRequest'

vi.mock('@/utils/apiRequest', () => {
  return {
    default: {
      delete: vi.fn(),
    }
  }
})

describe('logout.ts', () => {
  it('success', async () => {
    const params = { hall_id: 123, session_id: '456' }
    vi.mocked(apiRequest.delete).mockResolvedValueOnce({
      data: { code: 0, message: 'Logout successful' }
    })

    const response = await logout(params)

    expect(apiRequest.delete).toHaveBeenCalledWith('/api/account/logout', { params })
    expect(response).toEqual({ code: 0, message: 'Logout successful' })
  })

  it('error', async () => {
    const params = { hall_id: 123, session_id: '456' }
    vi.mocked(apiRequest.delete).mockRejectedValueOnce(new Error('Request failed'))

    await expect(logout(params)).rejects.toThrow('Request failed')
  })
})