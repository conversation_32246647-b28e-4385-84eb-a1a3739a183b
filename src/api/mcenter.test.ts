import apiRequest from '@/utils/apiRequest'
import { describe, it, expect, vi } from 'vitest'
import { getLiveNews, getLiveGameCode, getCasinoNews, getLotteryNews } from '@/api/mcenter'
import { ILotteryNews, ICasinoNews, ILiveNews, ILiveGameCodeList } from '@/types/gamenews'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

dayjs.extend(timezone)
dayjs.extend(utc)

vi.mock('@/utils/request', () => {
  return {
    default: {
      get: vi.fn(),
    }
  }
})

vi.mock('@/utils/apiRequest', () => {
  return {
    default: {
      get: vi.fn(),
    }
  }
})

vi.mock('@/utils/maintaintxt', () => ({
  maintainText: () => {
    return 'maintain text'
  }
}))

describe('getLiveNews', () => {
  it('getLiveNews should return live news data', async () => {
    const mockData = {
      code: 0,
      message: '',
      data: {
        start_time: '2024-11-12T00:00:00-04:00',
        bulletin: [
          {
            date: '2024-11-11 23:17:21',
            content:
              '敬請客戶留意!! 【 香港時間 2024-11-12 遊戲： 百家樂 桌號： AS5 局號： 324061319 】因系統異常，本牌局將作廢。下注金額將會退還，本遊戲將會開始新局，不便之處敬請見諒。',
            game_id: 3001,
            table_code: 86
          },
          {
            date: '2024-11-11 18:06:30',
            content:
              '敬請客戶留意!! 【 香港時間 2024-11-12 遊戲： 百家樂 桌號： AS5 局號： 324046260 】因系統異常，本牌局將作廢。下注金額將會退還，本遊戲將會開始新局，不便之處敬請見諒。',
            game_id: 3001,
            table_code: 86
          }
        ]
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })
    
    const result = await getLiveNews({ session_id: '123', hall_id: 456, lang: 'zh-cn' }) as ILiveNews
    expect(result.data?.[0].dateTime).toBe('2024-11-11 23:17:21')
  })

  it('getLiveNews should return maintain info', async () => {
    const mockData = {
      code: 561000008,
      data: {
        maintain_info: [
          {
            game_kind: 3,
            start_time: '2024-09-05T04:54:00-04:00',
            end_time: '2024-09-05T04:59:00-04:00',
            message: 'test'
          }
        ]
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })
    
    const result = await getLiveNews({ session_id: '123', hall_id: 456, lang: 'zh-cn' })
    expect(result.maintain).toBe(true)
    expect(result.maintainInfo).toBe('maintain text')
  })

  it('should reject when request fails', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)
    vi.spyOn(console, 'log').mockImplementation(vi.fn())

    await expect(getLiveNews({ session_id: '123', hall_id: 456, lang: 'zh-cn'  })).rejects.toThrow('Network Error')
    expect(console.log).toHaveBeenCalledWith(mockError)
  })
})

describe('getLiveGameCode', () => {
  it('getLiveGameCode should return game code data', async () => {
    const mockData = {
      code: 0,
      message: '',
      data: [
        {
          game_id: 3001,
          table_code: 1,
          table_name: 'A'
        }
      ]
    }
    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })
    
    const result = await getLiveGameCode({ hall_id: 456 }) as ILiveGameCodeList
    expect(result).toEqual({
      gameId: { '3001': 3001 },
      tableCode: { '3001': { '1': 'A' } }
    })
  })

  it('should reject when request fails', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)
    vi.spyOn(console, 'log').mockImplementation(vi.fn())

    await expect(getLiveGameCode({ hall_id: 456  })).rejects.toThrow('Network Error')
    expect(console.log).toHaveBeenCalledWith(mockError)
  })
})

describe('getCasinoNews', () => {
  it('getCasinoNews should return casino news data', async () => {
    const mockData = {
      code: 0,
      message: '',
      data: {
        start_time: '2024-11-09T00:00:00-04:00',
        bulletin: [
          {
            date: '2024-11-15 02:26:59',
            content: 'Gura \r\nsay\r\nA'
          }
        ]
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })
    
    const result = await getCasinoNews({
      session_id: '456',
      hall_id: 123,
      lang: 'zh-cn',
      date: '2024-11-15'
    }) as ICasinoNews
    expect(result.data?.[0].dateTime).toBe('2024-11-15 02:26:59')
  })

  it('getCasinoNews should return maintain info', async () => {
    const mockData = {
      code: 561000008,
      data: {
        maintain_info: [
          {
            game_kind: 3,
            start_time: '2024-09-05T04:54:00-04:00',
            end_time: '2024-09-05T04:59:00-04:00',
            message: 'test'
          }
        ]
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })
    
    const result = await getCasinoNews({
      session_id: '456',
      hall_id: 123,
      lang: 'zh-cn',
      date: '2023-01-01'
    })
    expect(result.maintain).toBe(true)
    expect(result.maintainInfo).toBe('maintain text')
  })

  it('should reject when request fails', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    await expect(getCasinoNews({
      session_id: '456',
      hall_id: 123,
      lang: 'zh-cn',
      date: '2023-01-01'
    })).rejects.toThrow('Network Error')
    expect(console.error).toHaveBeenCalledWith(mockError)
  })
})

describe('getLotteryNews', () => {
  it('getLotteryNews should return lottery news data', async () => {
    const mockData = {
      status: 'Y',
      data: [{ datetime: '2023-01-01', contents: 'Test Content' }]
    }
    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })
    
    const result = await getLotteryNews({
      session_id: '456',
      hall_id: 123,
      lang: 'zh-cn',
      category: 2
    }) as ILotteryNews
    expect(result.data?.[0].dateTime).toBe('2023-01-01')
  })

  it('getLotteryNews should return maintain info', async () => {
    const mockData = {
      code: 561000008,
      message: 'Game is under maintenance',
      data: {
        maintain_info: [
          {
            game_kind: 3,
            start_time: '2024-09-05T04:54:00-04:00',
            end_time: '2024-09-05T04:59:00-04:00',
            message: 'test'
          }
        ]
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })
    
    const result = await getLotteryNews({
      session_id: '456',
      hall_id: 123,
      lang: 'zh-cn',
      category: 2
    })
    expect(result.maintain).toBe(true)
    expect(result.maintainInfo).toBe('maintain text')
  })

  it('should reject when request fails', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    await expect(getLotteryNews({
      session_id: '456',
      hall_id: 123,
      lang: 'zh-cn',
      category: 2
    })).rejects.toThrow('Network Error')
    expect(console.error).toHaveBeenCalledWith(mockError)
  })
})