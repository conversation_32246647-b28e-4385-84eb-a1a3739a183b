import { maintainText } from '@/utils/maintaintxt'
import { 
  IGetLotteryNewsParams,
  ILotteryNews,
  IGetCasinoNewsParams,
  ICasinoNews,
  IGetLiveGameCodeParams,
  IGetLiveNewsParams,
  ILiveGameCodeList,
  ILiveNews
 } from '@/types/gamenews'
import apiRequest from '@/utils/apiRequest';
import dayjs from 'dayjs'

// BB Live
export const getLiveNews = async (params : IGetLiveNewsParams) => {
  try {
    const res = await apiRequest.get('/api/live/bulletin_list', { params })
    const resData = res.data
    let result = undefined

    // 遊戲維護中
    if (resData.code == 561000008 ) {
      result = {
        maintain:  true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else {
      result =  {
        startTime: dayjs(resData.data.start_time).tz('Etc/GMT+4').format('YYYY-MM-DD HH:mm:ss'),
        data: resData.data.bulletin?.map((item: any) => ({
          dateTime: item.date,
          content: item.content,
          gameId: item.game_id,
          tableCode: item.table_code
        })) || [],
        maintain: false,
        maintainInfo: ''
      } 
    }

    return result as ILiveNews
  } catch (error: any) {
    console.log(error)
    return Promise.reject(error)
  }
}

export const getLiveGameCode = async (params : IGetLiveGameCodeParams) => {
  try {
    const res = await apiRequest.get('/api/live/game_code_list', { params })
    const resData = res.data
    return resData.data.reduce((acc: any, item: any) => {
      acc.gameId[item.game_id] = item.game_id

      if (acc.tableCode[item.game_id]) {
        acc.tableCode[item.game_id][item.table_code] = item.table_name
      } else {
        acc.tableCode[item.game_id] = { [item.table_code]: item.table_name }
      }
      return acc
    }, { gameId: {}, tableCode: {} }) as ILiveGameCodeList 
  } catch (error: any) {
    console.log(error)
    return Promise.reject(error)
  }
}

// BB Casino
export const getCasinoNews = async (params : IGetCasinoNewsParams) => {
  try {
    const res = await apiRequest.get('/api/slot/bulletin_list', { params })
    const resData = res.data
    let result = undefined

    // 遊戲維護中
    if (resData.code == 561000008 ) {
      result = {
        maintain:  true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else {
      result =  {
        startTime: dayjs(resData.data.start_time).tz('Etc/GMT+4').format('YYYY-MM-DD HH:mm:ss'),
        data: resData.data.bulletin?.map((item: any) => ({
          dateTime: item.date,
          content: item.content
        })) || [],
        maintain: false,
        maintainInfo: ''
      } 
    }

    return result as ICasinoNews
  } catch (error: any) {
    console.error(error)
    return Promise.reject(error)
  }
}

// BB Lottery
export const getLotteryNews = async (params : IGetLotteryNewsParams) => {
  try {
    const res = await apiRequest.get('/api/lottery/bulletin_list', { params })
    const resData = res.data
    let result = undefined

    // 遊戲維護中
    if (resData.code == 561000008 ) {
      result = {
        maintain:  true,
        maintainInfo: maintainText(resData.data.maintain_info[0])
      }
    } else {
      result =  {
        data: resData.data?.map((item: any) => ({
          dateTime: item.datetime,
          content: item.game_name ? `[${item.game_name}] ${item.content}` : item.content
        })) || [],
        maintain: false,
        maintainInfo: ''
      } 
    }

    return result as ILotteryNews
  } catch (error: any) {
    console.error(error)
    return Promise.reject(error)
  }
}

