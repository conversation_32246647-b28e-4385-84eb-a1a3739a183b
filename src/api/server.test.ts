import apiRequest from '@/utils/apiRequest'
import { describe, it, expect, vi } from 'vitest'
import { getServerInfo } from './server'

vi.mock('@/utils/apiRequest', () => {
  return {
    default: {
      get: vi.fn(),
    }
  }
})

describe('getServerInfo', () => {
  it('should return transformed data when request is successful', async () => {
    const mockData = {
      code: 0,
      message: '',
      data: {
        server_info: {
          static_file_url: ''
        },
        google_analytics: {
          event: 'custom_pageview',
          user_id: '2958L82',
          hall_id: '',
          game_id: ''
        }
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockData })

    const result = await getServerInfo({ hall_id: 123, session_id: '456' })

    expect(result).toEqual({
      serverInfo: {
        staticFileUrl: ''
      },
      googleAnalytics: {
        event: 'custom_pageview',
        userId: '2958L82',
        hallId: '',
        gameId: ''
      }
    })
  })

  it('should reject when request fails', async () => {
    vi.mocked(apiRequest.get).mockRejectedValue(new Error('Network Error'))

    await expect(getServerInfo({ hall_id: 123, session_id: '456' })).rejects.toThrow('Network Error')
  })
})
