import apiRequest from '@/utils/apiRequest'
import type { IServerInfo, IGetServerInfoParams } from '@/types/server'

const serverInfoTransform = (data: any) => {
  const {
    server_info,
    google_analytics
  } = data

  return {
    serverInfo: {
      staticFileUrl: server_info.static_file_url
    },
    googleAnalytics: {
      event: google_analytics.event,
      userId: google_analytics.user_id,
      hallId: google_analytics.hall_id,
      gameId: google_analytics.game_id
    }
  } as IServerInfo
}

export const getServerInfo = async(params: IGetServerInfoParams) => {
  try {
    const res = await apiRequest.get('/api/server/info', { params })
    const resData = res.data
    return serverInfoTransform(resData.data)
  } catch (error: any) {
    return Promise.reject(error)
  }
}
