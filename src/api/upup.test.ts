import { describe, it, expect, vi } from 'vitest'
import apiRequest from '@/utils/apiRequest'
import { upUp } from '@/api/upup'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

dayjs.extend(timezone)
dayjs.extend(utc)

vi.mock('@/utils/apiRequest', () => ({
  default: {
    get: vi.fn(),
  },
}))

vi.mock('@/utils/apiUrl', () => ({
  useApiUrl: vi.fn(() => ''),
}))

describe('upup', () => {
  const params = { hall_id: 123 }

  it('should return transformed data when request is successful', async () => {
    const mockResponseData = {
      data: {
        is_maintain: true,
        upup_end_time: '2025-04-07T23:00:00-04:00', // 假設下午 2 點
        customer_service_online: 'S_CUSTOMER_SERVICE_ONLINE',
        web_upup: 'S_WEB_UPUP',
        upup_notice: 'S_UPUP_NOTICE',
        upup_content: 'S_UPUP_CONTENT'
      }
    }
    vi.mocked(apiRequest.get).mockResolvedValue({ data: mockResponseData })

    const result = await upUp(params)

    expect(result).toEqual({
      isMaintain: true,
      upupEndTime: '2025-04-07 23:00:00',
      customerServiceOnline: 'S_CUSTOMER_SERVICE_ONLINE',
      webUpup: 'S_WEB_UPUP',
      upupNotice: 'S_UPUP_NOTICE',
      upupContent: 'S_UPUP_CONTENT',
      hours: 11,
    })
  })

  it('should reject when request fails', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)
    await expect(upUp(params)).rejects.toThrow('Network Error')
  })
})