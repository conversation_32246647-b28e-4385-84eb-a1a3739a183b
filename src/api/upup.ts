import apiRequest from '@/utils/apiRequest';
import { IUpup, IUpupParams } from '@/types/upup'
import { useApiUrl } from '@/utils/apiUrl'
import dayjs from 'dayjs'

export const upUp = async (params: IUpupParams) => {
  try {
    const apiUrl = useApiUrl()
    const res = await apiRequest.get(`${apiUrl.value}/api/entrance/upup`, { params })
    let result =  {
      isMaintain: false,
      upupEndTime: '',
      customerServiceOnline: '',
      webUpup: '',
      upupNotice: '',
      upupContent: '',
      hours: 0
    }

    if (res.data.data) {
      const {
        is_maintain,
        upup_end_time,
        customer_service_online,
        web_upup,
        upup_notice,
        upup_content
      } = res.data.data

      result = {
        isMaintain: is_maintain,
        customerServiceOnline: customer_service_online,
        webUpup: web_upup,
        upupNotice: upup_notice,
        upupContent: upup_content,
        upupEndTime: '',
        hours: 12
      }

      if (upup_end_time) {
        const endTime = dayjs(upup_end_time).tz('Etc/GMT+4')

        result = {
          ...result,
          upupEndTime: endTime.format('YYYY-MM-DD HH:mm:ss'),
          hours: (endTime.get('hour') % 12) || 12
        }
      }
    }

    return result as IUpup
    
  } catch (error) {
    return Promise.reject(error)
  }
}