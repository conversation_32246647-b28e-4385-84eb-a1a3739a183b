<script setup lang="ts">
import { RouterView } from 'vue-router'
import { ElConfigProvider } from 'element-plus'
import { useRouter } from 'vue-router'

const isLoading = ref(true)
const router = useRouter()

router.beforeEach((to) => {
  isLoading.value = true
  if (to.meta.title) {
    document.title = to.meta.title as string
  }
})

router.afterEach(() => {
  setTimeout(() => {
    isLoading.value = false
  }, 500)
})
</script>

<template>
  <el-config-provider size="default" :z-index="3000">
    <div
      v-loading.fullscreen.lock="isLoading"
      element-loading-text="Loading..."
      :element-loading-spinner="false"
    >
      <Suspense>
        <RouterView />
      </Suspense>
    </div>
  </el-config-provider>
</template>

<style lang="scss" scoped>
</style>
