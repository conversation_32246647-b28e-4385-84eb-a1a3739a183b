import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import BackToTop from './BackToTop.vue'

describe('BackToTop', () => {
  it('should render correctly', async () => {
    const wrapper = mount(BackToTop)
    const elBacktopComponent = wrapper.findComponent({ name: 'ElBacktop' })
    const caretTopIcon = wrapper.findComponent({ name: 'CaretTop' })
    
    expect(elBacktopComponent.exists()).toBe(true)
    expect(elBacktopComponent.props('right')).toBe(15)
    expect(elBacktopComponent.props('bottom')).toBe(70)
    expect(caretTopIcon.exists()).toBe(false)
  })
})