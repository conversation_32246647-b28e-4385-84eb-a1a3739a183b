<script setup lang="ts">
import { CaretTop } from '@element-plus/icons-vue'
</script>

<template>
  <el-backtop :right="15" :bottom="70">
    <div class="backtop-icon">
      <CaretTop />
    </div>
  </el-backtop>
</template>

<style lang="scss" scoped>
.el-backtop {
  width: 60px;
  height: 60px;
  transition: all 1s;
  &:hover {
    background: rgba(#bbb, 0.1);
  }
}

.backtop-icon {
  height: 50%;
  width: 50%;
  text-align: center;
  line-height: 30px;
  color: var(--main);
  &:hover {
    transform: translate(0%, 3%);
  }
}
</style>