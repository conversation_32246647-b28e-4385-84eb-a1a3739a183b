import { mount, VueWrapper } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import GamePic from './GamePic.vue'
import { ElImage } from 'element-plus'

const gamePicUrl = 'path/to/image.jpg'
const failedPicUrl = 'path/to/failed-image.jpg'
const customClass = 'my-custom-pic-class'

describe('GamePic', () => {
  let wrapper: VueWrapper<any>

  const createWrapper = (props: any) => {
    return mount(GamePic, {
      props,
    })
  }

  it('should render the ElImage component with the correct gamePic src prop', () => {
    wrapper = createWrapper({ gamePic: gamePicUrl })
    const elImage = wrapper.findComponent(ElImage)

    expect(elImage.exists()).toBe(true)
    expect(elImage.props('src')).toBe('path/to/image.jpg')
  })

  it('should initially display a loading indicator', () => {
    wrapper = createWrapper({ gamePic: gamePicUrl })

    expect(wrapper.find('.el-loading-mask').exists()).toBe(true)
  })

  it('should isLoading is false when the image loads successfully', async () => {
    wrapper = createWrapper({ gamePic: gamePicUrl })
    const elImage = wrapper.findComponent(ElImage)

    expect(wrapper.vm.isLoading).toBe(true)

    await elImage.vm.$emit('load', new Event('load'))

    expect(wrapper.vm.isLoading).toBe(false)
  })

  it('should isLoading is false when the image fails to load', async () => {
    wrapper = createWrapper({ gamePic: gamePicUrl, failedPic: failedPicUrl })
    const elImage = wrapper.findComponent(ElImage)

    expect(wrapper.vm.isLoading).toBe(true) 

    await elImage.vm.$emit('error', new Event('error'))

    expect(wrapper.vm.isLoading).toBe(false)
  })

  it('should apply the provided class to the ElImage component', () => {
    wrapper = createWrapper({ gamePic: gamePicUrl, class: customClass })
    const elImage = wrapper.findComponent(ElImage)
    
    expect(elImage.classes()).toContain(customClass)
  })

  it('should pass down loading="lazy" and fit="cover" to ElImage', () => {
    wrapper = createWrapper({ gamePic: gamePicUrl })
    const elImage = wrapper.findComponent(ElImage)

    expect(elImage.props('loading')).toBe('lazy')
    expect(elImage.props('fit')).toBe('cover')
  })
})
