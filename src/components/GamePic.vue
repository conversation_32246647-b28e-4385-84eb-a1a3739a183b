<script setup lang="ts">
const props = defineProps({
  gamePic: {
    type: String,
    required: true,
    default: ''
  },
  failedPic: {
    type: String,
    required: false,
    default: ''
  },
  class: {
    type: String,
    required: false,
    default: ''
  }
})

const isLoading = ref(true)
</script>

<template>
  <el-image
    v-loading="isLoading"
    :src="props.gamePic"
    :class="props.class"
    loading="lazy"
    fit="cover"
    @load="isLoading = false"
    @error="isLoading = false"
  >
    <template #error>
      <img :src="props.failedPic" class="el-image__inner"/>
    </template>
  </el-image>
</template>
