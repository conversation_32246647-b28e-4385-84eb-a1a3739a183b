import { describe, expect, it, vi } from 'vitest'

import LiveMenu from './LiveMenu.vue'
import { createI18n } from 'vue-i18n'
import { mount } from '@vue/test-utils'

const i18n = createI18n({
  locale: 'en',
  messages: {
    en: {},
    'zh-tw': {},
  },
})

vi.mock('@/api/game', () => ({
  getLiveLobbyLink: vi.fn(() => ({
    link: 'test123.com',
    maintain: false,
    maintainInfo: ''
  })),
}))

describe('LiveMenu.vue', () => {
  it('render', () => {
    const menuList = [
      { id: 176, name: 'Ultimate Hall' },
      { id: 38, name: 'Other Hall' },
    ]
    const isLogin = true
    const wrapper = mount(LiveMenu, {
      global: {
        plugins: [i18n],
      },
      props: {
        menuList,
        isLogin
      },
    })

    const items = wrapper.findAll('.bbin-block')
    expect(items).toHaveLength(menuList.length)

    expect(items[0].text()).toContain('Ultimate Hall')
    expect(items[1].text()).toContain('Other Hall')
  })

  it('openLiveHall', async () => {
    const menuList = [
      { id: 176, name: 'Ultimate Hall' },
      { id: 38, name: 'Other Hall' },
    ]
    const isLogin = true
    const wrapper = mount(LiveMenu, {
      global: {
        plugins: [i18n],
      },
      props: {
        menuList,
        isLogin
      },
    })
    const vm = wrapper.vm as any

    const openLiveHallSpy = vi.spyOn(vm, 'openLiveHall')
    const openSpy = vi.spyOn(window, 'open').mockImplementation(vi.fn())

    await wrapper.find('.bbin-block').trigger('click')

    expect(openLiveHallSpy).toHaveBeenCalledTimes(1)
    expect(openSpy).toHaveBeenCalledWith(
      'test123.com',
      'live',
      'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no'
    )

    await wrapper.findAll('.bbin-block')[1].trigger('click')

    expect(openLiveHallSpy).toHaveBeenCalledTimes(2)
    expect(openSpy).toHaveBeenCalledWith(
      'test123.com',
      'live',
      'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no'
    )

    openSpy.mockRestore()
  })
})
