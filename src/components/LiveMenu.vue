<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { ElMessageBox } from 'element-plus'
import type { ILiveGameMenu } from '@/types/live'
import { getLiveLobbyLink } from '@/api/game'
import { useCookies } from 'vue3-cookies'

const props = defineProps({
  menuList: {
    type: Array as PropType<ILiveGameMenu[]>,
    default: () => [],
    required: true
  },
  isLogin: {
    type: Boolean,
    default: false,
    required: true
  }
})

const { t } = useI18n()
const { cookies } = useCookies()
// livedealer: 快速廳, multibet: 多台下注, blockchain: 區塊鏈, ultimate: 旗艦廳
const liveMenuIdMap: { [key: number]: string } = {
  38: 'livedealer',
  40: 'multibet',
  99: 'blockchain',
  176: 'ultimate'
}

const sortMenuList = computed(() => {
  let result = [] as ILiveGameMenu[]
  const desiredOrder = [176, 38, 40, 99]

  desiredOrder.forEach(id => {
    const item = props.menuList.find(item => item.id === id)
    if (item) {
      result.push(item)
    }
  })

  return result 
})

const openLiveHall = async (menuId: number) => {
  if (props.isLogin === false) {
    ElMessageBox.alert(
      t('S_LOGIN_TIPS'), 
      '', 
      { center: true, showClose: false}
    )
    return
  }

  try {
    const params = {
      session_id: cookies.get('SESSION_ID'),
      lang: cookies.get('lang'),
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
      domain_url: location.hostname,
      is_mobile: false,
      enter_page: liveMenuIdMap[menuId] || 'livedealer'
    }

    const res = await getLiveLobbyLink(params)
    
    if (res.maintain) {
      ElMessage.error({
        dangerouslyUseHTMLString: true,
        message: res.maintainInfo
      })
      return
    }

    window.open(
      res.link,
      'live',
      'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no'
    )
  } catch(error) {
    ElMessage.error((error as Error).message)
    console.error(error)
  }
}
</script>

<template>
<div class="hall-group-btn">
  <div
    v-for="menu in sortMenuList"
    :key="menu.id"
    class="bbin-block"
    @click="openLiveHall(menu.id)"
  >
    <a :class="menu.id === 176 ? `ultimate-hall-${menu.id}` : `other-hall other-hall-${menu.id}`">
      <i class="game-icon"></i>
      <p>
        <span>{{ menu.name }}</span>
      </p>
    </a>
  </div>
</div>
</template>

<style lang="scss" scoped>
.hall-group-btn {
  position: absolute;
  top: 13rem;
  right: 2.5rem;
}

.game-icon {
  display: inline-block;
  vertical-align: middle;
  width: 3rem;
  height: 2.5rem;
  margin: 0 1.5rem 0 1.8rem;
  background: url(/client/static/image/hall/live/livecasino_icon.png)
    no-repeat;
  transition: all 0.3s ease;
}

.bbin-block:nth-child(1) {
  float: left;
}

.bbin-block:nth-child(2),
.bbin-block:nth-child(3),
.bbin-block:nth-child(4) {
  display: flex;
  flex-direction: column;
}

.ultimate-hall-176 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: url(/client/static/image/hall/live/ultimate.png) 0 0 no-repeat;
  width: 146px;
  height: 226px;
  margin-right: 0px;
  border: 1px solid #fff;
  color: #fff;
  text-decoration: none;
  text-align: center;
  cursor: pointer;

    .game-icon {
      background-position: 100% 0;
      margin: 10px;
    }

    &:hover {
      color: #ffd24c;
      border-color: #fccf4f;
      background: url(/client/static/image/hall/live/ultimate.png) 0 100% no-repeat;
        .game-icon {
          background-position: 100% 100%;
        }
    }
}
.other-hall {
  display: flex;
  align-items: center;
  width: 182px;
  height: 5rem;
  border: 1px solid #fff;
  color: #fff;
  text-decoration: none;
  text-align: center;
  margin: 0 0 8px 5px;
  cursor: pointer;

  &.other-hall-38 {
    .game-icon {
      background-position: 29% 0;
    }
  }
  &.other-hall-40 {
    .game-icon {
      background-position: 43% 0;
    }
  }
  &.other-hall-99 {
    .game-icon {
      background-position: 56% 0;
    }
  }
  &:hover {
    color: #ffd24c;
    border-color: #fccf4f;

    &.other-hall-38 {
      .game-icon {
        background-position: 30% 100%;
      }
    }
    &.other-hall-40 {
      .game-icon {
        background-position: 43% 100%;
      }
    }
    &.other-hall-99 {
      .game-icon {
        background-position: 57% 100%;
      }
    }
    
  }
}
</style>