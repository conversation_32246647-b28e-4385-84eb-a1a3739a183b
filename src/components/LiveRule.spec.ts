import { mount } from '@vue/test-utils'
import { describe, it, expect, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import LiveRule from './LiveRule.vue'

const i18n = createI18n({
  locale: 'zh-tw',
  messages: {
    'zh-tw': { S_GAME_RULE: '規則' },
    'zh-cn': { S_GAME_RULE: '规则' },
    'us': { S_GAME_RULE: 'Rules' },
  },
})

vi.stubGlobal('localStorage', {
  getItem: vi.fn((key) => {
    if (key === 'hallinfo_hallid') return '123'
    return null
  })
})

describe('LiveRule.vue', () => {
  it('render', async () => {
    const ruleList = [
      { id: 1, name: 'Game 1' },
      { id: 2, name: 'Game 2' },
    ]

    const wrapper = mount(LiveRule, {
      global: {
        plugins: [i18n],
      },
      props: {
        ruleList,
      },
    })

    expect(wrapper.find('h2').text()).toBe('規則')

    const items = wrapper.findAll('li')
    expect(items).toHaveLength(ruleList.length)
    expect(items[0].text()).toBe('| Game 1')
    expect(items[1].text()).toBe('| Game 2')
  })

  it('openRule_clicked', async () => {
    const ruleList = [
      { id: 1, name: 'Game 1' },
    ]

    const wrapper = mount(LiveRule, {
      global: {
        plugins: [i18n],
      },
      props: {
        ruleList,
      },
    })

    const openSpy = vi.spyOn(window, 'open').mockImplementation(vi.fn())
    await wrapper.find('li').trigger('click')

    expect(openSpy).toHaveBeenCalledWith(
      '/api/live/redirect_rule_url?game_id=1&lang=tw&hall_id=123',
      '',
      'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no'
    )

    openSpy.mockRestore()
  })

  it('getLanguageCode_cn', async () => {
    i18n.global.locale = 'zh-cn'

    const ruleList = [
      { id: 1, name: 'Game 1' },
    ]

    const wrapper = mount(LiveRule, {
      global: {
        plugins: [i18n],
      },
      props: {
        ruleList,
      },
    })

    const openSpy = vi.spyOn(window, 'open').mockImplementation(vi.fn())
    await wrapper.find('li').trigger('click')

    expect(openSpy).toHaveBeenCalledWith(
      '/api/live/redirect_rule_url?game_id=1&lang=cn&hall_id=123',
      '',
      'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no'
    )

    openSpy.mockRestore()
  })

  it('getLanguageCode_en', async () => {
    i18n.global.locale = 'us'

    const ruleList = [
      { id: 1, name: 'Game 1' },
    ]

    const wrapper = mount(LiveRule, {
      global: {
        plugins: [i18n],
      },
      props: {
        ruleList,
      },
    })

    const openSpy = vi.spyOn(window, 'open').mockImplementation(vi.fn())
    await wrapper.find('li').trigger('click')

    expect(openSpy).toHaveBeenCalledWith(
      '/api/live/redirect_rule_url?game_id=1&lang=us&hall_id=123',
      '',
      'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no'
    )

    openSpy.mockRestore()
  })
})
