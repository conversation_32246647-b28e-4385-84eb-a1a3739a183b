<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import type { ILiveGameRule } from '@/types/live'

const props = defineProps({
  ruleList: {
    type: Array as PropType<ILiveGameRule[]>,
    default: () => [],
    required: true
  }
})

const { t, locale } = useI18n()

const getLanguageCode = (locale: string): string => {
  switch (locale) {
    case 'zh-tw':
      return 'tw'
    case 'zh-cn':
      return 'cn'
    default:
      return 'us'
  }
}

const openRule = (gameId: number) => {
  const lang = getLanguageCode(locale.value)

  window.open(
    `/api/live/redirect_rule_url?game_id=${gameId}&lang=${lang}&hall_id=${localStorage.getItem('hallinfo_hallid')}`,
    '',
    'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no'
  )
}
</script>

<template>
<div class="rule-list">
  <h2>{{ t('S_GAME_RULE') }}</h2>
  <ul>
    <li v-for="game in props.ruleList" :key="game.id" @click="openRule(game.id)">
      {{ '| ' + game.name }}
    </li>
  </ul>
</div>
</template>

<style lang="scss" scoped>
.rule-list {
  position: absolute;
  top: 12rem;
  right: 5rem;
  display: flex;
  flex-direction: column;
  width: 328px;
  height: 300px;

  h2 {
    font-size: 1.2rem;
    position: absolute;
    top: -1.3rem;
    left: 1rem;
    margin-bottom: 10px;
    color: #aaa;
  }

  ul {
    display: flex;
    flex-wrap: wrap;
    padding: 0;
    margin: 0;
    list-style-type: none;
    height: 100%;
    overflow-y: auto;
  }

  li {
    width: calc(50% - 12px);
    margin: 6px;
    color: #fff;
    cursor: pointer;
    &:hover {
      color: #fccf4f;
    }
  }
}
</style>