import { mount } from '@vue/test-utils'
import { describe, expect, it } from 'vitest'
import { nextTick } from 'vue'
import MaintainDialog from './MaintainDialog.vue'

describe('MaintainDialog.vue', () => {
  it('renders maintainInfo prop correctly', async () => {
    const maintainInfo = 'This is a test message.'
    const wrapper = mount(MaintainDialog, {
      props: {
        maintainInfo,
        modelValue: true
      }
    })

    
    await wrapper.vm.$nextTick()
    await nextTick()

    expect(wrapper.findComponent({name: 'el-dialog'}).props('center')).toBeTruthy()
    expect(wrapper.findComponent({name: 'el-dialog'}).props('showClose')).toBeFalsy()
    expect(wrapper.html()).toContain(maintainInfo)
    expect(wrapper.findComponent({name: 'el-button'}).exists).toBeTruthy()
  })

  it('closes the dialog when the Ok button is clicked', async () => {
    const wrapper = mount(MaintainDialog, {
      props: {
        maintainInfo: 'Test message',
        modelValue: true
      }
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    const vm = wrapper.vm as any
    expect(vm.showDialog).toBeTruthy()

    await wrapper.findComponent({name: 'el-button'}).trigger('click')

    expect(vm.showDialog).toBeFalsy()
    expect(wrapper.emitted('update:modelValue')).toEqual([[false]])
  })
})
