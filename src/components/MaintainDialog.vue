<script setup lang="ts">
const showDialog = defineModel({
  type: Boolean,
  default: false,
  required: true
})

const props = defineProps({
  maintainInfo: {
    type: String,
    default: '',
    required: true
  },
})

const handleClick = () => {
  showDialog.value = false
}
</script>

<template>
  <el-dialog
    v-model="showDialog"
    center
    :show-close="false"
  >
    <div v-html="props.maintainInfo"></div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClick">Ok</el-button>
      </div>
    </template>
  </el-dialog>
</template>
