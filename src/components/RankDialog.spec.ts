import { mount } from '@vue/test-utils';
import { describe, expect, it } from 'vitest';
import RankDialog from './RankDialog.vue';
import { rankData } from '@/types/game';
import { ElDialog } from 'element-plus';

describe('RankDialog.vue', () => {
  const mockTopList: rankData[] = [
    { name: 'Player 1', score: 100 },
    { name: 'Player 2', score: 90 },
    { name: 'Player 3', score: 80 },
    { name: 'Player 4', score: 70 },
    { name: 'Player 5', score: 60 },
    { name: 'Player 6', score: 50 },
    { name: 'Player 7', score: 40 },
    { name: 'Player 8', score: 30 },
    { name: 'Player 9', score: 20 },
    { name: 'Player 10', score: 10 },
    { name: 'Player 11', score: 5 },
    { name: 'Player 12', score: 1 },
  ];

  it('test_showRank_true', async () => {
    const wrapper = mount(RankDialog, {
      props: {
        modelValue: true,
        topList: mockTopList,
      },
    });

    await wrapper.vm.$nextTick()

    expect(wrapper.findComponent(ElDialog).exists()).toBe(true);
    expect(wrapper.findComponent(ElDialog).vm.title).toBe('Top Rank');
    expect(wrapper.findAll('tr').length).toBe(mockTopList.length + 1);
  });

  it('test_closeDialog', async () => {
    const wrapper = mount(RankDialog, {
      props: {
        modelValue: true,
        topList: mockTopList,
      },
    });

    await wrapper.vm.$nextTick()

    wrapper.findComponent(ElDialog).vm.$emit('close');

    expect(wrapper.emitted('update:modelValue')).toBeTruthy();
    expect(wrapper.emitted('update:modelValue')?.[0]).toEqual([false]);
  });

  it('test_row_class', async () => {
    const wrapper = mount(RankDialog, {
      props: {
        modelValue: true,
        topList: mockTopList,
      },
    });

    await wrapper.vm.$nextTick()

    const rows = wrapper.findAll('tr');

    for (let i = 0; i < 10; i++) {
      expect(rows[i + 1].classes()).toContain('top-10');
    }

    expect(rows[12].classes()).toContain('warning-row');
    expect(rows[11].classes()).toContain('success-row');
  });

  it('test_showRank_false', async () => {
    const wrapper = mount(RankDialog, {
      props: {
        modelValue: false,
        topList: mockTopList,
      },
    });

    await wrapper.vm.$nextTick()

    expect(wrapper.find('.el-dialog').exists()).toBe(false);
  });
});
