<script setup lang="ts">
import type { rankData } from '@/types/game'

const showRank = defineModel({
  type: Boolean,
  default: false,
  required: true
})

const props = defineProps({
  topList: {
    type: Array as PropType<rankData[]>,
    default: () => [],
    required: true
  },
})

const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex < 10) {
    return 'top-10'
  } else if (rowIndex % 2 == 1) {
    return 'warning-row'
  } else {
    return 'success-row'
  }
}
</script>

<template>
  <div>
    <el-dialog
      :model-value="showRank"
      title="Top Rank"
      width="90%"
      :align-center="true"
      @close="showRank = false"
    >
    <el-table
      width="200"
      :data="props.topList"
      header-cell-class-name="head-color"
      :row-class-name="tableRowClassName"
      :row-style="{ height: '0' }"
      :cell-style="{ padding: '0', textAlign: 'center' }"
    >
      <el-table-column type="index" />
      <el-table-column property="name" label="Name" />
      <el-table-column property="score" label="Score" />
    </el-table>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>

:deep(.head-color) {
  padding: 2px;
  color: #fff;
  font-size: 12px;
  text-align: center;
  background: var(--main-3);
}

:deep(.el-table) {
  .top-10 {
    --el-table-tr-bg-color: var(--main-7);
    color: #333;
    font-weight: 600;
    font-size: 1.2rem;
  }
  .warning-row {
    --el-table-tr-bg-color: var(--main-9);
  }
  .success-row {
    --el-table-tr-bg-color: #fff;
  }
}

</style>