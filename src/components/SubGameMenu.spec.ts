import { mount } from '@vue/test-utils'
import { describe, expect, it } from 'vitest'
import SubGameMenu from '@/components/SubGameMenu.vue'
import type { IGameMenu } from '@/types/game'
import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', name: 'home', component: { template: '<div>Home</div>' } },
    { path: '/m', name: 'mHome', component: { template: '<div>MHome</div>' } }
  ]
})

describe('SubGameMenu', () => {
  const mockSubMenu: IGameMenu[] = [
    {
      id: 1,
      name: 'Menu 1',
      lower: [] as IGameMenu[],
    },
    {
      id: 2,
      name: 'Menu 2',
      lower: [
        { id: 11, name: 'Sub Menu 2-1' },
        { id: 12, name: 'Sub Menu 2-2' },
      ] as IGameMenu[],
    },
  ] as IGameMenu[];

  it('renders sub menu items', () => {
    const wrapper = mount(SubGameMenu, {
      global: {
        plugins: [router],
      },
      props: {
        subMenu: mockSubMenu,
        'subMenuId': 1,
        'lowerMenuId': 0,
        'showLower': false,
      },
    });

    const menuItems = wrapper.findAll('.sub-menu-item');
    expect(menuItems).toHaveLength(mockSubMenu.length);
    expect(menuItems[0].text()).toBe('Menu 1');
    expect(menuItems[0].classes()).toContain('active');
    expect(menuItems[1].text()).toBe('Menu 2');
    expect(menuItems[1].classes()).not.toContain('active');
    expect(wrapper.find('.lower-wrap').exists()).toBe(false);
  });

  it('sets subMenuId and shows lower menu on click', async () => {
    const wrapper = mount(SubGameMenu, {
      global: {
        plugins: [router],
      },
      props: {
        subMenu: mockSubMenu,
        'subMenuId': 1,
        'lowerMenuId': 0,
        'showLower': false,
        'lowerType': 'static',
      },
    });

    const firstMenuItem = wrapper.findAll('.sub-menu-item')[1];
    await firstMenuItem.trigger('click');

    const lowerMenuItems = wrapper.findAll('.lower-wrap li')

    expect(wrapper.vm.subMenuId).toBe(2);
    expect(wrapper.vm.showLower).toBe(true);
    expect(wrapper.find('.lower-wrap').exists()).toBe(true);
    expect(lowerMenuItems).toHaveLength(mockSubMenu[1].lower!.length)
    expect(lowerMenuItems[0].text()).toBe('Sub Menu 2-1')
    expect(lowerMenuItems[1].text()).toBe('Sub Menu 2-2')
  });

  it('does not show lower menu if there are no lower items', async () => {
    const wrapper = mount(SubGameMenu, {
      global: {
        plugins: [router],
      },
      props: {
        subMenu: mockSubMenu,
        'subMenuId': 2,
        'lowerMenuId': 0,
        'showLower': false,
      },
    });

    const secondMenuItem = wrapper.findAll('.sub-menu-item')[0];
    await secondMenuItem.trigger('click');

    expect(wrapper.vm.subMenuId).toBe(1);
    expect(wrapper.vm.showLower).toBe(false);
    expect(wrapper.find('.lower-wrap').exists()).toBe(false);
  });

  it('sets lowerMenuId when a lower menu item is clicked', async () => {
    const wrapper = mount(SubGameMenu, {
      global: {
        plugins: [router],
      },
      props: {
        subMenu: mockSubMenu,
        'subMenuId': 2,
        'lowerMenuId': 0,
        'showLower': false,
        'lowerType': 'static',
      },
    });

    const firstMenuItem = wrapper.findAll('.sub-menu-item')[1];
    await firstMenuItem.trigger('click');

    const lowerMenuItem = wrapper.find('li');
    await lowerMenuItem.trigger('click');

    expect(wrapper.vm.lowerMenuId).toBe(11);
    expect(wrapper.vm.showLower).toBe(true);
  });

  it('handles hover type correctly', async () => {
    const wrapper = mount(SubGameMenu, {
      global: {
        plugins: [router],
      },
      props: {
        subMenu: mockSubMenu,
        'subMenuId': 1,
        'lowerMenuId': 0,
        'showLower': false,
        'lowerType': 'hover',
      },
    });

    const firstMenuItem = wrapper.findAll('.sub-menu-item')[1];
    await firstMenuItem.trigger('mouseover');

    expect(wrapper.vm.showLower).toBe(true);
    expect(wrapper.find('.lower-wrap').exists()).toBe(true);
  });

  it('handles static type correctly', async () => {
    const wrapper = mount(SubGameMenu, {
      global: {
        plugins: [router],
      },
      props: {
        subMenu: mockSubMenu,
        'subMenuId': 1,
        'lowerMenuId': 0,
        'showLower': false,
        'lowerType': 'static',
      },
    });

    const firstMenuItem = wrapper.find('.sub-menu-item');
    await firstMenuItem.trigger('mouseover');

    expect(wrapper.vm.showLower).toBe(false);
    expect(wrapper.find('.lower-wrap').exists()).toBe(false);
  });
});
