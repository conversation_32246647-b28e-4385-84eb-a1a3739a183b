<script setup lang="ts">
import type { IGameMenu } from '@/types/game'
import { useImportTheme } from '@/composables/useImportTheme'
import { useRoute } from 'vue-router'

const { importStyle } = useImportTheme()
const route = useRoute()

const isMobile = computed(() => {
  return route.fullPath.startsWith('/m')
})

const subMenuId = defineModel('subMenuId', {
  type: Number,
  default: 0,
  required: true
})

const lowerMenuId = defineModel('lowerMenuId', {
  type: Number,
  default: 0,
  required: true
})

const showLower = defineModel('showLower', {
  type: Boolean,
  default: false,
  required: true
})

const props = defineProps({
  subMenu: {
    type: Array as PropType<IGameMenu[]>,
    default: () => [],
    required: true
  },
  lowerType: {
    type: String,
    default: 'hover',
    required: false
  }
})

const setSubMenu = (menuItem: IGameMenu) => {
  subMenuId.value = menuItem.id
  lowerMenuId.value = 0

  handleLowerwrap(menuItem.lower || {})
}

const handleLowerwrap = (lower: Record<number, IGameMenu>) => {
  if (Object.keys(lower).length > 0) {
    showLower.value = true
    return
  }

  showLower.value = false
}

const setLowerMenu = (subMenuItemId: number, lowerMenuItemId: number) => {
  subMenuId.value = subMenuItemId
  lowerMenuId.value = lowerMenuItemId
}

importStyle('mobile/subGameMenu')
</script>

<template>
  <div class="sub-game-menu" :class="{ mobile: isMobile }">
    <template v-for="item in props.subMenu" :key="item.id">
      <div
        class="sub-menu-item"
        :class="item.id === subMenuId ? 'active' : ''"
        @click="setSubMenu(item)"
        @mouseover="props.lowerType === 'hover' ? handleLowerwrap(item.lower || {}) : ''"
      >
        <span class="item-name">{{ item.name }}</span>
      </div>
      <div v-if="showLower && !isMobile" :class="item.lower && Object.keys(item.lower).length > 0 ? 'lower-wrap' : ''">
        <template v-for="lower in item.lower" :key="lower.id">
          <li @click="setLowerMenu(item.id, lower.id)">{{ lower.name }}</li>
        </template>
      </div>
    </template>
  </div>
</template>

<style lang="scss" scoped>
.sub-game-menu {
  position: relative;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-items: flex-start;
  align-items: center;
  border-bottom: 1px solid var(--mobile-menu-bar-border);
  background: var(--menu-sub-bg);
  color: var(--icon-bg);

  .sub-menu-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    height: 40px;
    padding: 10px;
    font-size: 1.2rem;

    &:hover {
      color: var(--icon-bg-active);
    }

    &.active {
      color: #fff;
      background: var(--icon-bg-active);
      border: 1px solid var(--taxt-active);
      &:hover {
        color: #fff;
      }
    }
  }

  .lower-wrap {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    height: auto;
    padding: 10px;
    position: absolute;
    top: 40px;
    left: 0;
    background: #000;
    opacity: 0.7;
    list-style: none;
    z-index: 1;

    li {
      width: 100px;
      height: auto;
      margin-right: 5px;
      padding: 5px;
      border: 1px solid #fff;
      border-radius: 5px;
      cursor: pointer;
      &:hover {
        border: 1px solid var(--main);
      }
    }
  }

  &.mobile {
    color: var(--text-nonactive);
    background: var(--mobile-subGameMenu-bg);
    border-bottom: 1px solid var(--mobile-subGameMenu-border);
    padding: 0 10px;

    .sub-menu-item {
      padding: 0;
      
      .item-name {
        padding: 5px 20px;
        font-size: 1rem;
      }

      &.active {
        background: none;
        border: none;

        .item-name {
          border-radius: 10px;
          background: var(--mobile-subGameMenu-active);
        }
      }
    }
    
  }
}
</style>
