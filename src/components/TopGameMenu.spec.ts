import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import TopGameMenu from '@/components/TopGameMenu.vue'
import type { IGameMenu } from '@/types/game'
import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', name: 'home', component: { template: '<div>Home</div>' } },
    { path: '/m', name: 'mHome', component: { template: '<div>MHome</div>' } }
  ]
})

const mockGameMenu: IGameMenu[] = [
  { id: 1, gameCount: 1, sort: 1, name: 'Menu 1', games: [] },
  { id: 2, gameCount: 2, sort: 2, name: 'Menu 2', games: [] },
  { id: 3, gameCount: 3, sort: 3, name: 'Menu 3', games: [] },
]

describe('TopGameMenu', () => {
  it('renders correctly with gameMenu prop', () => {
    const wrapper = mount(TopGameMenu, {
      global: {
        plugins: [router],
      },
      props: {
        gameMenu: mockGameMenu,
        topMenuId: 1,
      },
    })

    const menuItems = wrapper.findAll('.main-menu-item')
    expect(menuItems).toHaveLength(mockGameMenu.length)
    expect(menuItems[0].text()).toBe('Menu 1')
    expect(menuItems[1].text()).toBe('Menu 2')
    expect(menuItems[2].text()).toBe('Menu 3')
  })

  it('sets active class on clicked menu item', async () => {
    const wrapper = mount(TopGameMenu, {
      global: {
        plugins: [router],
      },
      props: {
        gameMenu: mockGameMenu,
        topMenuId: 1,
      },
    })

    const firstMenuItem = wrapper.find('.main-menu-item')
    await firstMenuItem.trigger('click')

    expect(firstMenuItem.classes()).toContain('active')
    expect(wrapper.vm.topMenuId).toBe(1)

    const secondMenuItem = wrapper.findAll('.main-menu-item')[1]
    await secondMenuItem.trigger('click')

    expect(secondMenuItem.classes()).toContain('active')
    expect(firstMenuItem.classes()).not.toContain('active')
    expect(wrapper.vm.topMenuId).toBe(2)
  })
})
