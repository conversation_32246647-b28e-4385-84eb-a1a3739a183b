<script setup lang="ts">
import type { IGameMenu } from '@/types/game'
import { useImportTheme } from '@/composables/useImportTheme'
import { useRoute } from 'vue-router'

const { importStyle } = useImportTheme()
const route = useRoute()

const isMobile = computed(() => {
  return route.fullPath.startsWith('/m')
})

const topMenuId = defineModel('topMenuId', {
  type: Number,
  default: 0,
  required: true
})

const props = defineProps({
  gameMenu: {
    type: Array as PropType<IGameMenu[]>,
    default: () => [],
    required: false
  }
})

importStyle('pc/topGameMenu')
importStyle('mobile/topGameMenu')
</script>

<template>
  <div class="main" :class="{ 'mobile': isMobile }">
    <template v-for="item in props.gameMenu" :key="item.id">
      <div
        class="main-menu-item"
        :class="{ active: item.id === topMenuId }"
        @click="topMenuId = item.id"
      >
        {{ item.name }}
      </div>
    </template>
  </div>
</template>

<style lang="scss" scoped>
.main {
  display: flex;
  height: 40px;
  align-items: center;
  color: var(--text-active);

  .main-menu-item {
    width: 90px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--pc-topGameMenu-main-menu-item-text);
    cursor: pointer;

    &:hover {
      background: var(--pc-topGameMenu-main-menu-item-text-active);
    }
    &.active {
      background: var(--pc-topGameMenu-main-menu-item-text-active);
      border: 1px solid var(--text-active);
    }
  }

  &.mobile {
    width: 100%;
    justify-content: space-between;

    .main-menu-item {
      flex: 1;
      border: 0;
      color: var(--text-nonactive);
      background: var(--mobile-topGameMenu-main-menu-bg);

      &.active {
        position: relative;
        color: var(--mobile-topGameMenu-main-menu-active);

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          width: 100%;
          height: 3px;
          border-bottom: 3px solid var(--mobile-topGameMenu-main-menu-active);
        }
      }
    }
  }
}
</style>