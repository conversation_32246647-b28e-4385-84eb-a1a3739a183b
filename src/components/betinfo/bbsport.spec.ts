import { mount } from '@vue/test-utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import BBSport from '@/components/betinfo/bbsport.vue'
import { getSportBetInfo } from '@/api/betinfo'
import { ElMessage } from 'element-plus'
import type { ISportBetInfo } from '@/types/mcenter'

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      'S_GAMETYPE': 'Game Type',
      'S_SRLIMIT': 'Bet Limit',
      'S_SOLIMIT': 'Game Limit',
    },
  },
})

vi.mock('@/api/betinfo', () => ({
  getSportBetInfo: vi.fn(),
}))

vi.mock('@/components/selector/ObjectSelector.vue', () => ({
  default: {
    name: 'ObjectSelector',
    props: ['dataList'],
    template: '<div class="mock-object-selector">Mock ObjectSelector</div>',
  },
}))

describe('BBSport', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders correctly when not in maintenance', async () => {
    const mockBetInfo = {
      categoryList: [
        {
          id: 1,
          name: '足球'
        }
      ],
      gameLimitList: [{
        groupName: '讓球',
        betLimit: 1000000,
        gameLimit: 10000000
      }],
      maintain: false,
      maintainInfo: ''
    } as ISportBetInfo

    vi.mocked(getSportBetInfo).mockResolvedValue(mockBetInfo)

    const wrapper = mount(BBSport, {
      global: {
        plugins: [i18n],
      },
    })

    expect(wrapper.findComponent({ name: 'el-skeleton' }).exists()).toBe(true)

    await wrapper.vm.$nextTick()
    await vi.waitFor(() => {
      expect(getSportBetInfo).toHaveResolved()
    })
    await nextTick()

    expect(wrapper.findComponent({ name: 'el-skeleton' }).exists()).toBe(false)
    expect(wrapper.findComponent({ name: 'ObjectSelector' }).exists()).toBe(true)

    const elTable = wrapper.find('.el-table')
    expect(elTable.exists()).toBe(true)

    const elTableHeader = elTable.findAll('th')
    expect(elTableHeader.length).toBe(3)
    expect(elTableHeader[0].text()).toContain('Game Type')
    expect(elTableHeader[1].text()).toContain('Bet Limit')
    expect(elTableHeader[2].text()).toContain('Game Limit')

    const elTableBody = elTable.find('tbody')
    const elTableRows = elTableBody.findAll('tr')
    expect(elTableRows.length).toBe(1)
    expect(elTableRows[0].text()).toContain('讓球')
    expect(elTableRows[0].text()).toContain(1000000)
    expect(elTableRows[0].text()).toContain(10000000)
  })

  it('renders maintenance message when in maintenance', async () => {
    const mockBetInfo = {
      maintain: true,
      maintainInfo: 'Maintenance in progress'
    } as ISportBetInfo

    vi.mocked(getSportBetInfo).mockResolvedValue(mockBetInfo)

    const wrapper = mount(BBSport, {
      global: {
        plugins: [i18n],
      },
      props: {
        isSportmaintain: true
      }
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    expect(wrapper.html()).toContain('Maintenance in progress')
  })

  it('fetches sport bet info on game type change', async () => {
    const mockBetInfo = {
      categoryList: [
        {
          id: 1,
          name: '足球'
        }
      ],
      gameLimitList: [{
        groupName: '讓球',
        betLimit: 1000000,
        gameLimit: 10000000
      }],
      maintain: false,
      maintainInfo: ''
    } as ISportBetInfo

    vi.mocked(getSportBetInfo).mockResolvedValue(mockBetInfo)

    const wrapper = mount(BBSport, {
      global: {
        plugins: [i18n],
      },
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    const objectSelector = wrapper.findComponent({ name: 'ObjectSelector' })
    await objectSelector.vm.$emit('change')

    expect(getSportBetInfo).toHaveBeenCalled()
  })

  it('shows error message when fetch fails', async () => {
    const errorMessage = 'Failed to fetch data'
    vi.mocked(getSportBetInfo).mockRejectedValue(new Error(errorMessage))
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())

    const wrapper = mount(BBSport, {
      global: {
        plugins: [i18n],
      },
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    expect(console.error).toHaveBeenCalledWith(errorMessage)
    expect(ElMessage.error).toHaveBeenCalledWith(errorMessage)
  })
})
