<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { getSportBetInfo } from '@/api/betinfo'
import type { ISportBetInfo } from '@/types/mcenter'
import { ElMessage } from 'element-plus'
import ObjectSelector from '@/components/selector/ObjectSelector.vue'
import { useCookies } from 'vue3-cookies'

const { t } = useI18n()
const { cookies } = useCookies()
const maintainText = ref('')
const betInfo = ref<ISportBetInfo>({} as ISportBetInfo)
const gameTypeList = ref<{label: string, value: string}[]>([])
const gameTypeSelected = ref(1)
const isSportMaintain = ref(false)
const isLoading = ref(false)
const isShowSkeleton = ref(true)

const fetchSportBetinfo = async () => {
  isLoading.value = true

  try {
    const param = {
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
      session_id: cookies.get('SESSION_ID'),
      lang: cookies.get('lang'),
      category_id: gameTypeSelected.value
    }

    const res = await getSportBetInfo(param)
    
    if(res.maintain) {
      isSportMaintain.value = true
      maintainText.value = res.maintainInfo
    } else {
      betInfo.value = res as ISportBetInfo
      gameTypeList.value = betInfo.value.categoryList.map((item: any) => ({
        label: item.name,
        value: item.id
      }))
    }
  } catch (error: any) {
    console.error(error.message)
    ElMessage.error(error.message)
  } finally {
    isLoading.value = false
    isShowSkeleton.value = false
  }
}

fetchSportBetinfo()
</script>

<template>
  <div v-if="isSportMaintain === false">
    <el-skeleton v-if="isShowSkeleton" :rows="5" animated />
    <div 
      v-else
      v-loading="isLoading"
    >
      <div >
        <ObjectSelector
          v-model="gameTypeSelected"
          :data-list="gameTypeList"
          @change="fetchSportBetinfo"
        />
      </div>
      <div class="table-container">
        <el-table 
          :data="betInfo.gameLimitList"
          header-cell-class-name="table-header2"
          cell-class-name="table-cell2"
        >
          <el-table-column prop="groupName" :label="t('S_GAMETYPE')" />
          <el-table-column prop="betLimit" :label="t('S_SRLIMIT')" />
          <el-table-column prop="gameLimit" :label="t('S_SOLIMIT')" />
        </el-table>
      </div>
    </div>
  </div>
  <div
    v-else
    v-html="maintainText"
  />
</template>

<style lang="scss" scoped>
.table-container {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;

  .el-table {
    margin: 15px;
  }
}

:deep(.el-table){
  .table-header2 {
    color: var(--mcenter-table-header2--text);
    font-size: 15px;
    text-align: center !important;
    background: var(--mcenter-betinfo-table-header-bg) !important;
  }

  .table-cell2 {
    color: var(--mcenter-table-cell2--text);
    text-align: center !important;
  }

  .el-table__empty-text {
    color: var(--mcenter-nodata);
  }
}
</style>