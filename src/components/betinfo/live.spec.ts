import { mount } from '@vue/test-utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import Live from '@/components/betinfo/live.vue'
import { getLiveBetInfo } from '@/api/betinfo'
import { ElMessage } from 'element-plus'
import type { ILiveBetInfo } from '@/types/mcenter'

vi.mock('@/api/betinfo', () => ({
  getLiveBetInfo: vi.fn(),
}))

describe('Live', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders correctly when not in maintenance', async () => {
    const mockBetInfo = {
      gameLimitList: [{
        name: '百家樂',
        gamePlayLimitList: [{
          limitName: '單注最低限額',
          limitValue: 20
        }]
      }, {
        name: '龍虎鬥',
        gamePlayLimitList: [{
          limitName: '單注最低限額',
          limitValue: 50
        }]
      }],
      maintain: false,
      maintainInfo: ''
    } as ILiveBetInfo

    vi.mocked(getLiveBetInfo).mockResolvedValue(mockBetInfo)

    const wrapper = mount(Live)

    const vm = wrapper.vm as any
    const handerMethodSpy = vi.spyOn(vm, 'handerMethod')

    await vm.$nextTick()
    await nextTick()
    await nextTick()

    const elTables = wrapper.findAllComponents({ name: 'el-table' })
    expect(elTables[0].exists()).toBe(true)
    expect(elTables[1].exists()).toBe(true)
    expect(handerMethodSpy).toHaveBeenCalled()

    elTables.forEach(table => {
      const theadTh = table.findAll('thead tr')[1].findAll('th')
      theadTh.forEach(th => {
        expect(th.attributes('style')).toContain('display: none')
      })
    })
    
    expect(elTables[0].find('thead .cell').text()).toContain('百家樂')
    expect(elTables[0].findAll('tbody .cell')[0].text()).toContain('單注最低限額')
    expect(elTables[0].findAll('tbody .cell')[1].text()).toContain('20')
    expect(elTables[1].find('thead .cell').text()).toContain('龍虎鬥')
    expect(elTables[1].findAll('tbody .cell')[0].text()).toContain('單注最低限額')
    expect(elTables[1].findAll('tbody .cell')[1].text()).toContain('50')
  })

  it('renders maintenance message when in maintenance', async () => {
    const mockBetInfo = {
      maintain: true,
      maintainInfo: 'Maintenance in progress',
    } as ILiveBetInfo

    vi.mocked(getLiveBetInfo).mockResolvedValue(mockBetInfo)

    const wrapper = mount(Live)
    const vm = wrapper.vm as any

    vm.isLiveMaintain = true

    await vm.$nextTick()
    await nextTick()

    expect(wrapper.html()).toContain('Maintenance in progress')
  })

  it('shows error message when fetch fails', async () => {
    const errorMessage = 'Failed to fetch data'
    vi.mocked(getLiveBetInfo).mockRejectedValue(new Error(errorMessage))
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())

    const wrapper = mount(Live)

    await wrapper.vm.$nextTick()
    await nextTick()

    expect(console.error).toHaveBeenCalledWith(errorMessage)
    expect(ElMessage.error).toHaveBeenCalledWith(errorMessage)
  })
})
