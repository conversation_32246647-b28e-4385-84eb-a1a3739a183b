<script setup lang="ts">
import { getLiveBetInfo } from '@/api/betinfo'
import { ElMessage } from 'element-plus'
import type { ILiveBetInfo } from '@/types/mcenter'
import { useCookies } from 'vue3-cookies'

const maintainText = ref('')
const betInfo = ref<ILiveBetInfo>({} as ILiveBetInfo)
const isLiveMaintain = ref(false)
const isLoading = ref(false)
const { cookies } = useCookies()

const handerMethod = ({ rowIndex }: any) => {
  if (rowIndex === 1) {
    return { display: 'none' }
  }
}

const fetchLiveBetinfo = async () => {
  isLoading.value = true

  try {
    const params = {
      session_id: cookies.get('SESSION_ID'),
      lang: cookies.get('lang'),
      hall_id: Number(localStorage.getItem('hallinfo_hallid'))
    }

    const res = await getLiveBetInfo(params)
    
    if (res.maintain) {
      isLiveMaintain.value = true
      maintainText.value = res.maintainInfo
    } else {
      betInfo.value = res as ILiveBetInfo
    }
  } catch (error: any) {
    console.error(error.message)
    ElMessage.error(error.message)
  } finally {
    isLoading.value = false
  }
}

fetchLiveBetinfo()
</script>

<template>
  <div
    v-if="isLiveMaintain === false"
  >
    <div class="table-container" v-if="!isLoading">
      <template v-for="data in betInfo.gameLimitList" :key="data.groupName">
        <el-table
          :data="data.gamePlayLimitList"
          style="width: 45%"
          header-cell-class-name="table-header2"
          cell-class-name="table-cell2"
          :header-cell-style="handerMethod"
        >
          <el-table-column :label="data.name">
            <el-table-column label="" prop="limitName" />
            <el-table-column label="" prop="limitValue" />
          </el-table-column>
        </el-table>
      </template>
    </div>
    <el-skeleton v-else :rows="5" animated />
  </div>
  <div
    v-else
    v-html="maintainText"
  />
</template>

<style lang="scss" scoped>
.table-container {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;

  .el-table {
    margin: 15px;
  }
}

:deep(.el-table){
  .table-header2 {
    color: var(--mcenter-table-header2--text);
    font-size: 15px;
    text-align: center !important;
    background: var(--mcenter-betinfo-table-header-bg) !important;
  }

  .table-cell2 {
    color: var(--mcenter-table-cell2--text);
    text-align: center !important;
  }

  .el-table__empty-text {
    color: var(--mcenter-nodata);
  }
}
</style>