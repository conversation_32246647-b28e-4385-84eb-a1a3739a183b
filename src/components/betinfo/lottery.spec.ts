import { flushPromises, mount } from '@vue/test-utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import Lottery from '@/components/betinfo/lottery.vue'
import { getLotteryBetInfo } from '@/api/betinfo'
import { ElMessage } from 'element-plus'
import type { ILotteryBetInfo } from '@/types/mcenter'

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      'S_GAMETYPE': 'Game Type',
      'S_SILIMIT': 'Game Limit',
      'S_SOLIMIT': 'Bet Limit',
    },
  },
})

vi.mock('@/api/betinfo', () => ({
  getLotteryBetInfo: vi.fn(),
}))

vi.mock('@/components/selector/ObjectSelector.vue', () => ({
  default: {
    name: 'ObjectSelector',
    props: ['dataList'],
    template: '<div class="mock-object-selector">Mock ObjectSelector</div>',
  },
}))

const mockBetInfo = {
  gameLimitList: [{
    title: '特別號',
    singleCredit: 4160,
    singleOdds: 2080
  }],
  gameList: [{
    id: 'HKLT',
    name: '香港六合彩'
  }],
  maintain: false,
  maintainInfo: ''
} as ILotteryBetInfo

describe('Lottery', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders correctly when not in maintenance', async () => {
    vi.mocked(getLotteryBetInfo).mockResolvedValue(mockBetInfo)

    const wrapper = mount(Lottery, {
      global: {
        plugins: [i18n],
      },
    })

    expect(wrapper.findComponent({ name: 'el-skeleton' }).exists()).toBe(true)

    await flushPromises()
    await nextTick()

    expect(wrapper.findComponent({ name: 'el-skeleton' }).exists()).toBe(false)
    expect(wrapper.findComponent({ name: 'ObjectSelector' }).exists()).toBe(true)

    const elTable = wrapper.find('.el-table')
    expect(elTable.exists()).toBe(true)

    const elTableHeader = elTable.findAll('th')
    expect(elTableHeader.length).toBe(3)
    expect(elTableHeader[0].text()).toContain('Game Type')
    expect(elTableHeader[1].text()).toContain('Game Limit')
    expect(elTableHeader[2].text()).toContain('Bet Limit')

    const elTableBody = elTable.find('tbody')
    const elTableRows = elTableBody.findAll('tr')
    expect(elTableRows.length).toBe(1)
    expect(elTableRows[0].text()).toContain('特別號')
    expect(elTableRows[0].text()).toContain('4160')
    expect(elTableRows[0].text()).toContain('2080')
  })

  it('renders maintenance message when in maintenance', async () => {
    const mockBetInfo = {
      maintain: true,
      maintainInfo: 'Maintenance in progress'
    } as ILotteryBetInfo

    vi.mocked(getLotteryBetInfo).mockResolvedValue(mockBetInfo)

    const wrapper = mount(Lottery, {
      global: {
        plugins: [i18n],
      },
      props: {
        isLotterymaintain: true
      }
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    expect(wrapper.html()).toContain('Maintenance in progress')
  })

  it('fetches lottery bet info on game type change', async () => {
    vi.mocked(getLotteryBetInfo).mockResolvedValue(mockBetInfo)

    const wrapper = mount(Lottery, {
      global: {
        plugins: [i18n],
      },
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    const objectSelector = wrapper.findComponent({ name: 'ObjectSelector' })
    await objectSelector.vm.$emit('change')

    expect(getLotteryBetInfo).toHaveBeenCalled()
  })

  it('shows error message when fetch fails', async () => {
    const errorMessage = 'Failed to fetch data'
    vi.mocked(getLotteryBetInfo).mockRejectedValue(new Error(errorMessage))
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())

    mount(Lottery, {
      global: {
        plugins: [i18n],
      },
    })

    await flushPromises()

    expect(console.error).toHaveBeenCalledWith(errorMessage)
    expect(ElMessage.error).toHaveBeenCalledWith(errorMessage)
  })
})
