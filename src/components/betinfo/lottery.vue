<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { getLotteryBetInfo } from '@/api/betinfo'
import type { ILotteryBetInfo } from '@/types/mcenter'
import { ElMessage } from 'element-plus'
import ObjectSelector from '@/components/selector/ObjectSelector.vue'
import { useCookies } from 'vue3-cookies'

const { t } = useI18n()
const { cookies } = useCookies()
const maintainText = ref('')
const tableData = ref<ILotteryBetInfo["gameLimitList"]>([] as ILotteryBetInfo["gameLimitList"])
const gameTypeList = ref<{label: string, value: string}[]>([])
const gameTypeSelected = ref<string>('HKLT')
const isLotteryMaintain = ref(false)
const isLoading = ref(false)
const isShowSkeleton = ref(true)

const fetchLotteryBetinfo = async () => {
  isLoading.value = true

  try {
    const params = {
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
      session_id: cookies.get('SESSION_ID'),
      lang: cookies.get('lang'),
      game_id: gameTypeSelected.value
    }

    const res = await getLotteryBetInfo(params)
    
    if (res.maintain) {
      isLotteryMaintain.value = true
      maintainText.value = res.maintainInfo
    } else {
      const data = res as ILotteryBetInfo
      tableData.value = data.gameLimitList
      gameTypeList.value = data.gameList.map((item: any) => ({
        label: item.name,
        value: item.id
      }))
    }
  } catch (error: any) {
    console.error(error.message)
    ElMessage.error(error.message)
  } finally {
    isLoading.value = false
    isShowSkeleton.value = false
  }
}

fetchLotteryBetinfo()
</script>

<template>
  <div v-if="isLotteryMaintain === false">
    <el-skeleton v-if="isShowSkeleton" :rows="5" animated />
    <div 
      v-else
      v-loading="isLoading"
    >
      <div >
        <ObjectSelector
          v-model="gameTypeSelected"
          :data-list="gameTypeList"
          @change="fetchLotteryBetinfo"
        />
      </div>
      <div class="table-container">
        <el-table 
          :data="tableData"
          header-cell-class-name="table-header2"
          cell-class-name="table-cell2"
        >
          <el-table-column prop="title" :label="t('S_GAMETYPE')" />
          <el-table-column prop="singleCredit" :label="t('S_SILIMIT')" />
          <el-table-column prop="singleOdds" :label="t('S_SOLIMIT')" />
        </el-table>
      </div>
    </div>
  </div>
  <div
    v-else
    v-html="maintainText"
  />
</template>

<style lang="scss" scoped>
.table-container {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;

  .el-table {
    margin: 15px;
  }
}

:deep(.el-table){
  .table-header2 {
    color: var(--mcenter-table-header2--text);
    font-size: 15px;
    text-align: center !important;
    background: var(--mcenter-betinfo-table-header-bg) !important;
  }

  .table-cell2 {
    color: var(--mcenter-table-cell2--text);
    text-align: center !important;
  }

  .el-table__empty-text {
    color: var(--mcenter-nodata);
  }
}
</style>