import { mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'
import BetHistoryDialog from './BetHistoryDialog.vue'

describe('BetHistoryDialog', () => {
  it('renders correctly when modelValue is true', async () => {
    const wrapper = mount(BetHistoryDialog, {
      props: {
        modelValue: false,
        title: 'test',
        ifrUrl: 'google.com'
      },
      global: {
        stubs: {
          transition: false,
        },
      },
    })

    const vm = wrapper.vm as any

    const handleOpenedSpy = vi.spyOn(vm, 'handleOpened')

    await wrapper.setProps({ modelValue: true });

    expect(wrapper.find('.el-dialog__title').text()).toBe('test')
    expect(wrapper.findComponent({ name: 'el-skeleton' }).exists()).toBe(true)

    await vi.waitFor(() => {
      expect(handleOpenedSpy).toHaveBeenCalled()
    })

    expect(wrapper.find('iframe').exists()).toBe(true)
    expect(wrapper.find('iframe').attributes('src')).toBe('google.com')

    const iframeOnloadSpy = vi.spyOn(vm.dialogIframe, 'onload')

    await vm.dialogIframe.onload(new Event('load'))

    await vi.waitFor(() => {
      expect(iframeOnloadSpy).toHaveBeenCalled()
    })

    expect(wrapper.findComponent({ name: 'el-skeleton' }).exists()).toBe(false)
    expect(wrapper.find('iframe').attributes('class')).toBe('')
  })

  it('renders correctly when modelValue is false', async () => {
    const wrapper = mount(BetHistoryDialog, {
      props: {
        modelValue: false,
        title: 'test',
        ifrUrl: 'google.com'
      },
      global: {
        stubs: {
          transition: false,
        },
      },
    })

    const vm = wrapper.vm as any

    const handleClosedSpy = vi.spyOn(vm, 'handleClosed')

    await wrapper.setProps({ modelValue: true });
    await wrapper.setProps({ modelValue: false });

    await vi.waitFor(() => {
      expect(handleClosedSpy).toHaveBeenCalled()
    })

    expect(wrapper.findComponent({ name: 'el-skeleton' }).exists()).toBe(true)
    expect(wrapper.find('iframe').exists()).toBe(false)
  })
})
