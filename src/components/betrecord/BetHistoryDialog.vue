<script setup lang="ts">
import { nextTick } from 'vue'

const visible = defineModel({
  type: Boolean,
  default: false,
  required: true
})

const props = defineProps({
  title: {
    type: String,
    default: '',
    required: true
  },
  ifrUrl: {
    type: String,
    default: '',
    required: true
  }
})

const dialogIframe: globalThis.Ref = ref(null)
const isIframeLoading = ref(true)
const isShowIframe = ref(false)

const handleOpened = async() => {
  isShowIframe.value = true
  await nextTick()
  dialogIframe.value.onload = () => {
    isIframeLoading.value = false
  }
}

const handleClosed = () => {
  isIframeLoading.value = true
  isShowIframe.value = false
}
</script>

<template>
  <div class="card-table-dialog">
    <el-dialog
      v-model="visible"
      :title="props.title"
      width="70%"
      height="100%"
      @opened="handleOpened"
      @closed="handleClosed"
    >
      <el-skeleton v-if="isIframeLoading" :rows="5" animated />
      <iframe v-if="isShowIframe" :src="ifrUrl" :class="{ hide: isIframeLoading }" frameborder="0" width="100%" height="500px" ref="dialogIframe"></iframe>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.card-table-dialog {
  .hide {
    display: none;
  }
}
</style>