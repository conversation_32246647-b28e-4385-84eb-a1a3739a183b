import { flushPromises, mount } from '@vue/test-utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import { ICardRecord } from '@/types/betrecord'
import CardIndex from './CardIndex.vue'
import { getCardRecord } from '@/api/betrecord'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import { setActivePinia, createPinia } from 'pinia'

dayjs.extend(timezone)
dayjs.extend(utc)

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      'S_TOTAL': 'Total',
    },
  },
})

vi.mock('vue3-cookies', () => ({
  useCookies: vi.fn().mockReturnValue({
    cookies: {
      get: (key: string) => {
        if (key === 'session_id') return 'session_id'
        if (key === 'lang') return 'en'
        return null
      }
    }
  }),
}))

vi.mock('@/stores/cardGame', () => ({
  useCardGameStore: vi.fn(() => ({
    fetchGameList: vi.fn(),
    gameList: ref([
      {
        gameId: 66104,
        gameKind: 66,
        name: '21點百家樂',
        link: '/six/game_entry.php?game_id=66104&html5=0&lang=zh-tw&platform_id=66&sid=bga430b8b033d031da5010e02af93b91a70a1f9307',
        icon: '',
        externalId: '12027'
      }
    ]),
    gameNames: ref({
      66104: '21點百家樂'
    })
  }))
}))

vi.mock('@/api/betrecord', () => ({
  getCardRecord: vi.fn(),
}))

vi.mock('@/components/betrecord/CardSearch.vue', () => ({
  default: {
    name: 'CardSearch',
    props: ['gameId', 'startTime', 'endTime', 'gameList', 'startTimes', 'endTimes'],
    template: '<div class="mock-card-search">Mock CardSearch</div>',
  },
}))
vi.mock('@/components/betrecord/CardTable.vue', () => ({
  default: {
    name: 'CardTable',
    props: ['topTableData', 'cardTableData', 'isLoading'],
    template: '<div class="mock-card-table">Mock CardTable</div>',
  },
}))
vi.mock('@/components/mcenter/PaginationComponent.vue', () => ({
  default: {
    name: 'PaginationComponent',
    props: ['currentPage', 'totalPage'],
    template: '<div class="mock-pagination-component">Mock PaginationComponent</div>',
  },
}))

const mockCardRecord = {
  searchRange: {
    startDate: '2025-04-28',
    endDate: '2025-05-05'
  },
  wagersList: [
    {
      id: 73264,
      wagersDate: '2025-04-28',
      gameType: 66104,
      resultStatus: 'LOSE',
      betAmount: '10.00',
      payoff: '-10.00',
      wagersDetailUrl:
        'https://pokerworldbattle.com/BetRecord/manager/managerPage.php?userID=9430&wagersID=2055141&data=8UaxQ4VX3o2f8XM%252BczOD26tlF6bnVmZAH2dIqZJ0nfbIMh9sMuQMtLRG0OGPGMSH&lang=zh-cn&GameType=12027&gmt=0&fromWeb='
    }
  ],
  total: {
    totalBetAmount: '180.00',
    totalPayoff: '110.90'
  },
  pagination: {
    currentPage: 1,
    pageLimit: 20,
    total: 22,
    totalPage: 2
  },
  maintain: false,
  maintainInfo: ''
} as ICardRecord

describe('CardIndex', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('renders correctly when not in maintenance', async () => {
    const wrapper = mount(CardIndex, {
      global: {
        plugins: [i18n],
      },
    })

    expect(wrapper.findComponent({ name: 'el-skeleton' }).exists()).toBe(true)

    await wrapper.vm.$nextTick()
    await flushPromises()
    await nextTick()

    expect(wrapper.findComponent({ name: 'el-skeleton' }).exists()).toBe(false)
    expect(wrapper.findComponent({ name: 'CardSearch' }).exists()).toBe(true)
    expect(wrapper.findComponent({ name: 'CardTable' }).exists()).toBe(true)
    expect(wrapper.findComponent({ name: 'PaginationComponent' }).exists()).toBe(true)
  })

  it('renders maintenance message when in maintenance', async () => {
    const mockCardRecordData = {
      maintain: true,
      maintainInfo: 'Maintenance in progress'
    } as ICardRecord

    vi.mocked(getCardRecord).mockResolvedValue(mockCardRecordData)

    const wrapper = mount(CardIndex, {
      global: {
        plugins: [i18n],
      }
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    wrapper.findComponent({ name: 'CardSearch' }).vm.$emit('search')
    await flushPromises()

    expect(wrapper.html()).toContain('Maintenance in progress')
  })

  it('fetches card record on search', async () => {
    vi.mocked(getCardRecord).mockResolvedValue(mockCardRecord)

    const wrapper = mount(CardIndex, {
      global: {
        plugins: [i18n],
      },
    })

    const openSpy = vi.spyOn(wrapper.vm, 'handleSearch' as any)

    await wrapper.vm.$nextTick()
    await nextTick()

    const cardSearch = wrapper.findComponent({ name: 'CardSearch' })
    await cardSearch.vm.$emit('search')

    expect(openSpy).toHaveBeenCalled()
  })

  it('fetches card record on current change', async () => {
    vi.mocked(getCardRecord).mockResolvedValue(mockCardRecord)

    const wrapper = mount(CardIndex, {
      global: {
        plugins: [i18n],
      },
    })

    const openSpy = vi.spyOn(wrapper.vm, 'fetchCardRecord' as any)

    await wrapper.vm.$nextTick()
    await nextTick()
      
    const paginationComponent = wrapper.findComponent({ name: 'PaginationComponent' })
    await paginationComponent.vm.$emit('currentChange')

    expect(openSpy).toHaveBeenCalled()
  })

  it('shows error message when fetch fails', async () => {
    const mockError = new Error('Failed to fetch data')
    vi.mocked(getCardRecord).mockRejectedValue(mockError)
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())

    const wrapper = mount(CardIndex, {
      global: {
        plugins: [i18n],
      },
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    wrapper.findComponent({ name: 'CardSearch' }).vm.$emit('search')
    await flushPromises()

    expect(console.error).toHaveBeenCalledWith(mockError)
    expect(ElMessage.error).toHaveBeenCalledWith('Failed to fetch data')
  })
})
