<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { getCardRecord } from '@/api/betrecord'
import PaginationComponent from '@/components/mcenter/PaginationComponent.vue'
import { ICardRecord } from '@/types/betrecord'
import CardSearch from '@/components/betrecord/CardSearch.vue'
import CardTable from '@/components/betrecord/CardTable.vue'
import { useCookies } from 'vue3-cookies'
import { useCardGameStore } from "@/stores/cardGame"
import { storeToRefs } from 'pinia'
import dayjs from 'dayjs'

const { t } = useI18n()
const { cookies } = useCookies()
const cardGameStore = useCardGameStore()
const { gameList, gameNames } = storeToRefs(cardGameStore)
const maintainText = ref('')
const gameId = ref()
const topTableData = ref([
  {
    sum: t('S_TOTAL'),
    totalCount: '--',
    totalBetAmount: '--',
    totalPayoff: '--',
  }
]) 
const cardTableData = ref<any[]>([])
const totalPage = ref(1)
const qPage = ref(1)
const startTime = ref('')
const startTimes = ref({})
const endTime = ref('')
const endTimes = ref({})
const isLoading = ref(false)
const isShowSkeleton = ref(true)
const isCardMaintain = ref(false)

const getDateRange = (dateTime: number) => {
  let result = [dayjs.tz(dateTime, 'Etc/GMT+4').format('YYYY-MM-DD')]
  for (let x = 1; x <= 6; x++) {
    result.push(dayjs.tz(dateTime, 'Etc/GMT+4').subtract(x, 'day').format('YYYY-MM-DD'))
  }
  
  return result
}

const fetchCardRecord = async () => {
  isLoading.value = true
  try {
    const param = {
      session_id: cookies.get('SESSION_ID'),
      lang: cookies.get('lang'),
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
      game_id: gameId.value,
      start_date: startTime.value,
      end_date: endTime.value,
      page: qPage.value
    }

    const res = await getCardRecord(param)
    
    if(res.maintain) {
      isCardMaintain.value = true
      maintainText.value = res.maintainInfo
    } else {
      const data = res as ICardRecord
      cardTableData.value =  data.wagersList.map(item => ({
        ...item,
        gameType: gameNames.value[item.gameType] || item.gameType
      }))
      totalPage.value =  data.pagination.totalPage
      topTableData.value = [{
        ...data.total,
        totalCount: String(data.pagination.total),
        sum: t('S_TOTAL'),
      }]
    }
  } catch (error) {
    ElMessage.error((error as Error).message)
    console.error(error)
  } finally {
    isLoading.value = false
    isShowSkeleton.value = false
  }
}

const handleSearch = () => {
  qPage.value = 1
  fetchCardRecord()
}

const init = async () => {
  const dateList = getDateRange(new Date().getTime())
  startTime.value = dateList[0]
  endTime.value = dateList[0]
  startTimes.value = dateList
  endTimes.value = dateList

  try {
    await cardGameStore.fetchGameList(false)
  } catch(error) {
    ElMessage.error((error as Error).message)
    console.error(error)
  } finally {
    isShowSkeleton.value = false
  }
}

init()
</script>

<template>
  <template v-if="isCardMaintain === false">
    <el-skeleton v-if="isShowSkeleton" class="skeleton" :rows="5" animated />
    <div 
      v-else
      v-loading="isLoading"
    >
      <CardSearch
        v-model:game-id="gameId"
        v-model:start-time="startTime"
        v-model:end-time="endTime"
        :game-list="gameList"
        :start-times="startTimes"
        :end-times="endTimes"
        @search="handleSearch"
      />
      <CardTable
        :top-table-data="topTableData"
        :card-table-data="cardTableData"
      />
      <PaginationComponent
        v-model:current-page="qPage"
        :page-count="totalPage"
        @current-change="fetchCardRecord"
      />
    </div>
  </template>
  <template v-else>
    <div v-html="maintainText"></div>
  </template>
</template>