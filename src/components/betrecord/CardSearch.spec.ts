import { mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import CardSearch from './CardSearch.vue'
import { ElMessage } from 'element-plus'

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      'S_NO_DATA': 'No data',
      'S_ENTER_GAME_NAME': 'Enter game name',
      'S_SEARCH': 'Search',
      "S_FIELD_REQUIRE": "Field cannot be empty"
    },
  },
})

const mockTimes = {
  "2025-02-19": "2025-02-19",
  "2025-02-18": "2025-02-18",
  "2025-02-17": "2025-02-17",
  "2025-02-16": "2025-02-16",
  "2025-02-15": "2025-02-15",
  "2025-02-14": "2025-02-14",
  "2025-02-13": "2025-02-13",
  "2025-02-12": "2025-02-12"
}
const mockGameList = [
  {
    gameId: 66001,
    gameKind: 66,
    name: '炸金花',
    link: '/six/game_entry.php?game_id=66001&html5=0&lang=zh-tw&platform_id=66&sid=bga430b8b033d031da5010e02af93b91a70a1f9307',
    icon: 'Event',
    externalId: '10001'
  },
  {
    gameId: 66003,
    gameKind: 66,
    name: '通比牛牛',
    link: '/six/game_entry.php?game_id=66003&html5=0&lang=zh-tw&platform_id=66&sid=bga430b8b033d031da5010e02af93b91a70a1f9307',
    icon: 'Recommend',
    externalId: '10004'
  }
]
const propsData = {
  startTimes: { ...mockTimes },
  endTimes: { ...mockTimes },
  gameList: mockGameList,
  gameId: undefined,
  startTime: '2025-02-19',
  endTime: '2025-02-19'
}

describe('CardSearch', () => {
  it('renders correctly with default props', async () => {
    const wrapper = mount(CardSearch, {
      props: { ...propsData },
      global: {
        plugins: [i18n],
      },
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    const elSelects = wrapper.findAllComponents({ name: 'el-select' })
    expect(elSelects).toHaveLength(3)
    elSelects.forEach((elSelect) => {
      expect(elSelect.exists()).toBe(true)
    })

    const gameListSelect = elSelects[0]
    const gameListOptions = gameListSelect.findAllComponents({ name: 'el-option' })
    expect(gameListSelect.text()).toBe('Enter game name')
    expect(gameListOptions.length).toBe(mockGameList.length)
    mockGameList.forEach((item, index) => {
      expect(gameListOptions[index].text()).toBe(item.name)
    })

    const startTimesSelect = elSelects[1]
    const startTimesOptions = startTimesSelect.findAllComponents({ name: 'el-option' })
    expect(startTimesSelect.text()).toBe('2025-02-19')
    expect(startTimesOptions.length).toBe(Object.values(mockTimes).length)
    Object.values(mockTimes).forEach((item, index) => {
      expect(startTimesOptions[index].text()).toBe(item)
    })

    const endTimesSelect = elSelects[2]
    const endTimesOptions = endTimesSelect.findAllComponents({ name: 'el-option' })
    expect(endTimesSelect.text()).toBe('2025-02-19')
    expect(endTimesOptions.length).toBe(Object.values(mockTimes).length)
    Object.values(mockTimes).forEach((item, index) => {
      expect(endTimesOptions[index].text()).toBe(item)
    })

    expect(wrapper.findComponent({ name: 'el-button' }).text()).toBe('Search')
  })

  it('renders time select correctly when select the time', async () => {
    const wrapper = mount(CardSearch, {
      props: {
        ...propsData,
        startTime: '2025-02-16',
      },
      global: {
        plugins: [i18n],
      },
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    const elSelects = wrapper.findAllComponents({ name: 'el-select' })

    const startTimesSelect = elSelects[1]
    const startTimesOptions = startTimesSelect.findAllComponents({ name: 'el-option' })
    const endTimesSelect = elSelects[2]
    const endTimesOptions = endTimesSelect.findAllComponents({ name: 'el-option' })
    
    endTimesOptions.forEach((option) => {
      const isDisabled = option.text() < startTimesSelect.text()
      expect(option.attributes('aria-disabled')).toBe(isDisabled ? 'true' : undefined)
    })

    await wrapper.setProps({ endTime: "2025-02-15" });

    startTimesOptions.forEach((option) => {
      const isDisabled = option.text() > endTimesSelect.text()
      expect(option.attributes('aria-disabled')).toBe(isDisabled ? 'true' : undefined)
    })

    endTimesOptions.forEach((option) => {
      const isDisabled = option.text() < startTimesSelect.text()
      expect(option.attributes('aria-disabled')).toBe(isDisabled ? 'true' : undefined)
    })
  })

  it('calls the handleClick correctly on button click', async () => {
    const wrapper = mount(CardSearch, {
      props: { ...propsData },
      global: {
        plugins: [i18n],
      },
    })

    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())

    await wrapper.vm.$nextTick()
    await nextTick()

    const elButton = wrapper.findComponent({ name: 'el-button' })
    const gameListSelect = wrapper.findComponent({ name: 'el-select' })

    await elButton.trigger('click')

    expect(gameListSelect.attributes('class')?.includes('empty')).toBe(true)
    expect(ElMessage.error).toHaveBeenCalledWith('Field cannot be empty')

    await wrapper.setProps({ gameId: 66003 });
    await elButton.trigger('click')

    expect(gameListSelect.attributes('class')?.includes('empty')).toBe(false)
    expect(wrapper.emitted('search')).toBeTruthy()
  })
})
