<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import type { IGameInfo } from '@/types/game'

const { t } = useI18n()
const props = defineProps({
  gameList: {
    type: Array as PropType<IGameInfo[]>,
    default: () => ([]),
    required: true
  },
  startTimes: {
    type: Object,
    default: () => ({}),
    required: true
  },
  endTimes: {
    type: Object,
    default: () => ({}),
    required: true
  }
})

const gameId = defineModel('gameId', {
  type: Number,
  default: undefined
})

const startTime = defineModel('startTime', {
  type: String,
  default: ''
})

const endTime = defineModel('endTime', {
  type: String,
  default: ''
})

const emit = defineEmits(['search'])

const gameidEmpty = ref(false)

const handleClick = () => {
  if(!gameId.value) {
    gameidEmpty.value = true
    ElMessage.error(t('S_FIELD_REQUIRE'))
  } else {
    gameidEmpty.value = false
    emit('search')
  }
}

</script>

<template>
  <div class="search">
    <el-select 
      v-model="gameId"
      :placeholder="t('S_ENTER_GAME_NAME')"
      style="width: 160px"
      :class="!gameId && gameidEmpty ? 'empty' : ''"
      filterable
      :no-match-text="t('S_NO_DATA')"
    >
      <el-option
        v-for="item in props.gameList"
        :key="item.gameId"
        :label="item.name"
        :value="item.gameId"
      />
    </el-select>
    <el-select v-model="startTime" placeholder="Select" style="width: 200px">
      <el-option
        v-for="item in props.startTimes"
        :key="item"
        :label="item"
        :value="item"
        :disabled="String(item) > endTime"
      />
    </el-select>
    <span>{{ '-' }}</span>
    <el-select v-model="endTime" placeholder="Select" style="width: 200px">
      <el-option
        v-for="item in props.endTimes"
        :key="item"
        :label="item"
        :value="item"
        :disabled="String(item) < startTime"
      />
    </el-select>
    <el-button 
      color="var(--mcenter-effectivebetting-button-bg)"
      round
      @click="handleClick"
    >
      {{ t('S_SEARCH') }}
    </el-button>
  </div>
</template>

<style lang="scss" scoped>
.search {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-left: 5px;

  .el-select {
    width: 30%;

    &.empty {
      border: 1px solid #ff5656;
      border-radius: 5px;
    }
  }

  :deep(.el-button) {
    span {
      color: white;
    }

    &:hover {
      background: var(--mcenter-effectivebetting-button-bg);
      border-color: var(--mcenter-effectivebetting-button-bg);
    }
    &:active {
      background: var(--mcenter-effectivebetting-button-bg);
    }
  }
}
</style>