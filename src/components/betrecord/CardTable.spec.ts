import { mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import CardTable from './CardTable.vue'

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      'S_COUNT': 'Count',
      'S_WAGER_AMOUNT': 'Wager Amount',
      'S_PAYOFF_PAYOFF': 'Win gold',
      'S_NODATA': 'There is no betting information today, or your bet today has already resulted.',
      'S_WAGER_DATE': 'Wager Date',
      'S_BET_NUM': 'Bet Number',
      'S_GAMETYPE2': 'Game Type',
      'S_RESULT': 'Result',
      'S_BETHISTORYBTN': 'Bet History'
    },
  },
})

vi.mock('@/components/betrecord/BetHistoryDialog.vue', () => ({
  default: {
    name: 'BetHistoryDialog',
    props: ['modelValue', 'title', 'ifrUrl'],
    template: '<div class="mock-bet-history-dialog">Mock BetHistoryDialog</div>',
  },
}))

const propsData = {
  topTableData: [
    {
      sum: '总计',
      totalCount: '2',
      totalBetAmount: '4.00',
      totalPayoff: '2.00'
    }
  ],
  cardTableData: [
    {
      id: 73264,
      wagersDate: '2025-04-28',
      gameType: '21點百家樂',
      resultStatus: 'LOSE',
      betAmount: '10.00',
      payoff: '-10.00',
      wagersDetailUrl:
        'https://pokerworldbattle.com/BetRecord/manager/managerPage.php?userID=9430&wagersID=2055141&data=8UaxQ4VX3o2f8XM%252BczOD26tlF6bnVmZAH2dIqZJ0nfbIMh9sMuQMtLRG0OGPGMSH&lang=zh-cn&GameType=12027&gmt=0&fromWeb='
    },
    {
      id: 73263,
      wagersDate: '2025-04-28',
      gameType: '21點百家樂',
      resultStatus: 'LOSE',
      betAmount: '10.00',
      payoff: '-10.00',
      wagersDetailUrl:
        'https://pokerworldbattle.com/BetRecord/manager/managerPage.php?userID=9430&wagersID=2055140&data=8UaxQ4VX3o2f8XM%252BczOD2%252BR%252BTBhBWs%252BdOPqoR91XyBO%252FZZ%252B0eo68mBC%252BHmLYvQRl&lang=zh-cn&GameType=12027&gmt=0&fromWeb='
    }
  ]
}

describe('CardTable', () => {
  it('renders correctly with default props', async () => {
    const wrapper = mount(CardTable, {
      props: { ...propsData },
      global: {
        plugins: [i18n],
      },
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    const elTables = wrapper.findAllComponents({ name: "el-table" })
    const topTable = elTables[0]
    const topTableHeaderText = ['', 'Count', 'Wager Amount', 'Win gold']
    const topTableHeaderCells = topTable.findAll('thead .cell')
    const topTableBodyText = ['总计', '2', '4.00', '2.00']
    const topTableBodyCells = topTable.findAll('tbody .cell')
    const cardTable = elTables[1]
    const cardTableHeaderText = ['Wager Date', 'Bet Number', 'Game Type', 'Result', 'Wager Amount', 'Win gold']
    const cardTableHeaderCells = cardTable.findAll('thead .cell')
    const cardTableBodyText = [
      ...['2025-04-28', '73264', '21點百家樂', 'LOSE', '10.00', '-10.00'],
      ...['2025-04-28', '73263', '21點百家樂', 'LOSE', '10.00', '-10.00']
    ]
    const cardTableBodyCells = cardTable.findAll('tbody .cell')

    expect(topTableHeaderCells.every((cell, index) => cell.text() === topTableHeaderText[index] )).toBe(true)
    expect(topTableBodyCells.every((cell, index) => cell.text() === topTableBodyText[index] )).toBe(true)
    expect(topTableBodyCells[3].find('span').attributes('style')).toBeFalsy()
    expect(cardTableHeaderCells.every((cell, index) => cell.text() === cardTableHeaderText[index] )).toBe(true)
    expect(cardTableBodyCells.every((cell, index) => cell.html().includes(cardTableBodyText[index]) )).toBe(true)
    expect(wrapper.findComponent({ name: 'BetHistoryDialog' }).exists()).toBe(true)

    await wrapper.setProps({
      topTableData: [{
        sum: "总计",
        totalCount: "2",
        totalBetAmount: "4.00",
        totalPayoff: "-2.00"
      }]
    })

    expect(topTableBodyCells[3].find('span').attributes('style')).toBeTruthy()
  })

  it('calls the openDialog on click the bet-amount span', async () => {
    const wrapper = mount(CardTable, {
      props: { ...propsData },
      global: {
        plugins: [i18n],
      },
    })

    const vm = wrapper.vm as any
    const openDialogSpy = vi.spyOn(vm, 'openDialog')

    await wrapper.vm.$nextTick()
    await nextTick()

    const elTables = wrapper.findAllComponents({ name: "el-table" })
    const cardTable = elTables[1]
    const betAmountEl = cardTable.find('tbody .cell .bet-amount')

    await betAmountEl.trigger('click')
    expect(openDialogSpy).toHaveBeenCalledWith({
      id: 73264,
      wagersDate: '2025-04-28',
      gameType: '21點百家樂',
      resultStatus: 'LOSE',
      betAmount: '10.00',
      payoff: '-10.00',
      wagersDetailUrl:
        'https://pokerworldbattle.com/BetRecord/manager/managerPage.php?userID=9430&wagersID=2055141&data=8UaxQ4VX3o2f8XM%252BczOD26tlF6bnVmZAH2dIqZJ0nfbIMh9sMuQMtLRG0OGPGMSH&lang=zh-cn&GameType=12027&gmt=0&fromWeb='
    })
  })
})
