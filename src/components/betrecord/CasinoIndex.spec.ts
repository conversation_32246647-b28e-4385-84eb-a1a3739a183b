import { flushPromises, mount } from '@vue/test-utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import { ICasinoGame, ICasinoDateRecord } from '@/types/betrecord'
import CasinoIndex from './CasinoIndex.vue'
import { getCasinoGame, getCasinoDateRecord } from '@/api/betrecord'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

dayjs.extend(timezone)
dayjs.extend(utc)

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      'S_TOTAL': 'Total',
    },
  },
})

vi.mock('@/api/betrecord', () => ({
  getCasinoDateRecord: vi.fn(),
  getCasinoComboRecord: vi.fn(),
  getCasinoGame: vi.fn()
}))

vi.mock('@/components/betrecord/CasinoSearch.vue', () => ({
  default: {
    name: 'CasinoSearch',
    props: ['gameId', 'comboVal', 'searchTypeVal', 'startTime', 'endTime', 'startTimes', 'endTimes', 'allGamelist', 'comboLists'],
    template: '<div class="mock-casino-search">Mock CasinoSearch</div>',
  },
}))
vi.mock('@/components/betrecord/CasinoTable.vue', () => ({
  default: {
    name: 'CasinoTable',
    props: ['topTableData', 'casinoTableData', 'isLoading'],
    template: '<div class="mock-casino-table">Mock CasinoTable</div>',
  },
}))
vi.mock('@/components/mcenter/PaginationComponent.vue', () => ({
  default: {
    name: 'PaginationComponent',
    props: ['currentPage', 'totalPage'],
    template: '<div class="mock-pagination-component">Mock PaginationComponent</div>',
  },
}))

const mockCasinoGame = {
  searchRange: {
    startDate: '2025-04-04',
    endDate: '2025-04-11'
  },
  gameList: {
    bbRecord: [
      {
        id: 5094,
        name: '金瓶梅2'
      },
      {
        id: 5909,
        name: '開心消消樂'
      },
      {
        id: 5251,
        name: '一桿清台'
      }
    ],
    bbComboRecord: [
      {
        id: 5122,
        name: '球球大作戰'
      },
      {
        id: 5143,
        name: '糖果派對3'
      },
      {
        id: 5150,
        name: '寶石傳奇'
      }
    ]
  },
  maintain: false,
  maintainInfo: '',
} as ICasinoGame
const mockCasinoDateRecord = {
  isMaintain: false,
  searchRange: {
    startDate: '2024-12-02',
    endDate: '2024-12-09'
  },
  wagersList: [
    {
      id: 5200008107339,
      wagersDate: '2024-12-06 07:17:32',
      gameType: 5143,
      betAmount: '5.00',
      payoff: '-5.00',
      jpAmount: '￥ 0.00',
      wagersDetailUrl: "https://bbgp-game1.casinovir999.net/ipl/portal.php/game/httpredirect?gametype=5198&hallid=3820474&id=456121189&key=9d30b90ae88139e0133c53e79af5aa5a&lang=zh-tw&p_service=gp&type=casinodetail&wid=5200008626490"
    }
  ],
  total: {
    totalCount: 178,
    totalBetAmount: '755.00',
    totalPayoff: '833.10',
    totalJpAmount: '￥ 0.00'
  },
  pagination: {
    currentPage: 1,
    pageLimit: 20,
    total: 178,
    totalPage: 9
  },
  maintain: false,
  maintainInfo: ''
} as ICasinoDateRecord

describe('CardIndex', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders correctly when not in maintenance', async () => {
    vi.mocked(getCasinoGame).mockResolvedValue(mockCasinoGame)
    vi.mocked(getCasinoDateRecord).mockResolvedValue(mockCasinoDateRecord)

    const wrapper = mount(CasinoIndex, {
      global: {
        plugins: [i18n],
      },
    })

    expect(wrapper.findComponent({ name: 'el-skeleton' }).exists()).toBe(true)

    await wrapper.vm.$nextTick()
    await flushPromises()
    await nextTick()

    expect(getCasinoGame).toHaveResolved()
    expect(wrapper.findComponent({ name: 'el-skeleton' }).exists()).toBe(false)
    expect(wrapper.findComponent({ name: 'CasinoSearch' }).exists()).toBe(true)
    expect(wrapper.findComponent({ name: 'CasinoTable' }).exists()).toBe(true)
    expect(wrapper.findComponent({ name: 'PaginationComponent' }).exists()).toBe(true)
  })

  it('renders maintenance message when getCasinoDateRecord is in maintenance', async () => {
    const mockCasinoRecordData = {
      maintain: true,
      maintainInfo: 'Maintenance in progress',
    } as ICasinoDateRecord

    vi.mocked(getCasinoGame).mockResolvedValue(mockCasinoGame)
    vi.mocked(getCasinoDateRecord).mockResolvedValue(mockCasinoRecordData)

    const wrapper = mount(CasinoIndex, {
      global: {
        plugins: [i18n],
      }
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    const casinoSearch = wrapper.findComponent({ name: 'CasinoSearch' })
    await casinoSearch.vm.$emit('search')
    await flushPromises()

    expect(wrapper.html()).toContain('Maintenance in progress')
  })

  it('renders maintenance message when getCasinoGame is in maintenance', async () => {
    const mockCasinoGameData = {
      ...mockCasinoGame,
      maintain: true,
      maintainInfo: 'Maintenance in progress',
    } as ICasinoGame

    vi.mocked(getCasinoGame).mockResolvedValue(mockCasinoGameData)
    vi.mocked(getCasinoDateRecord).mockResolvedValue(mockCasinoDateRecord)

    const wrapper = mount(CasinoIndex, {
      global: {
        plugins: [i18n],
      }
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    expect(wrapper.html()).toContain('Maintenance in progress')
  })

  it('fetches casino record on search', async () => {
    vi.mocked(getCasinoGame).mockResolvedValue(mockCasinoGame)
    vi.mocked(getCasinoDateRecord).mockResolvedValue(mockCasinoDateRecord)

    const wrapper = mount(CasinoIndex, {
      global: {
        plugins: [i18n],
      },
    })

    const openSpy = vi.spyOn(wrapper.vm, 'handleSearchEvent' as any)

    await wrapper.vm.$nextTick()
    await nextTick()

    const casinoSearch = wrapper.findComponent({ name: 'CasinoSearch' })
    await casinoSearch.vm.$emit('search')

    expect(openSpy).toHaveBeenCalled()
  })

  it('calls handleSearch function on current change', async () => {
    vi.mocked(getCasinoGame).mockResolvedValue(mockCasinoGame)
    vi.mocked(getCasinoDateRecord).mockResolvedValue(mockCasinoDateRecord)

    const wrapper = mount(CasinoIndex, {
      global: {
        plugins: [i18n],
      },
    })

    const openSpy = vi.spyOn(wrapper.vm, 'fetchCasinoRecord' as any)

    await wrapper.vm.$nextTick()
    await nextTick()
      
    const paginationComponent = wrapper.findComponent({ name: 'PaginationComponent' })
    await paginationComponent.vm.$emit('currentChange')

    expect(openSpy).toHaveBeenCalled()
  })

  it('shows error message when fetch getCasinoGame fails', async () => {
    const mockError = new Error('Failed to fetch data')
    vi.mocked(getCasinoGame).mockRejectedValue(mockError)
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())

    const wrapper = mount(CasinoIndex, {
      global: {
        plugins: [i18n],
      },
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    expect(console.error).toHaveBeenCalledWith(mockError)
    expect(ElMessage.error).toHaveBeenCalledWith('Failed to fetch data')
  })
  
  it('shows error message when fetch getCasinoDateRecord fails', async () => {
    const mockError = new Error('Failed to fetch data')
    vi.mocked(getCasinoDateRecord).mockRejectedValue(mockError)
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())

    const wrapper = mount(CasinoIndex, {
      global: {
        plugins: [i18n],
      },
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    const casinoSearch = wrapper.findComponent({ name: 'CasinoSearch' })
    await casinoSearch.vm.$emit('search')

    expect(console.error).toHaveBeenCalledWith(mockError)
    expect(ElMessage.error).toHaveBeenCalledWith('Failed to fetch data')
  })
})
