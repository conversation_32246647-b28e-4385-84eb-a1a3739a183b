<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { getCasinoGame, getCasinoDateRecord, getCasinoComboRecord } from '@/api/betrecord'
import PaginationComponent from '@/components/mcenter/PaginationComponent.vue'
import { ICasinoGame, ICasinoComboRecord, ICasinoDateRecord } from '@/types/betrecord'
import CasinoSearch from './CasinoSearch.vue'
import CasinoTable from './CasinoTable.vue'
import { useCookies } from 'vue3-cookies'
import dayjs from 'dayjs'

const { t } = useI18n()
const { cookies } = useCookies()
const maintainText = ref('')

const startTime = ref('')
const startTimes = ref({})
const endTime = ref('')
const endTimes = ref({})
const searchTypeVal = ref('bbRecord')
const comboVal = ref(5)
const comboLists = ref<number[]>([5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20])
const gameId = ref()
const allGamelist = ref<{[key: string]: {id: number, name: string}[]}>({})
const topTableData = ref([
  {
    sum: t('S_TOTAL'),
    totalCount: '--',
    totalBetAmount: '--',
    totalPayoff: '--',
    totalJpAmount: '--',
  }
]) 
const casinoTableData = ref<any[]>([])
const totalPage = ref(1)
const qPage = ref(1)
const isLoading = ref(false)
const isShowSkeleton = ref(true)
const isCasinoMaintain = ref(false)
const tableSearchTypeVal = ref('bbRecord')
let gameNameMap = {} as { [key: string]: string }

const getDateRange = (dateTime: number) => {
  let result = [dayjs.tz(dateTime, 'Etc/GMT+4').format('YYYY-MM-DD')]
  for (let x = 1; x <= 6; x++) {
    result.push(dayjs.tz(dateTime, 'Etc/GMT+4').subtract(x, 'day').format('YYYY-MM-DD'))
  }
  
  return result
}

const fetchCasinoGame = async () => {
  isLoading.value = true

  try {
    const params = {
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
      session_id: cookies.get('SESSION_ID'),
      lang: cookies.get('lang')
    }

    const res = await getCasinoGame(params)
    
    if (res.maintain) {
      isCasinoMaintain.value = true
      maintainText.value = res.maintainInfo
    } else {
      const data = res as ICasinoGame
      allGamelist.value = data.gameList
      gameNameMap = {
        ...data.gameList.bbRecord.reduce((acc, item) => {
          acc[item.id] = item.name
          return acc        
        }, {} as { [key: string]: string }),
        ...data.gameList.bbComboRecord.reduce((acc, item) => {
          acc[item.id] = item.name
          return acc        
        }, {} as { [key: string]: string })
      }
    }
  } catch (error) {
    ElMessage.error((error as Error).message)
    console.error(error)
  } finally {
    isLoading.value = false
    isShowSkeleton.value = false
  }
}

const fetchCasinoRecord = async () => {
  isLoading.value = true  

  try {
    const fetchRecord = searchTypeVal.value === 'bbRecord' ? fetchCasinoDateRecord : fetchCasinoComboRecord
    let res = await fetchRecord()
    
    if (res.maintain) {
      isCasinoMaintain.value = true
      maintainText.value = res.maintainInfo
      return
    }

    totalPage.value = res.pagination.totalPage

    if (searchTypeVal.value === 'bbRecord') {
      res = res as ICasinoDateRecord
      topTableData.value = [{
        sum: t('S_TOTAL'),
        totalCount: String(res.total.totalCount),
        totalBetAmount: res.total.totalBetAmount,
        totalPayoff: res.total.totalPayoff,
        totalJpAmount: res.total.totalJpAmount
      }]
    } else {
      res = res as ICasinoComboRecord
      topTableData.value = [{
        sum: t('S_TOTAL'),
        totalCount: String(res.pagination.total),
        totalBetAmount: '--',
        totalPayoff: '--',
        totalJpAmount: '--',
      }]
    }

    tableSearchTypeVal.value = searchTypeVal.value

    casinoTableData.value = res.wagersList.map(item => ({
      ...item,
      gameType: gameNameMap[item.gameType]
    }))

  } catch (error) {
    ElMessage.error((error as Error).message)
    console.error(error)
  } finally {
    isLoading.value = false
  }
}

const fetchCasinoDateRecord = async () => {
  const params = {
    hall_id: Number(localStorage.getItem('hallinfo_hallid')),
    session_id: cookies.get('SESSION_ID'),
    game_id: gameId.value,
    start_date: startTime.value,
    end_date: endTime.value,
    lang: cookies.get('lang'),
    page: qPage.value,
    scheme: location.protocol.includes('https') ? 'https' : 'http'
  }

  return getCasinoDateRecord(params)
}

const fetchCasinoComboRecord = async () => {
  const params = {
    hall_id: Number(localStorage.getItem('hallinfo_hallid')),
    session_id: cookies.get('SESSION_ID'),
    game_id: gameId.value,
    start_date: startTime.value,
    end_date: endTime.value,
    times: comboVal.value,
    page: qPage.value
  }

  return getCasinoComboRecord(params)
}

const handleSearchEvent = () => {
  qPage.value = 1
  fetchCasinoRecord()
}

const init = async () => {
  const dateList = getDateRange(new Date().getTime())
  startTime.value = dateList[0]
  endTime.value = dateList[0]
  startTimes.value = dateList
  endTimes.value = dateList
}

init()
fetchCasinoGame()
</script>

<template>
  <template v-if="isCasinoMaintain === false">
    <el-skeleton v-if="isShowSkeleton" class="skeleton" :rows="5" animated />
    <div 
      v-else
      v-loading="isLoading"
    >
      <CasinoSearch
        v-model:game-id="gameId"
        v-model:combo-val="comboVal"
        v-model:search-type-val="searchTypeVal"
        v-model:start-time="startTime"
        v-model:end-time="endTime"
        :all-game-list="allGamelist"
        :combo-lists="comboLists"
        :start-times="startTimes"
        :end-times="endTimes"
        @search="handleSearchEvent"
      />
      <CasinoTable
        :top-table-data="topTableData"
        :casino-table-data="casinoTableData"
        :search-type-val="tableSearchTypeVal"
      />
      <PaginationComponent
        v-model:current-page="qPage"
        :page-count="totalPage"
        @current-change="fetchCasinoRecord"
      />
    </div>
  </template>
  <template v-else>
    <div v-html="maintainText"></div>
  </template>
</template>