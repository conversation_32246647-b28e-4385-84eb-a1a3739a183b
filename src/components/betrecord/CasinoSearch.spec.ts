import { mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import CasinoSearch from './CasinoSearch.vue'
import { ElMessage } from 'element-plus'

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      'S_BET_RECORD2': 'Bet Record',
      'S_COMBO_RECORD': 'Bets of continuous clearing',
      'S_FIELD_REQUIRE': 'Field cannot be empty',
      "S_ENTER_GAME_NAME": "Please Enter Game Name",
      "S_NO_DATA": "No Data",
      "S_COMBO_TIMES": "Combo Times",
      "S_SEARCH": "Search",
      "times": "times"
    },
  },
})

const mockTimes = {
  "2025-02-19": "2025-02-19",
  "2025-02-18": "2025-02-18",
  "2025-02-17": "2025-02-17",
  "2025-02-16": "2025-02-16",
  "2025-02-15": "2025-02-15",
  "2025-02-14": "2025-02-14",
  "2025-02-13": "2025-02-13",
  "2025-02-12": "2025-02-12"
}
const mockAllGameList = {
  bbComboRecord: [
    { id: 5122, name: '球球大作战' },
    { id: 5143, name: '糖果派对3' }
  ],
  bbRecord: [
    { id: 5259, name: '爆利金刚' },
    { id: 5225, name: '火烧连环船' }
  ]
}
const mockComboList = [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]
const searchTypes = ['Bet Record', 'Bets of continuous clearing']
const propsData = {
  startTimes: { ...mockTimes },
  endTimes: { ...mockTimes },
  allGameList: { ...mockAllGameList },
  comboLists: mockComboList.slice(),
  startTime: '2025-02-19',
  endTime: '2025-02-19',
  searchTypeVal: 'bbRecord',
  comboVal: 5
}

describe('CardSearch', () => {
  it('renders correctly with default props', async () => {
    const wrapper = mount(CasinoSearch, {
      props: { ...propsData },
      global: {
        plugins: [i18n],
      },
    })

    const vm = wrapper.vm as any

    await vm.$nextTick()
    await nextTick()

    let elSelects = wrapper.findAllComponents({ name: 'el-select' })
    expect(elSelects).toHaveLength(4)
    elSelects.forEach((elSelect) => {
      expect(elSelect.exists()).toBe(true)
    })

    const searchTypeSelect = elSelects[0]
    const searchTypeOptions = searchTypeSelect.findAllComponents({ name: 'el-option' })
    expect(searchTypeSelect.text()).toBe('Bet Record')
    expect(searchTypeOptions.length).toBe(searchTypes.length)
    searchTypes.forEach((item, index) => {
      expect(searchTypeOptions[index].text()).toBe(item)
    })

    const gameBBRecordListSelect = elSelects[1]
    const gameBBRecordListOptions = gameBBRecordListSelect.findAllComponents({ name: 'el-option' })
    expect(gameBBRecordListSelect.text()).toBe('Please Enter Game Name')
    expect(gameBBRecordListOptions.length).toBe(mockAllGameList['bbRecord'].length)
    mockAllGameList['bbRecord'].forEach((item, index) => {
      expect(gameBBRecordListOptions[index].text()).toBe(item.name)
    })

    const startTimesSelect = elSelects[2]
    const startTimesOptions = startTimesSelect.findAllComponents({ name: 'el-option' })
    expect(startTimesSelect.text()).toBe('2025-02-19')
    expect(startTimesOptions.length).toBe(Object.values(mockTimes).length)
    Object.values(mockTimes).forEach((item, index) => {
      expect(startTimesOptions[index].text()).toBe(item)
    })

    const endTimesSelect = elSelects[3]
    const endTimesOptions = endTimesSelect.findAllComponents({ name: 'el-option' })
    expect(endTimesSelect.text()).toBe('2025-02-19')
    expect(endTimesOptions.length).toBe(Object.values(mockTimes).length)
    Object.values(mockTimes).forEach((item, index) => {
      expect(endTimesOptions[index].text()).toBe(item)
    })

    expect(wrapper.findComponent({ name: 'el-button' }).text()).toBe('Search')

    await wrapper.setProps({
      searchTypeVal: 'bbComboRecord'
    })
    await nextTick()

    elSelects = wrapper.findAllComponents({ name: 'el-select' })

    const gameBBComboRecordListSelect = elSelects[1]
    const gameBBComboRecordListOptions = gameBBComboRecordListSelect.findAllComponents({ name: 'el-option' })
    expect(gameBBComboRecordListSelect.text()).toBe('Please Enter Game Name')
    expect(gameBBComboRecordListOptions.length).toBe(mockAllGameList['bbComboRecord'].length)
    mockAllGameList['bbComboRecord'].forEach((item, index) => {
      expect(gameBBComboRecordListOptions[index].text()).toBe(item.name)
    })

    const comboListSelect = elSelects[2]
    const comboListOptions = comboListSelect.findAllComponents({ name: 'el-option' })
    expect(comboListSelect.text()).toBe('5 times')
    expect(comboListOptions.length).toBe(mockComboList.length)
    mockComboList.forEach((item, index) => {
      expect(comboListOptions[index].text()).toBe(`${item} times`)
    })
  })

  it('renders time select correctly when select the time', async () => {
    const wrapper = mount(CasinoSearch, {
      props: { ...propsData },
      global: {
        plugins: [i18n],
      },
    })

    const vm = wrapper.vm as any

    await vm.$nextTick()
    await nextTick()

    const elSelects = wrapper.findAllComponents({ name: 'el-select' })

    const startTimesSelect = elSelects[2]
    const startTimesOptions = startTimesSelect.findAllComponents({ name: 'el-option' })
    const endTimesSelect = elSelects[3]
    const endTimesOptions = endTimesSelect.findAllComponents({ name: 'el-option' })
    
    endTimesOptions.forEach((option) => {
      const isDisabled = option.text() < startTimesSelect.text()
      expect(option.attributes('aria-disabled')).toBe(isDisabled ? 'true' : undefined)
    })

    await wrapper.setProps({ endTime: "2025-02-15" });

    startTimesOptions.forEach((option) => {
      const isDisabled = option.text() > endTimesSelect.text()
      expect(option.attributes('aria-disabled')).toBe(isDisabled ? 'true' : undefined)
    })

    endTimesOptions.forEach((option) => {
      const isDisabled = option.text() < startTimesSelect.text()
      expect(option.attributes('aria-disabled')).toBe(isDisabled ? 'true' : undefined)
    })
  })

  it('renders allGameListSelect correctly on gameId and searchTypeVal change', async () => {
    const mockAllGameList = {
      bbComboRecord: [
        { id: 5122, name: '球球大作战' },
        { id: 5143, name: '糖果派对3' },
        { id: 5912, name: '连环夺宝2' }
      ],
      bbRecord: [
        { id: 5259, name: '爆利金刚' },
        { id: 5225, name: '火烧连环船' },
        { id: 5912, name: '连环夺宝2' }
      ]
    }

    const wrapper = mount(CasinoSearch, {
      props: { 
        ...propsData,
        allGameList: mockAllGameList,
        gameId: 5912
      },
      global: {
        plugins: [i18n],
      },
    })

    const vm = wrapper.vm as any

    await vm.$nextTick()
    await nextTick()

    const elSelects = wrapper.findAllComponents({ name: 'el-select' })
    const searchTypeSelect = elSelects[0]
    const allGameListSelect = elSelects[1]

    expect(searchTypeSelect.text()).toBe('Bet Record')
    expect(allGameListSelect.text()).toBe('连环夺宝2')

    await wrapper.setProps({ searchTypeVal: 'bbComboRecord' })
    expect(searchTypeSelect.text()).toBe('Bets of continuous clearing')
    expect(allGameListSelect.text()).toBe('连环夺宝2')

    await wrapper.setProps({ gameId: 5143 })
    expect(allGameListSelect.text()).toBe('糖果派对3')

    await wrapper.setProps({ searchTypeVal: 'bbRecord' })
    expect(searchTypeSelect.text()).toBe('Bet Record')
    expect(allGameListSelect.text()).toBe('Please Enter Game Name')
  })

  it('calls the handleClick correctly on button click', async () => {
    const wrapper = mount(CasinoSearch, {
      props: { ...propsData },
      global: {
        plugins: [i18n],
      },
    })

    const vm = wrapper.vm as any

    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())

    await vm.$nextTick()
    await nextTick()

    const elButton = wrapper.findComponent({ name: 'el-button' })
    const allGameListSelect = wrapper.findAllComponents({ name: 'el-select' })[1]

    await elButton.trigger('click')

    expect(allGameListSelect.attributes('class')?.includes('empty')).toBe(true)
    expect(ElMessage.error).toHaveBeenCalledWith('Field cannot be empty')

    await wrapper.setProps({ gameId: 5122 });
    await elButton.trigger('click')

    expect(allGameListSelect.attributes('class')?.includes('empty')).toBe(false)
    expect(wrapper.emitted('search')).toBeTruthy()
  })
})
