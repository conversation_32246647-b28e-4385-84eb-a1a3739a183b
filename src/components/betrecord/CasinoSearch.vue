<script setup lang="ts">
import { PropType } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const gameId = defineModel('gameId', {
  type: [Number, undefined] as PropType<number | undefined>,
  default: undefined
})

const startTime = defineModel('startTime', {
  type: String,
  default: ''
})

const endTime = defineModel('endTime', {
  type: String,
  default: ''
})

const comboVal = defineModel('comboVal', {
  type: Number,
  default: 5
})

const searchTypeVal = defineModel('searchTypeVal', {
  type: String,
  default: 'bbRecord'
})

const props = defineProps({
  allGameList: {
    type: Object as PropType<{[key: string]: {id: number, name: string}[]}>,
    default: () => ({ bbRecord: [], bbComboRecord: [] }),
    required: true
  },
  comboLists: {
    type: Array as PropType<number[]>,
    default: () => ([]),
    required: true
  },
  startTimes: {
    type: Object,
    default: () => ({}),
    required: true
  },
  endTimes: {
    type: Object,
    default: () => ({}),
    required: true
  }
})

const emit = defineEmits(['search'])

const searchTypes = ref([{
  label: t('S_BET_RECORD2'),
  value: 'bbRecord'
}, {
  label: t('S_COMBO_RECORD'),
  value: 'bbComboRecord'
}])

const gameidEmpty = ref(false)

const handleClick = () => {
  if(!gameId.value) {
    gameidEmpty.value = true
    ElMessage.error(t('S_FIELD_REQUIRE'))
  } else {
    gameidEmpty.value = false
    emit('search')
  }
}

watch(searchTypeVal, () => {
  const matchResult = props.allGameList[searchTypeVal.value].filter((game: { id: number, name: string }) => {
    return game.id === gameId.value
  })

  if (matchResult.length === 0) {
    gameId.value = undefined
  }
})
</script>

<template>
  <div class="search">
    <el-select
      v-model="searchTypeVal"
      style="width: 150px"
    >
      <el-option
        v-for="item in searchTypes"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <el-select
      v-model="gameId"
      :placeholder="t('S_ENTER_GAME_NAME')"
      style="width: 150px"
      :class="!gameId && gameidEmpty ? 'empty' : ''"
      filterable
      :no-match-text="t('S_NO_DATA')"
    >
      <el-option
        v-for="item in props.allGameList[searchTypeVal]"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      />
    </el-select>
    <el-select
      v-if="searchTypeVal === 'bbComboRecord'"
      v-model="comboVal"
      :placeholder="t('S_COMBO_TIMES')"
      style="width: 70px"
    >
      <el-option
        v-for="item in props.comboLists"
        :key="item"
        :label="`${item} ${t('times')}`"
        :value="item"
      />
    </el-select>
    <div class="date-picker">
      <el-select v-model="startTime" placeholder="Select" style="width: 200px">
        <el-option
          v-for="item in props.startTimes"
          :key="item"
          :label="item"
          :value="item"
          :disabled="String(item) > endTime"
        />
      </el-select>
      <span>{{ '-' }}</span>
      <el-select v-model="endTime" placeholder="Select" style="width: 200px">
        <el-option
          v-for="item in props.endTimes"
          :key="item"
          :label="item"
          :value="item"
          :disabled="String(item) < startTime"
        />
      </el-select>
    </div>
    <el-button 
      color="var(--mcenter-effectivebetting-button-bg)"
      round 
      @click="handleClick"
    >
      {{ t('S_SEARCH') }}
    </el-button>
  </div>
</template>

<style lang="scss" scoped>
.search {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-left: 5px;

  .date-picker {
    display: inline-block;
  }

  .el-select {
    width: 30%;

    &.empty {
      border: 1px solid #ff5656;
      border-radius: 5px;
    }
  }

  :deep(.el-button) {
    color: white;

    &:hover {
      background: var(--mcenter-effectivebetting-button-bg);
      border-color: var(--mcenter-effectivebetting-button-bg);
    }
    
    &:active {
      background: var(--mcenter-effectivebetting-button-bg);
    }
  }
}
</style>