import { mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import CasinoTable from './CasinoTable.vue'

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      'S_COUNT': 'Count',
      'S_WAGER_AMOUNT': 'Wager Amount',
      'S_PAYOFF_PAYOFF': 'Win gold',
      'S_JACKPOT_DATA': 'Jackpot Data',
      'S_NODATA': 'There is no betting information today, or your bet today has already resulted.',
      'S_BETHISTORYBTN': 'Bet History',
      'S_BET_NUM': 'Bet Number',
      'S_GAMETYPE2': 'Game Type',
      'S_MODULE_TIMES': 'Module Times',
      'S_WAGER_DATE': 'Betting Time'
    },
  },
})

vi.mock('@/components/betrecord/BetHistoryDialog.vue', () => ({
  default: {
    name: 'BetHistoryDialog',
    props: ['modelValue', 'title', 'ifrUrl'],
    template: '<div class="mock-bet-history-dialog">Mock BetHistoryDialog</div>',
  },
}))

const propsData = {
  topTableData: [{
    sum: "总计",
    totalCount: 1,
    totalBetAmount: "3.00",
    totalPayoff: "-3.00",
    totalJpAmount: "￥ 0.00"
  }],
  casinoTableData: [{
    wagersDate: "2025-02-16 22:11:24",
    gameType: "碰碰胡",
    betAmount: "3.00",
    payoff: "-3.00",
    jpAmount: "GRAND (￥ 10.0000)",
    id: 5200008417378,
    wagersDetailUrl: "https://bbgp-game1.casinovir999.net/ipl/portal.php/game/httpredirect?gametype=5198&hallid=3820474&id=456121189&key=9d30b90ae88139e0133c53e79af5aa5a&lang=zh-tw&p_service=gp&type=casinodetail&wid=5200008626490"
  }],
  searchTypeVal: 'bbRecord'
}

describe('casinoTable', () => {
  it('renders correctly with default props', async () => {
    const wrapper = mount(CasinoTable, {
      props: { ...propsData },
      global: {
        plugins: [i18n],
      },
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    const elTables = wrapper.findAllComponents({ name: "el-table" })
    const topTable = elTables[0]
    const topTableHeaderText = ['', 'Count', 'Wager Amount', 'Win gold', 'Jackpot Data(RMB)']
    const topTableHeaderCells = topTable.findAll('thead .cell')
    const topTableBodyText = ['总计', '1', '3.00', '-3.00', '￥ 0.00']
    const topTableBodyCells = topTable.findAll('tbody .cell')
    const casinoTable = elTables[1]
    const casinoTableHeaderText = ['Bet History', 'Bet Number', 'Game Type', 'Wager Amount', 'Win gold', 'Jackpot Data(RMB)']
    const casinoTableHeaderCells = casinoTable.findAll('thead .cell')
    const casinoTableBodyText = ['2025-02-16 22:11:24', '5200008417378', '碰碰胡', '3.00', '-3.00', 'GRAND (￥ 10.0000)']
    const casinoTableBodyCells = casinoTable.findAll('tbody .cell')

    expect(topTableHeaderCells.every((cell, index) => cell.text() === topTableHeaderText[index] )).toBe(true)
    expect(topTableBodyCells.every((cell, index) => cell.text() === topTableBodyText[index] )).toBe(true)
    expect(topTableBodyCells[3].find('span').attributes('style')).toBeTruthy()
    expect(casinoTableHeaderCells.every((cell, index) => cell.text() === casinoTableHeaderText[index] )).toBe(true)
    expect(casinoTableBodyCells.every((cell, index) => cell.html().includes(casinoTableBodyText[index]) )).toBe(true)
    expect(casinoTableBodyCells[4].find('span').attributes('style')).toBeTruthy()
    expect(wrapper.findComponent({ name: 'BetHistoryDialog' }).exists()).toBe(true)

    await wrapper.setProps({
      topTableData: [{
        sum: "总计",
        totalCount: 1,
        totalBetAmount: "3.00",
        totalPayoff: "3.00",
        totalJpAmount: "￥ 0.00"
      }],
      casinoTableData: [{
        wagersDate: "2025-02-16 22:11:24",
        gameType: "碰碰胡",
        betAmount: "3.00",
        payoff: "3.00",
        id: 5200008417378
      }]
    })

    expect(topTableBodyCells[3].find('span').attributes('style')).toBeFalsy()
    expect(casinoTableBodyCells[4].find('span').attributes('style')).toBeFalsy()
  })

  it('renders correctly with default props and searchType is bbComboRecord', async () => {
    const wrapper = mount(CasinoTable, {
      props: { 
        ...propsData,
        casinoTableData: [{
          wagersDate: "2025-02-16 22:11:24",
          gameType: "碰碰胡",
          id: 5200008417378,
          times: 9
        }],
        searchTypeVal: 'bbComboRecord'
      },
      global: {
        plugins: [i18n],
      },
    })

    await wrapper.vm.$nextTick()
    await nextTick()
    
    const comboTable = wrapper.findAllComponents({ name: "el-table" })[1]
    const comboTableHeaderText = ['Betting Time', 'Bet Number', 'Game Type', 'Module Times']
    const comboTableHeaderCells =comboTable.findAll('thead .cell')
    const comboTableBodyText = ['2025-02-16 22:11:24', '5200008417378', '碰碰胡', '9']
    const comboTableBodyCells =comboTable.findAll('tbody .cell')

    expect(comboTableHeaderCells.every((cell, index) => cell.text() === comboTableHeaderText[index] )).toBe(true)
    expect(comboTableBodyCells.every((cell, index) => cell.html().includes(comboTableBodyText[index]) )).toBe(true)
  })

  it('calls the openDialog on click the bet-amount span', async () => {
    const wrapper = mount(CasinoTable, {
      props: { ...propsData },
      global: {
        plugins: [i18n],
      },
    })

    const vm = wrapper.vm as any
    const openDialogSpy = vi.spyOn(vm, 'openDialog')

    await wrapper.vm.$nextTick()
    await nextTick()

    const elTables = wrapper.findAllComponents({ name: "el-table" })
    const casinoTable = elTables[1]
    const betAmountEl = casinoTable.find('tbody .cell .bet-amount')

    await betAmountEl.trigger('click')
    expect(openDialogSpy).toHaveBeenCalledWith({
      wagersDate: "2025-02-16 22:11:24",
      gameType: "碰碰胡",
      betAmount: "3.00",
      payoff: "-3.00",
      id: 5200008417378,
      jpAmount: "GRAND (￥ 10.0000)",
      wagersDetailUrl: "https://bbgp-game1.casinovir999.net/ipl/portal.php/game/httpredirect?gametype=5198&hallid=3820474&id=456121189&key=9d30b90ae88139e0133c53e79af5aa5a&lang=zh-tw&p_service=gp&type=casinodetail&wid=5200008626490"
    })
  })
})
