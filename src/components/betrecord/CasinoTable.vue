<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import BetHistoryDialog from './BetHistoryDialog.vue'

const { t } = useI18n()

const props = defineProps({
  topTableData: {
    type: Array,
    default: () => ([]),
    required: true
  },
  casinoTableData: {
    type: Array,
    default: () => ([]),
    required: true
  },
  searchTypeVal: {
    type: String,
    default: 'bbRecord',
    required: true
  }
})

const ifrUrl = ref('')
const dialogVisible = ref(false)
const dialogTitle = ref('')

const openDialog = (item: {gameType: string, wagersDetailUrl: string}) => {
  dialogVisible.value = true
  dialogTitle.value = item.gameType +'/'+ t('S_BETHISTORYBTN')
  ifrUrl.value = item.wagersDetailUrl
}
</script>

<template>
  <div class="table-container">
    <el-table
      :data="props.topTableData"
      :border="true"
      header-cell-class-name="table-header"
      cell-class-name="table-cell"
    >
      <el-table-column prop="sum" label="" />
      <el-table-column prop="totalCount" :label="t('S_COUNT')" />
      <el-table-column prop="totalBetAmount" :label="t('S_WAGER_AMOUNT')" />
      <el-table-column prop="totalPayoff" :label="t('S_PAYOFF_PAYOFF')">
        <template #default="scope">
          <span :style="{ color: scope.row.totalPayoff < 0 ? '#f00' : ''  }">
            {{ scope.row.totalPayoff }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="totalJpAmount" :label="t('S_JACKPOT_DATA')+'(RMB)'" />
    </el-table>
    <el-table
      v-if="props.searchTypeVal === 'bbRecord'"
      :data="props.casinoTableData"
      header-cell-class-name="table-header2"
      cell-class-name="table-cell2"
      :empty-text="t('S_NODATA')"
    >
      <el-table-column prop="wagersDate" :label="t('S_BETHISTORYBTN')" />
      <el-table-column prop="id" :label="t('S_BET_NUM')" />
      <el-table-column prop="gameType" :label="t('S_GAMETYPE2')" />
      <el-table-column prop="betAmount" :label="t('S_WAGER_AMOUNT')">
        <template #default="scope">
          <span class="bet-amount" @click="openDialog(scope.row)">
            {{ scope.row.betAmount }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="payoff" :label="t('S_PAYOFF_PAYOFF')">
        <template #default="scope">
          <span :style="{ color: scope.row.payoff < 0 ? '#f00' : '' }">
            {{ scope.row.payoff }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="jpAmount" :label="t('S_JACKPOT_DATA')+'(RMB)'" />
    </el-table>
    <el-table
      v-else
      :data="props.casinoTableData"
      header-cell-class-name="table-header2"
      cell-class-name="table-cell2"
      :empty-text="t('S_NODATA')"
    >
      <el-table-column prop="wagersDate" :label="t('S_WAGER_DATE')" />
      <el-table-column prop="id" :label="t('S_BET_NUM')" />
      <el-table-column prop="gameType" :label="t('S_GAMETYPE2')" />
      <el-table-column prop="times" :label="t('S_MODULE_TIMES')" />'" />
    </el-table>
    <BetHistoryDialog
      v-model="dialogVisible"
      :title="dialogTitle"
      :ifr-url="ifrUrl"
    />
  </div>
</template>

<style lang="scss" scoped>
.table-container {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;

  .el-table {
    margin: 15px;
  }

  .bet-amount {
    color: #54b9ff;
    text-decoration: underline;
    cursor: pointer;
  }

  :deep(.el-table){
    .table-header {
      color: var(--mcenter-collapse-item-text);
      font-size: 15px;
      text-align: center !important;
      background: #eee !important;
    }

    .table-header2 {
      color: var(--mcenter-table-header2--text);
      font-size: 15px;
      text-align: center !important;
      background: var(--mcenter-betinfo-table-header-bg) !important;
    }

    .table-cell {
      color: var(--mcenter-table-cell2--text);
      text-align: center !important;
    }

    .table-cell2 {
      color: var(--mcenter-table-cell2--text);
      text-align: center !important;
    }

    .el-table__empty-text {
      color: var(--mcenter-nodata);
    }
  }
}
</style>