import { flushPromises, mount } from '@vue/test-utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import { IFishRecord } from '@/types/betrecord'
import FishingIndex from './FishingIndex.vue'
import { getFishingRecord } from '@/api/betrecord'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import { setActivePinia, createPinia } from 'pinia'

dayjs.extend(timezone)
dayjs.extend(utc)

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      'S_TOTAL': 'Total',
    },
  },
})

vi.mock('vue3-cookies', () => ({
  useCookies: vi.fn().mockReturnValue({
    cookies: {
      get: (key: string) => {
        if (key === 'session_id') return 'session_id'
        if (key === 'lang') return 'en'
        return null
      }
    }
  }),
}))

vi.mock('@/stores/fishingGame', () => ({
  useFishingGameStore: vi.fn(() => ({
    fetchFishingData: vi.fn(),
    gameList: ref([
      {
        gameId: 66068,
        name: '捕魚高手',
        icon: '',
        link: '/bbgame/game_entry.php?game_id=66068&html5=0&lang=zh-tw&platform_id=66&sid=bg14fa912c488f2aa26c463a959641c284f4a3b690',
        gameKind: 66,
        ruleLink: '/bbgame/game_rule.php?game_id=66068&hall_id=3820474&lang=zh-tw&platform_id=66',
        nickname: 'BB'
      },
      {
        gameId: 38001,
        name: '捕魚大師',
        icon: 'Event',
        link: '/bbgame/game_entry.php?game_id=38001&html5=0&lang=zh-tw&platform_id=38&sid=bg14fa912c488f2aa26c463a959641c284f4a3b690',
        gameKind: 38,
        ruleLink: '/bbgame/game_rule.php?game_id=38001&hall_id=3820474&lang=zh-tw&platform_id=38',
        nickname: 'BB'
      },
      {
        gameId: 38002,
        name: '富貴漁場',
        icon: 'Event',
        link: '/bbgame/game_entry.php?game_id=38002&html5=0&lang=zh-tw&platform_id=38&sid=bg14fa912c488f2aa26c463a959641c284f4a3b690',
        gameKind: 38,
        ruleLink: '/bbgame/game_rule.php?game_id=38002&hall_id=3820474&lang=zh-tw&platform_id=38',
        nickname: 'BB'
      },
      {
        gameId: 38003,
        name: '捕魚達人',
        icon: '',
        link: '/bbgame/game_entry.php?game_id=38003&html5=0&lang=zh-tw&platform_id=38&sid=bg14fa912c488f2aa26c463a959641c284f4a3b690',
        gameKind: 38,
        ruleLink: '/bbgame/game_rule.php?game_id=38003&hall_id=3820474&lang=zh-tw&platform_id=38',
        nickname: 'BB'
      }
    ]),
    gameNames: ref({
      66068: '捕魚高手',
      38001: '捕魚大師',
      38002: '富貴漁場',
      38003: '捕魚達人'
    })
  }))
}))

vi.mock('@/api/betrecord', () => ({
  getFishingRecord: vi.fn()
}))

vi.mock('@/components/betrecord/FishingSearch.vue', () => ({
  default: {
    name: 'FishingSearch',
    props: ['gameId', 'startTime', 'endTime', 'startTimes', 'endTimes', 'gamelist'],
    template: '<div class="mock-fishing-search">Mock FishingSearch</div>',
  },
}))
vi.mock('@/components/betrecord/FishingTable.vue', () => ({
  default: {
    name: 'FishingTable',
    props: ['topTableData', 'fishingTableData', 'isLoading'],
    template: '<div class="mock-fishing-table">Mock FishingTable</div>',
  },
}))
vi.mock('@/components/mcenter/PaginationComponent.vue', () => ({
  default: {
    name: 'PaginationComponent',
    props: ['currentPage', 'totalPage'],
    template: '<div class="mock-pagination-component">Mock PaginationComponent</div>',
  },
}))

const mockFishingRecord = {
  searchRange: {
    startDate: '2025-04-18',
    endDate: '2025-04-25'
  },
  wagersList: [
    {
      id: 595701053,
      wagersDate: '2025-04-20 22:12:28',
      gameType: 38001,
      betAmount: '22.00',
      payoff: '-14.00',
      wagersDetailUrl:
        'https://fisher-test.cc/bet-record/fish/platform/wager/detail?pf=1&token=eyJpdiI6IjZTaWE2UEJYZWVLTFdKY2RxeHRHcVE9PSIsInZhbHVlIjoiOEhUNmprN1ZjUGhtM2dZWHZONFcwbHdtRjUzbHNwakM0TU9aNlRJUktKbVMwTjdTbUV2N2JrN0M0UXpRY1RaNlhJQVVIZnFwckJPVkxqUEJ1dHhIRHc9PSIsIm1hYyI6IjcwZDQ5YWQ2NTE0ZWEyM2ZlZjhmNzk0MDc4YjkwMzFlODcwMDI0MWQyZTg0YzQzZTA3NmY1MGVhM2NmNDRmYWYiLCJ0YWciOiIifQ==&lang=zh-cn'
    }
  ],
  total: {
    totalBetAmount: '82.00',
    totalPayoff: '-48.00'
  },
  pagination: {
    currentPage: 1,
    pageLimit: 20,
    total: 2,
    totalPage: 1
  },
  maintain: false,
  maintainInfo: ''
} as IFishRecord

describe('FishingIndex', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('renders correctly when not in maintenance', async () => {
    const wrapper = mount(FishingIndex, {
      global: {
        plugins: [i18n],
      },
    })

    expect(wrapper.findComponent({ name: 'el-skeleton' }).exists()).toBe(true)

    await flushPromises()

    expect(wrapper.findComponent({ name: 'el-skeleton' }).exists()).toBe(false)
    expect(wrapper.findComponent({ name: 'FishingSearch' }).exists()).toBe(true)
    expect(wrapper.findComponent({ name: 'FishingTable' }).exists()).toBe(true)
    expect(wrapper.findComponent({ name: 'PaginationComponent' }).exists()).toBe(true)
  })

  it('renders maintenance message when in maintenance', async () => {
    const mockFishingRecordData = {
      ...mockFishingRecord,
      maintain: true,
      maintainInfo: 'Maintenance in progress',
    } as IFishRecord

    vi.mocked(getFishingRecord).mockResolvedValue(mockFishingRecordData)

    const wrapper = mount(FishingIndex, {
      global: {
        plugins: [i18n],
      }
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    wrapper.findComponent({ name: 'FishingSearch' }).vm.$emit('search')
    await flushPromises()

    expect(wrapper.html()).toContain('Maintenance in progress')
  })

  it('fetches fishing record on search', async () => {
    vi.mocked(getFishingRecord).mockResolvedValue(mockFishingRecord)

    const wrapper = mount(FishingIndex, {
      global: {
        plugins: [i18n],
      },
    })

    const vm = wrapper.vm as any
    const openSpy = vi.spyOn(vm, 'handleSearch')

    await wrapper.vm.$nextTick()
    await nextTick()

    const fishingSearch = wrapper.findComponent({ name: 'FishingSearch' })
    await fishingSearch.vm.$emit('search')

    expect(openSpy).toHaveBeenCalled()
  })

  it('fetches fishing record when the current page changes', async () => {
    vi.mocked(getFishingRecord).mockResolvedValue(mockFishingRecord)

    const wrapper = mount(FishingIndex, {
      global: {
        plugins: [i18n],
      },
    })

    const vm = wrapper.vm as any
    const openSpy = vi.spyOn(vm, 'fetchFishingRecord')

    await vm.$nextTick()
    await nextTick()
      
    const paginationComponent = wrapper.findComponent({ name: 'PaginationComponent' })
    await paginationComponent.vm.$emit('currentChange')

    expect(openSpy).toHaveBeenCalled()
  })

  it('shows error message when fetch API fails', async () => {
    const mockError = new Error('Failed to fetch data')
    vi.mocked(getFishingRecord).mockRejectedValue(mockError)
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())

    const wrapper = mount(FishingIndex, {
      global: {
        plugins: [i18n],
      },
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    wrapper.findComponent({ name: 'FishingSearch' }).vm.$emit('search')
    await flushPromises()

    expect(console.error).toHaveBeenCalledWith(mockError)
    expect(ElMessage.error).toHaveBeenCalledWith('Failed to fetch data')
  })
})
