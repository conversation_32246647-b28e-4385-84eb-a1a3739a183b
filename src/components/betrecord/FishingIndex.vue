<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { getFishingRecord } from '@/api/betrecord'
import PaginationComponent from '@/components/mcenter/PaginationComponent.vue'
import { IFishRecord } from '@/types/betrecord'
import { getTodayDate } from '@/utils/date'
import FishingSearch from './FishingSearch.vue'
import FishingTable from './FishingTable.vue'
import { useCookies } from 'vue3-cookies'
import { useFishingGameStore } from '@/stores/fishingGame'
import { storeToRefs } from 'pinia'
import dayjs from 'dayjs'

const { t } = useI18n()
const { cookies } = useCookies()
const fishingStore = useFishingGameStore()
const gameId = ref()
const { gameList, gameNames } = storeToRefs(fishingStore)
const startTime = ref(getTodayDate())
const startTimes = ref({})
const endTime = ref(getTodayDate())
const endTimes = ref({})
const maintainText = ref('')
const topTableData = ref([
  {
    sum: t('S_TOTAL'),
    totalCount: '--',
    totalBetAmount: '--',
    totalPayoff: '--',
  }
])
const fishingTableData = ref<any[]>([])
const totalPage = ref(1)
const qPage = ref(1)
const isLoading = ref(false)
const isShowSkeleton = ref(true)
const isFishMaintain = ref(false)

// 只取 gameKind 38 捕魚機的遊戲
const searchGameList = computed(() => {
  return gameList.value.filter(item => item.gameKind == 38)
})

const getDateRange = (dateTime: number) => {
  let result = [dayjs.tz(dateTime, 'Etc/GMT+4').format('YYYY-MM-DD')]
  for (let x = 1; x <= 6; x++) {
    result.push(dayjs.tz(dateTime, 'Etc/GMT+4').subtract(x, 'day').format('YYYY-MM-DD'))
  }
  
  return result
}

const fetchFishingRecord = async () => {
  isLoading.value = true
  try {
    const params = {
      session_id: cookies.get('SESSION_ID'),
      lang: cookies.get('lang'),
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
      game_id: gameId.value,
      start_date: startTime.value,
      end_date: endTime.value,
      page: qPage.value
    }

    const res = await getFishingRecord(params)
    
    if (res.maintain) {
      isFishMaintain.value = true
      maintainText.value = res.maintainInfo
    } else {
      const data = res as IFishRecord
      fishingTableData.value = data.wagersList.map(item => ({
        ...item,
        gameType: gameNames.value[item.gameType] || item.gameType
      }))
      totalPage.value = data.pagination.totalPage
      topTableData.value = [{
        ...data.total,
        totalCount: String(data.pagination.total),
        sum: t('S_TOTAL'),
      }]
    }
  } catch (error) {
    ElMessage.error((error as Error).message)
    console.error(error)
  } finally {
    isLoading.value = false
    isShowSkeleton.value = false
  }
}

const handleSearch = () => {
  qPage.value = 1
  fetchFishingRecord()
}

const init = async () => {
  const dateList = getDateRange(new Date().getTime())
  startTime.value = dateList[0]
  endTime.value = dateList[0]
  startTimes.value = dateList
  endTimes.value = dateList

  try {
    await fishingStore.fetchFishingData(false)
  } catch(error) {
    ElMessage.error((error as Error).message)
    console.error(error)
  } finally {
    isShowSkeleton.value = false
  }
}

init()
</script>

<template>
  <template v-if="isFishMaintain === false">
    <el-skeleton v-if="isShowSkeleton" class="skeleton" :rows="5" animated />
    <div 
      v-else
      v-loading="isLoading"
    >
      <FishingSearch
        v-model:game-id="gameId"
        v-model:start-time="startTime"
        v-model:end-time="endTime"
        :game-list="searchGameList"
        :start-times="startTimes"
        :end-times="endTimes"
        @search="handleSearch"
      />
      <FishingTable
        :top-table-data="topTableData"
        :fishing-table-data="fishingTableData"
      />
      <PaginationComponent
        v-model:current-page="qPage"
        :page-count="totalPage"
        @current-change="fetchFishingRecord"
      />
    </div>
  </template>
  <template v-else>
    <div v-html="maintainText"></div>
  </template>
</template>