import { mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import FishingSearch from './FishingSearch.vue' 
import { ElMessage } from 'element-plus'

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      'S_FIELD_REQUIRE': 'Field cannot be empty',
      "S_ENTER_GAME_NAME": "Please Enter Game Name",
      "S_SEARCH": "Search"
    },
  },
})

const mockTimes = {
  "2025-02-19": "2025-02-19",
  "2025-02-18": "2025-02-18",
  "2025-02-17": "2025-02-17",
  "2025-02-16": "2025-02-16",
  "2025-02-15": "2025-02-15",
  "2025-02-14": "2025-02-14",
  "2025-02-13": "2025-02-13",
  "2025-02-12": "2025-02-12"
}
const mockGameList = [
  { gameId: 38003, name: "捕鱼达人" },
  { gameId: 38001, name: "捕鱼大师" },
  { gameId: 38002, name: "富贵渔场" }
]
const propsData = {
  startTimes: { ...mockTimes },
  endTimes: { ...mockTimes },
  gameList: mockGameList.slice(),
  startTime: '2025-02-19',
  endTime: '2025-02-19',
  gameId: 38001
}

describe('FishingSearch', () => {
  it('renders correctly with default props', async () => {
    const wrapper = mount(FishingSearch, {
      props: { ...propsData },
      global: {
        plugins: [i18n],
      },
    })

    const vm = wrapper.vm as any

    await vm.$nextTick()
    await nextTick()

    const elSelects = wrapper.findAllComponents({ name: 'el-select' })
    expect(elSelects).toHaveLength(3)
    elSelects.forEach((elSelect) => {
      expect(elSelect.exists()).toBe(true)
    })

    const gameListSelect = elSelects[0]
    const gameListOptions = gameListSelect.findAllComponents({ name: 'el-option' })
    expect(gameListSelect.text()).toBe('捕鱼大师')
    expect(gameListOptions.length).toBe(Object.values(mockGameList).length)
    Object.values(mockGameList).forEach((item, index) => {
      expect(gameListOptions[index].text()).toBe(item.name)
    })

    const startTimesSelect = elSelects[1]
    const startTimesOptions = startTimesSelect.findAllComponents({ name: 'el-option' })
    expect(startTimesSelect.text()).toBe('2025-02-19')
    expect(startTimesOptions.length).toBe(Object.values(mockTimes).length)
    Object.values(mockTimes).forEach((item, index) => {
      expect(startTimesOptions[index].text()).toBe(item)
    })

    const endTimesSelect = elSelects[2]
    const endTimesOptions = endTimesSelect.findAllComponents({ name: 'el-option' })
    expect(endTimesSelect.text()).toBe('2025-02-19')
    expect(endTimesOptions.length).toBe(Object.values(mockTimes).length)
    Object.values(mockTimes).forEach((item, index) => {
      expect(endTimesOptions[index].text()).toBe(item)
    })

    expect(wrapper.findComponent({ name: 'el-button' }).text()).toBe('Search')
  })

  it('renders the select correctly when selected', async () => {
    const wrapper = mount(FishingSearch, {
      props: { ...propsData },
      global: {
        plugins: [i18n],
      },
    })

    const vm = wrapper.vm as any

    await vm.$nextTick()
    await nextTick()

    const elSelects = wrapper.findAllComponents({ name: 'el-select' })

    const startTimesSelect = elSelects[1]
    const startTimesOptions = startTimesSelect.findAllComponents({ name: 'el-option' })
    const endTimesSelect = elSelects[2]
    const endTimesOptions = endTimesSelect.findAllComponents({ name: 'el-option' })
    
    endTimesOptions.forEach((option) => {
      const isDisabled = option.text() < startTimesSelect.text()
      expect(option.attributes('aria-disabled')).toBe(isDisabled ? 'true' : undefined)
    })

    await wrapper.setProps({ endTime: "2025-02-15" });

    startTimesOptions.forEach((option) => {
      const isDisabled = option.text() > endTimesSelect.text()
      expect(option.attributes('aria-disabled')).toBe(isDisabled ? 'true' : undefined)
    })

    endTimesOptions.forEach((option) => {
      const isDisabled = option.text() < startTimesSelect.text()
      expect(option.attributes('aria-disabled')).toBe(isDisabled ? 'true' : undefined)
    })
  })

  it('emits search event when the search button is clicked', async () => {
    const wrapper = mount(FishingSearch, {
      props: { ...propsData, gameId: undefined },
      global: {
        plugins: [i18n],
      },
    })

    const vm = wrapper.vm as any

    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())

    await vm.$nextTick()
    await nextTick()

    const elButton = wrapper.findComponent({ name: 'el-button' })
    const gameListSelect = wrapper.findAllComponents({ name: 'el-select' })[0]

    await elButton.trigger('click')

    expect(gameListSelect.attributes('class')?.includes('empty')).toBe(true)
    expect(ElMessage.error).toHaveBeenCalledWith('Field cannot be empty')

    await wrapper.setProps({ gameId: 38003 });
    await elButton.trigger('click')

    expect(gameListSelect.attributes('class')?.includes('empty')).toBe(false)
    expect(wrapper.emitted('search')).toBeTruthy()
  })
})
