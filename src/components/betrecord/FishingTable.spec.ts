import { mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import FishingTable from './FishingTable.vue'

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      'S_BETHISTORYBTN': 'Bet History',
      'S_WAGER_AMOUNT': 'Wager Amount',
      'S_AG_VALID_BET': 'Valid bets',
      'S_PAYOFF_PAYOFF': 'Win gold',
      'S_NODATA': 'There is no betting information today, or your bet today has already resulted.',
      'S_BET_NUM': 'Bet Number',
      'S_GAMETYPE2': 'Game Type',
      'S_COUNT': 'Count'
    },
  },
})

vi.mock('@/components/betrecord/BetHistoryDialog.vue', () => ({
  default: {
    name: 'BetHistoryDialog',
    props: ['modelValue', 'title', 'ifrUrl'],
    template: '<div class="mock-bet-history-dialog">Mock BetHistoryDialog</div>',
  },
}))

const propsData = {
  topTableData: [{
    sum: "总计",
    totalCount: 3,
    totalBetAmount: "136.00",
    totalPayoff: "-50.00"
  }],
  fishingTableData: [{
    gameType: "BB捕鱼大师",
    betAmount: "16.00",
    payoff: "-16.00",
    wagersDetailUrl: "https://777.vir777.net/entrance/redirect/bbfmg/detailUrl?gameType=38001&wagersId=595700528&lang=zh-cn",
    wagersDate: "2025-02-27 03:03:44",
    id: 595700528
  }]
}

describe('FishingTable', () => {
  it('renders correctly with default props', async () => {
    const wrapper = mount(FishingTable, {
      props: { ...propsData },
      global: {
        plugins: [i18n],
      },
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    const elTables = wrapper.findAllComponents({ name: "el-table" })
    const topTable = elTables[0]
    const topTableHeaderText = ['', 'Count', 'Valid bets', 'Win gold']
    const topTableHeaderCells = topTable.findAll('thead .cell')
    const topTableBodyText = ['总计', '3', '136.00', '-50.00']
    const topTableBodyCells = topTable.findAll('tbody .cell')
    const fishingTable = elTables[1]
    const fishingTableHeaderText = ['Bet History', 'Bet Number', 'Game Type', 'Wager Amount', 'Win gold']
    const fishingTableHeaderCells = fishingTable.findAll('thead .cell')
    const fishingTableBodyText = ['2025-02-27 03:03:44', '595700528', '捕鱼大师', '16.00', '-16.00']
    const fishingTableBodyCells = fishingTable.findAll('tbody .cell')

    expect(topTableHeaderCells.every((cell, index) => cell.text() === topTableHeaderText[index] )).toBe(true)
    expect(topTableBodyCells.every((cell, index) => cell.text() === topTableBodyText[index] )).toBe(true)
    expect(topTableBodyCells[3].find('span').attributes('style')).toBeTruthy()
    expect(fishingTableHeaderCells.every((cell, index) => cell.text() === fishingTableHeaderText[index] )).toBe(true)
    expect(fishingTableBodyCells.every((cell, index) => cell.html().includes(fishingTableBodyText[index]) )).toBe(true)
    expect(fishingTableBodyCells[4].find('span').attributes('style')).toBeTruthy()
    expect(wrapper.findComponent({ name: 'BetHistoryDialog' }).exists()).toBe(true)

    await wrapper.setProps({
      topTableData: [{
        sum: "总计",
        totalcount: 3,
        totalBetAmount: "136.00",
        totalPayoff: "50.00"
      }],
      fishingTableData: [{
          gameType: "BB捕鱼大师",
          betAmount: "16.00",
          payoff: "16.00",
          wagersDetailUrl: "https://777.vir777.net/entrance/redirect/bbfmg/detailUrl?gameType=38001&wagersId=595700528&lang=zh-cn",
          wagersDate: "2025-02-27 03:03:44",
          id: 595700528
      }],
    })

    expect(topTableBodyCells[3].find('span').attributes('style')).toBeFalsy()
    expect(fishingTableBodyCells[4].find('span').attributes('style')).toBeFalsy()
  })

  it('calls the openDialog on click the bet-amount span', async () => {
    const wrapper = mount(FishingTable, {
      props: { ...propsData },
      global: {
        plugins: [i18n],
      },
    })

    const vm = wrapper.vm as any
    const openDialogSpy = vi.spyOn(vm, 'openDialog')

    await wrapper.vm.$nextTick()
    await nextTick()

    const elTables = wrapper.findAllComponents({ name: "el-table" })
    const fishingTable = elTables[1]
    const betAmountEl = fishingTable.find('tbody .cell .bet-amount')

    await betAmountEl.trigger('click')
    expect(openDialogSpy).toHaveBeenCalledWith({
      gameType: "BB捕鱼大师",
      betAmount: "16.00",
      payoff: "-16.00",
      wagersDetailUrl: "https://777.vir777.net/entrance/redirect/bbfmg/detailUrl?gameType=38001&wagersId=595700528&lang=zh-cn",
      wagersDate: "2025-02-27 03:03:44",
      id: 595700528
    })
  })
})
