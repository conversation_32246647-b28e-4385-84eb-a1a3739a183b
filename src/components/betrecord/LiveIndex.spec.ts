import { flushPromises, mount } from '@vue/test-utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import { ILiveRecord } from '@/types/betrecord' 
import LiveIndex from './LiveIndex.vue'
import { getLiveRecordByDate } from '@/api/betrecord'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

dayjs.extend(timezone)
dayjs.extend(utc)

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      'S_TOTAL': 'Total',
    },
  },
})

vi.mock('@/api/betrecord', () => ({
  getLiveRecordByRoundSerial: vi.fn(),
  getLiveRecordByDate: vi.fn()
}))

vi.mock('@/api/game', () => ({
  getLiveGameList: vi.fn(() => ({
    3001: '百家乐'
  }))
}))

vi.mock('vue3-cookies', () => ({
  useCookies: vi.fn().mockReturnValue({
    cookies: {
      get: (key: string) => {
        if (key === 'session_id') return 'session_id'
        if (key === 'lang') return 'en'
        return null
      }
    }
  }),
}))

vi.mock('@/components/betrecord/LiveSearch.vue', () => ({
  default: {
    name: 'LiveSearch',
    props: ['searchType', 'snumVal', 'startTime', 'endTime', 'startTimes', 'endTimes'],
    template: '<div class="mock-live-search">Mock LiveSearch</div>',
  },
}))
vi.mock('@/components/betrecord/LiveTable.vue', () => ({
  default: {
    name: 'LiveTable',
    props: ['topTableData', 'liveTableData', 'isLoading'],
    template: '<div class="mock-live-table">Mock LiveTable</div>',
  },
}))
vi.mock('@/components/mcenter/PaginationComponent.vue', () => ({
  default: {
    name: 'PaginationComponent',
    props: ['currentPage', 'totalPage'],
    template: '<div class="mock-pagination-component">Mock PaginationComponent</div>',
  },
}))

const mockLiveDateRecord = {
  isMaintain: false,
  searchRange: {
    startDate: '2024-11-27',
    endDate: '2024-12-04'
  },
  wagersList: [
    {
      id: 520000094787,
      wagersDate: '2024-12-02 04:58:13',
      gameType: 3001,
      roundSerial: 325197364,
      roundNo: '5-10',
      tableCode: 'EU1',
      betAmount: '20.00',
      commissionable: '20.00',
      payoff: '-20.00',
      wagersDetailUrl: 'https://bbgp-game1.livevir999.net/ipl/portal.php/game/httpredirect?gametype=3027&hallid=3820474&id=456121189&key=36ffa4a4d9fba367cb83bced14bec5be&lang=zh-tw&p_service=gp&type=livedetail&wid=520000815705'
    }
  ],
  total: {
    totalCount: 21,
    totalBetAmount: '420.00',
    totalCommissionable: '420.00',
    totalPayoff: '83.20'
  },
  pagination: {
    currentPage: 1,
    pageLimit: 5,
    total: 21,
    totalPage: 5
  },
  maintain: false,
  maintainInfo: ''
} as ILiveRecord

describe('LiveIndex', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders correctly when not in maintenance', async () => {
    vi.mocked(getLiveRecordByDate).mockResolvedValue(mockLiveDateRecord)

    const wrapper = mount(LiveIndex, {
      global: {
        plugins: [i18n],
      },
    })

    expect(wrapper.findComponent({ name: 'el-skeleton' }).exists()).toBe(true)

    await wrapper.vm.$nextTick()
    await flushPromises()
    await nextTick()

    expect(getLiveRecordByDate).toHaveResolved()
    expect(wrapper.findComponent({ name: 'el-skeleton' }).exists()).toBe(false)
    expect(wrapper.findComponent({ name: 'LiveSearch' }).exists()).toBe(true)
    expect(wrapper.findComponent({ name: 'LiveTable' }).exists()).toBe(true)
    expect(wrapper.findComponent({ name: 'PaginationComponent' }).exists()).toBe(true)
  })

  it('renders maintenance message when in maintenance', async () => {
    const mockLiveDateRecordData = {
      maintain: true,
      maintainInfo: 'Maintenance in progress',
    } as ILiveRecord
    
    vi.mocked(getLiveRecordByDate).mockResolvedValue(mockLiveDateRecordData)

    const wrapper = mount(LiveIndex, {
      global: {
        plugins: [i18n],
      }
    })

    await wrapper.vm.$nextTick()
    await nextTick()
    await flushPromises()

    expect(wrapper.html()).toContain('Maintenance in progress')
  })

  it('fetches live record on search', async () => {
    vi.mocked(getLiveRecordByDate).mockResolvedValue(mockLiveDateRecord)

    const wrapper = mount(LiveIndex, {
      global: {
        plugins: [i18n],
      },
    })

    const openSpy = vi.spyOn(wrapper.vm, 'handleSearch' as any)

    await wrapper.vm.$nextTick()
    await nextTick()
    await flushPromises()

    const liveSearch = wrapper.findComponent({ name: 'LiveSearch' })
    await liveSearch.vm.$emit('search')

    expect(openSpy).toHaveBeenCalled()
  })

  it('fetchs live record when the current page changes', async () => {
    vi.mocked(getLiveRecordByDate).mockResolvedValue(mockLiveDateRecord)

    const wrapper = mount(LiveIndex, {
      global: {
        plugins: [i18n],
      },
    })

    const openSpy = vi.spyOn(wrapper.vm, 'fetchLiveRecord' as any)

    await wrapper.vm.$nextTick()
    await nextTick()
    await flushPromises()
      
    const paginationComponent = wrapper.findComponent({ name: 'PaginationComponent' })
    await paginationComponent.vm.$emit('currentChange')

    expect(openSpy).toHaveBeenCalled()
  })

  it('shows error message when fetch API fails', async () => {
    const mockError = new Error('Failed to fetch data')
    vi.mocked(getLiveRecordByDate).mockRejectedValue(mockError)
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())

    mount(LiveIndex, {
      global: {
        plugins: [i18n],
      },
    })

    await flushPromises()

    expect(console.error).toHaveBeenCalledWith(mockError)
    expect(ElMessage.error).toHaveBeenCalledWith('Failed to fetch data')
  })
})