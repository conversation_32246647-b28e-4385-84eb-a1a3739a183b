<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { getLiveRecordByDate, getLiveRecordByRoundSerial } from '@/api/betrecord'
import { getLiveGameList } from '@/api/game'
import PaginationComponent from '@/components/mcenter/PaginationComponent.vue'
import LiveSearch from './LiveSearch.vue'
import LiveTable from './LiveTable.vue'
import { useCookies } from 'vue3-cookies'
import dayjs from 'dayjs'

const { t } = useI18n()
const { cookies } = useCookies()
const maintainText = ref('')
const searchType = ref('Date')
const topTableData = ref([
  {
    sum: t('S_TOTAL'),
    totalbet: '--',
    totalcomm: '--',
    totalcount: '--',
    totalpayoff: '--'
  }
])
const liveTableData = ref<any[]>([])
const snumVal = ref()
const startTime = ref('')
const startTimes = ref({})
const endTime = ref('')
const endTimes = ref({})
const totalPage = ref(1)
const qPage = ref(1)
const isLoading = ref(false)
const isShowSkeleton = ref(true)
const isLiveMaintain = ref(false)
const gameList = ref({} as {[key: string]: string })

const getDateRange = (dateTime: number) => {
  let result = [dayjs.tz(dateTime, 'Etc/GMT+4').format('YYYY-MM-DD')]
  for (let x = 1; x <= 7; x++) {
    result.push(dayjs.tz(dateTime, 'Etc/GMT+4').subtract(x, 'day').format('YYYY-MM-DD'))
  }
  return result
}

const fetchLiveRecord = async () => {
  isLoading.value = true

  try {
    const fetchRecord = searchType.value === 'Date' ? fetchLiveDateRecord : fetchLiveRoundSerialRecord
    const res = await fetchRecord()

    if (res.maintain) {
      isLiveMaintain.value = true
      maintainText.value = res.maintainInfo
      return;
    }

    topTableData.value = [{
      sum: t('S_TOTAL'),
      totalcount: String(res.total.totalCount),
      totalbet: res.total.totalBetAmount,
      totalcomm: res.total.totalCommissionable,
      totalpayoff: res.total.totalPayoff
    }]

    liveTableData.value = res.wagersList.map(item => {
      return {
        ...item,
        gameType: gameList.value[item.gameType],
      }
    })
    totalPage.value = res.pagination.totalPage
  } catch (error) {
    ElMessage.error((error as Error).message)
    console.error(error)
  } finally {
    isLoading.value = false
    isShowSkeleton.value = false
  }
};

const fetchLiveDateRecord = async () => {
  const params = {
    hall_id: Number(localStorage.getItem('hallinfo_hallid')),
    session_id: cookies.get('SESSION_ID'),
    start_date: startTime.value,
    end_date: endTime.value,
    lang: cookies.get('lang'),
    page: qPage.value,
    scheme: location.protocol.includes('https') ? 'https' : 'http'
  };

  return getLiveRecordByDate(params)
};

const fetchLiveRoundSerialRecord = async () => {
  const params = {
    hall_id: Number(localStorage.getItem('hallinfo_hallid')),
    session_id: cookies.get('SESSION_ID'),
    roundserial: snumVal.value,
    lang: cookies.get('lang'),
    page: qPage.value,
    scheme: location.protocol.includes('https') ? 'https' : 'http'
  };

  return getLiveRecordByRoundSerial(params)
};

const fetchGameList = async () => {
  try {
    const res = await getLiveGameList({ hall_id: Number(localStorage.getItem('hallinfo_hallid')), lang: cookies.get('lang') })
    gameList.value = res
  } catch (error) {
    ElMessage.error((error as Error).message)
    console.log(error)
  }
}

const handleSearch = () => {
  qPage.value = 1
  fetchLiveRecord()
}

const init = async () => {
  const dateList = getDateRange(new Date().getTime())
  startTime.value = dateList[0]
  endTime.value = dateList[0]
  startTimes.value = dateList
  endTimes.value = dateList

  try {
    await fetchGameList()
    fetchLiveRecord()
  } catch(error) {
    ElMessage.error((error as Error).message)
    console.log(error)
  }
}

init()
</script>

<template>
  <template v-if="isLiveMaintain === false">
    <el-skeleton v-if="isShowSkeleton" class="skeleton" :rows="5" animated />
    <div 
      v-else
      v-loading="isLoading"
    >
      <LiveSearch
        v-model:search-type="searchType"
        v-model:snum-val="snumVal"
        v-model:start-time="startTime"
        v-model:end-time="endTime"
        :start-times="startTimes"
        :end-times="endTimes"
        @search="handleSearch"
      />
      <LiveTable
        :top-table-data="topTableData"
        :live-table-data="liveTableData"
      />
      <PaginationComponent
        v-model:current-page="qPage"
        :page-count="totalPage"
        @current-change="fetchLiveRecord"
      />
    </div>
  </template>
  <template v-else>
    <div v-html="maintainText"></div>
  </template>
</template>

<style lang="scss" scoped>
.table-container {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;

  .el-table {
    margin: 15px;
  }

  .bet-amount {
    color: #54b9ff;
    text-decoration: underline;
    cursor: pointer;
  }
}

:deep(.el-table){
  .table-header {
    color: var(--mcenter-collapse-item-text);
    font-size: 15px;
    text-align: center !important;
    background: #eee !important;
  }

  .table-header2 {
    color: var(--mcenter-table-header2--text);
    font-size: 15px;
    text-align: center !important;
    background: var(--mcenter-betinfo-table-header-bg) !important;
  }

  .table-cell {
    color: var(--mcenter-table-cell2--text);
    text-align: center !important;
  }

  .table-cell2 {
    color: var(--mcenter-table-cell2--text);
    text-align: center !important;
  }

  .el-table__empty-text {
    color: var(--mcenter-nodata);
  }
}
</style>