import { mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import LiveSearch from './LiveSearch.vue'
import { ElMessage } from 'element-plus'

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      'S_DATE_SEARCH': 'Search by Date',
      'S_PID_SEARCH': 'Search by PID',
      'S_SEARCH_ROUND_MSG': 'Please enter the round number',
      "S_SEARCH": "Search"
    },
  },
})

const mockTimes = {
  "2025-02-19": "2025-02-19",
  "2025-02-18": "2025-02-18",
  "2025-02-17": "2025-02-17",
  "2025-02-16": "2025-02-16",
  "2025-02-15": "2025-02-15",
  "2025-02-14": "2025-02-14",
  "2025-02-13": "2025-02-13",
  "2025-02-12": "2025-02-12"
}

const propsData = {
  startTimes: { ...mockTimes },
  endTimes: { ...mockTimes },
  startTime: '2025-02-19',
  endTime: '2025-02-19',
  searchType: "Date",
  snumVal: ''
}

describe('LiveSearch', () => {
  it('renders correctly with default props', async () => {
    const wrapper = mount(LiveSearch, {
      props: { ...propsData },
      global: {
        plugins: [i18n],
      },
    })

    const vm = wrapper.vm as any

    await vm.$nextTick()
    await nextTick()

    const elRadios = wrapper.findAllComponents({ name: 'el-radio' })
    expect(elRadios).toHaveLength(2)
    elRadios.forEach((elRadio) => {
      expect(elRadio.exists()).toBe(true)
    })
    expect(elRadios[0].attributes('class')).toContain('is-checked')

    const elSelects = wrapper.findAllComponents({ name: 'el-select' })
    expect(elSelects).toHaveLength(2)
    elSelects.forEach((elSelect) => {
      expect(elSelect.exists()).toBe(true)
    })

    const startTimesSelect = elSelects[0]
    const startTimesOptions = startTimesSelect.findAllComponents({ name: 'el-option' })
    expect(startTimesSelect.text()).toBe('2025-02-19')
    expect(startTimesOptions.length).toBe(Object.values(mockTimes).length)
    Object.values(mockTimes).forEach((item, index) => {
      expect(startTimesOptions[index].text()).toBe(item)
    })

    const endTimesSelect = elSelects[1]
    const endTimesOptions = endTimesSelect.findAllComponents({ name: 'el-option' })
    expect(endTimesSelect.text()).toBe('2025-02-19')
    expect(endTimesOptions.length).toBe(Object.values(mockTimes).length)
    Object.values(mockTimes).forEach((item, index) => {
      expect(endTimesOptions[index].text()).toBe(item)
    })

    expect(wrapper.findComponent({ name: 'el-button' }).text()).toBe('Search')
    expect(wrapper.findComponent({ name: 'el-input' }).exists()).toBe(false)

    await wrapper.setProps({ searchType: 'No' })

    expect(elRadios[1].attributes('class')).toContain('is-checked')
    expect(wrapper.findComponent({ name: 'el-input' }).exists()).toBe(true)
  })

  it('renders the select correctly when selected', async () => {
    const wrapper = mount(LiveSearch, {
      props: { ...propsData },
      global: {
        plugins: [i18n],
      },
    })

    const vm = wrapper.vm as any

    await vm.$nextTick()
    await nextTick()

    const elSelects = wrapper.findAllComponents({ name: 'el-select' })

    const startTimesSelect = elSelects[0]
    const startTimesOptions = startTimesSelect.findAllComponents({ name: 'el-option' })
    const endTimesSelect = elSelects[1]
    const endTimesOptions = endTimesSelect.findAllComponents({ name: 'el-option' })
    
    endTimesOptions.forEach((option) => {
      const isDisabled = option.text() < startTimesSelect.text()
      expect(option.attributes('aria-disabled')).toBe(isDisabled ? 'true' : undefined)
    })

    await wrapper.setProps({ endTime: "2025-02-15" });

    startTimesOptions.forEach((option) => {
      const isDisabled = option.text() > endTimesSelect.text()
      expect(option.attributes('aria-disabled')).toBe(isDisabled ? 'true' : undefined)
    })

    endTimesOptions.forEach((option) => {
      const isDisabled = option.text() < startTimesSelect.text()
      expect(option.attributes('aria-disabled')).toBe(isDisabled ? 'true' : undefined)
    })
  })

  it('emits search event when the search button is clicked', async () => {
    const wrapper = mount(LiveSearch, {
      props: { ...propsData, gameId: undefined },
      global: {
        plugins: [i18n],
      },
    })

    const vm = wrapper.vm as any

    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())

    await vm.$nextTick()
    await nextTick()

    const elButton = wrapper.findComponent({ name: 'el-button' })
    await elButton.trigger('click')

    expect(wrapper.emitted('search')).toBeTruthy()
  })

  it('calls the limitInputNumber function on input keyup and keydown', async () => {
    const wrapper = mount(LiveSearch, {
      props: { ...propsData, searchType: 'No' },
      global: { 
        plugins: [i18n],
      },
    })

    const vm = wrapper.vm as any
    const openSpy = vi.spyOn(vm, 'limitInputNumber')

    await vm.$nextTick()
    await nextTick()

    const elInput = wrapper.findComponent({ name: 'el-input' })
    const input = elInput.find('input')
   
    await elInput.setValue('1234Text')
    await elInput.find('input').trigger('keyup')

    expect(openSpy).toHaveBeenCalled()
    expect(input.element.value).toBe('1234')

    await elInput.setValue('5678Text')
    await elInput.find('input').trigger('keydown')

    expect(openSpy).toHaveBeenCalled()
    expect(input.element.value).toBe('5678')
  })
})
