<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const isSnumEmpty = ref(false)

const searchType = defineModel('searchType', {
  type: String,
  default: 'Date'
})

const snumVal = defineModel('snumVal', {
  type: String,
  default: ''
})

const startTime = defineModel('startTime', {
  type: String,
  default: ''
})

const endTime = defineModel('endTime', {
  type: String,
  default: ''
})

const props = defineProps({
  startTimes: {
    type: Object,
    default: () => ({}),
    required: true
  },
  endTimes: {
    type: Object,
    default: () => ({}),
    required: true
  }
})

const emit = defineEmits(['search'])

const selStartTime = ref('')
const selEndTime = ref('')

const limitInputNumber = () => {
  snumVal.value = snumVal.value.replace(/[^0-9]/g, '');
}

const handleClick = () => {
  if (searchType.value !== 'Date' && snumVal.value === '') {
    isSnumEmpty.value = true
    ElMessage.error(t('S_FIELD_REQUIRE'))
    return
  }

  isSnumEmpty.value = false

  emit('search', {
    searchType: searchType.value,
    startTime: selStartTime.value,
    endTime: selEndTime.value,
    snumVal: snumVal.value
  })
}
</script>

<template>
  <div class="search">
    <div class="radio-group">
      <el-radio-group v-model="searchType">
        <el-radio value="Date">{{ t('S_DATE_SEARCH') }}</el-radio>
        <el-radio value="No">{{ t('S_PID_SEARCH') }}</el-radio>
      </el-radio-group>
    </div>
    <div v-if="searchType === 'Date'" class="date-picker">
      <el-select v-model="startTime" placeholder="Select" style="width: 200px">
        <el-option
          v-for="item in props.startTimes"
          :key="item"
          :label="item"
          :value="item"
          :disabled="String(item) > endTime"
        />
      </el-select>
      <span>{{ '- ' }}</span>
      <el-select v-model="endTime" placeholder="Select" style="width: 200px">
        <el-option
          v-for="item in props.endTimes"
          :key="item"
          :label="item"
          :value="item"
          :disabled="String(item) < startTime"
        />
      </el-select>
    </div>
    <el-input
      v-else
      v-model="snumVal"
      :placeholder="t('S_SEARCH_ROUND_MSG')"
      class="no-input"
      :class="{ 'empty': isSnumEmpty }"
      @keyup="limitInputNumber()"
      @keydown="limitInputNumber()"
    />
    <el-button 
      color="var(--mcenter-effectivebetting-button-bg)"
      round class="search-btn"
      @click="handleClick()"
    >
      {{ t('S_SEARCH') }}
    </el-button>
  </div>
</template>

<style lang="scss" scoped>
.search {
  margin: 0 5px;
  .el-select {
    margin-right: 10px;
  }

  .no-input {
    width: 200px;

    &.empty {
      border: 1px solid #ff5656;
      border-radius: 5px;
    }
  }

  .date-picker {
    display: inline-block;
  }

  .search-btn {
    width: 180px;
    float: right;
  }

  :deep(.el-button) {
    span {
      color: white;
    }

    &:hover {
      background: var(--mcenter-effectivebetting-button-bg);
      border-color: var(--mcenter-effectivebetting-button-bg);
    }
    &:active {
      background: var(--mcenter-effectivebetting-button-bg);
    }
  }
}
</style>