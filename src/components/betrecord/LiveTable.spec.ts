import { mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import LiveTable from './LiveTable.vue'

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      'S_BETHISTORYBTN': 'Bet History',
      'S_COUNT': 'Count',
      'S_WAGER_AMOUNT': 'Wager Amount',
      'S_AG_VALID_BET': 'Valid bets',
      'S_PAYOFF_PAYOFF': 'Win gold',
      'S_NODATA': 'There is no betting information today, or your bet today has already resulted.',
      'S_WAGER_DATE': 'Wager Date',
      'S_BET_NUM': 'Bet Number',
      'S_PID': 'Round No.',
      'S_GAME_NUM': 'Game No.',
      'S_GAMETYPE2': 'Game Type',
      'S_TID': 'Table No.'
    },
  },
})

vi.mock('@/components/betrecord/BetHistoryDialog.vue', () => ({
  default: {
    name: 'BetHistoryDialog',
    props: ['modelValue', 'title', 'ifrUrl'],
    template: '<div class="mock-bet-history-dialog">Mock BetHistoryDialog</div>',
  },
}))

const propsData = {
  topTableData: [{
    sum: "总计",
    totalbet: "50.00",
    totalcomm: "50.00",
    totalcount: "1",
    totalpayoff: "-50.00"
  }],
  liveTableData: [{
    wagersDate: "2025-03-04 22:20:23",
    id: 520000437398,
    roundSerial: 332377049,
    roundNo: "47-56",
    gameType: "百家乐",
    tableCode: "AS2",
    commissionable: "50.00",
    betAmount: "50.00",
    payoff: "-50.00",
    wagersDetailUrl: 'https://bbgp-game1.livevir999.net/ipl/portal.php/game/httpredirect?gametype=3027&hallid=3820474&id=456121189&key=********************************&lang=zh-tw&p_service=gp&type=livedetail&wid=520000815705'
  }]
}

describe('LiveTable', () => {
  it('renders correctly with default props', async () => {
    const wrapper = mount(LiveTable, {
      props: { ...propsData },
      global: {
        plugins: [i18n],
      },
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    const elTables = wrapper.findAllComponents({ name: "el-table" })
    const topTable = elTables[0]
    const topTableHeaderText = ['', 'Count', 'Wager Amount', 'Valid bets', 'Win gold']
    const topTableHeaderCells = topTable.findAll('thead .cell')
    const topTableBodyText = ['总计', '1', '50.00', '50.00', '-50.00']
    const topTableBodyCells = topTable.findAll('tbody .cell')
    const liveTable = elTables[1]
    const liveTableHeaderText = ['Wager Date', 'Bet Number', 'Round No.', 'Game No.', 'Game Type', 'Table No.', 'Wager Amount', 'Valid bets', 'Win gold']
    const liveTableHeaderCells = liveTable.findAll('thead .cell')
    const liveTableBodyText = ['2025-03-04 22:20:23', '520000437398', '332377049', '47-56', '百家乐', 'AS2', '50.00', '50.00', '-50.00']
    const liveTableBodyCells = liveTable.findAll('tbody .cell')

    expect(topTableHeaderCells.every((cell, index) => cell.text() === topTableHeaderText[index] )).toBe(true)
    expect(topTableBodyCells.every((cell, index) => cell.text() === topTableBodyText[index] )).toBe(true)
    expect(topTableBodyCells[4].find('span').attributes('style')).toBeTruthy()
    expect(liveTableHeaderCells.every((cell, index) => cell.text() === liveTableHeaderText[index] )).toBe(true)
    expect(liveTableBodyCells.every((cell, index) => cell.html().includes(liveTableBodyText[index]) )).toBe(true)
    expect(liveTableBodyCells[8].find('span').attributes('style')).toBeTruthy()
    expect(wrapper.findComponent({ name: 'BetHistoryDialog' }).exists()).toBe(true)

    await wrapper.setProps({
      topTableData: [{
        sum: "总计",
        totalbet: "50.00",
        totalcomm: "50.00",
        totalcount: "1",
        totalpayoff: "50.00"
      }],
      liveTableData: [{
        wagersDate: "2025-03-04 22:20:23",
        id: 520000437398,
        roundSerial: 332377049,
        roundNo: "47-56",
        gameType: "百家乐",
        tableCode: "AS2",
        commissionable: "50.00",
        betAmount: "50.00",
        payoff: "50.00",
        wagersDetailUrl: 'https://bbgp-game1.livevir999.net/ipl/portal.php/game/httpredirect?gametype=3027&hallid=3820474&id=456121189&key=********************************&lang=zh-tw&p_service=gp&type=livedetail&wid=520000815705'
      }],
    })

    expect(topTableBodyCells[4].find('span').attributes('style')).toBeFalsy()
    expect(liveTableBodyCells[8].find('span').attributes('style')).toBeFalsy()
  })

  it('calls the openDialog on click the bet-amount span', async () => {
    const wrapper = mount(LiveTable, {
      props: { ...propsData },
      global: {
        plugins: [i18n],
      },
    })

    const vm = wrapper.vm as any
    const openDialogSpy = vi.spyOn(vm, 'openDialog')

    await wrapper.vm.$nextTick()
    await nextTick()

    const elTables = wrapper.findAllComponents({ name: "el-table" })
    const liveTable = elTables[1]
    const betAmountEl = liveTable.find('tbody .cell .bet-amount')

    await betAmountEl.trigger('click')
    expect(openDialogSpy).toHaveBeenCalledWith({
      wagersDate: "2025-03-04 22:20:23",
      id: 520000437398,
      roundSerial: 332377049,
      roundNo: "47-56",
      gameType: "百家乐",
      tableCode: "AS2",
      commissionable: "50.00",
      betAmount: "50.00",
      payoff: "-50.00",
      wagersDetailUrl: 'https://bbgp-game1.livevir999.net/ipl/portal.php/game/httpredirect?gametype=3027&hallid=3820474&id=456121189&key=********************************&lang=zh-tw&p_service=gp&type=livedetail&wid=520000815705'
    })
  })
})
