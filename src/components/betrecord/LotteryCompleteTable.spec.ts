import { flushPromises, mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import LotteryCompleteTable from './LotteryCompleteTable.vue'
import { getLotteryComplete, getLotteryCompleteByGame } from '@/api/betrecord'
import request from '@/utils/request'

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      'S_NO_DATA': 'No Data',
      'S_TOTAL': 'Total',
      'S_WAGER_AMOUNT': 'Wager Amount',
      'S_AG_VALID_BET': 'Valid bets',
      'S_PAYOFF_PAYOFF': 'Win gold',
      'S_DATE': 'Date',
      'S_EF_STR': 'Valid bet amount',
      'S_NODATA': 'There is no betting information today, or your bet today has already resulted.',
      'S_GAMETYPE2': 'Game Type'
    },
  },
})

vi.mock('@/components/betrecord/LotteryDetailDialog.vue', () => ({
  default: {
    name: 'LotteryDetailDialog',
    props: ['modelValue', 'type', 'gameItem'],
    template: '<div class="mock-lottery-detail-dialog">Mock LotteryDetailDialog</div>',
  },
}))

vi.mock('@/api/betrecord', () => ({
  getLotteryComplete: vi.fn(),
  getLotteryCompleteByGame: vi.fn()
}))

vi.mock('@/utils/request', () => {
  return {
    default: {
      get: vi.fn(),
    }
  }
})

vi.mock('@/utils/apiRequest', () => {
  return {
    default: {
      get: vi.fn(),
    }
  }
})

const mockLotteryCompleteData = {
  total: {
    sum: "总计",
    totalBetAmount: "10.00",
    totalCommissionable: "10.00",
    totalPayoff: "-3.85"
  },
  list: [{
    roundDate: "2025-03-05",
    betAmount: "10.00",
    commissionable: "10.00",
    payoff: "-3.85"
  }],
  maintain: false,
  maintainInfo: ""
}

const mockLotteryDetailData = {
  game: {
    MADR: {
      name: "BB 马力欧梯子",
      count: 2,
      gold: "10.00",
      wingold: "-3.85",
      valid: "10.00"
    }
  },
  message: "",
  status: "Y",
  maintain: "N"
}

const mockLotteryCompleteByGame = {
  list: [{
    gameType: 'BJ3D',
    gameName: '3D彩',
    betAmount: "10.00",
    commissionable: "10.00",
    payoff: "-3.85"
  }],
  maintain: false,
  maintainInfo: ""
}

vi.mocked(getLotteryComplete).mockResolvedValue(mockLotteryCompleteData)
vi.mocked(getLotteryCompleteByGame).mockResolvedValue(mockLotteryCompleteByGame)
vi.mocked(request.get).mockResolvedValue({ data: mockLotteryDetailData })

describe('LotteryCompleteTable', () => {
  it('renders correctly with default data', async () => {    
    const wrapper = mount(LotteryCompleteTable, {
      global: {
        plugins: [i18n],
      },
    })
    const vm = wrapper.vm as any
    const handleExpandSpy = vi.spyOn(vm, 'handleExpand')

    await vm.$nextTick()
    await nextTick()
    await flushPromises()

    const elTables = wrapper.findAllComponents({ name: "el-table" })
    const topTable = elTables[0]
    const topTableHeaderText = ['', 'Wager Amount', 'Valid bets', 'Win gold']
    const topTableHeaderCells = topTable.findAll('thead .cell')
    const topTableBodyText = ['总计', '10.00', '10.00', '-3.85']
    const topTableBodyCells = topTable.findAll('tbody .cell')
    const lotteryTable = elTables[1]
    const lotteryTableHeaderText = ['Date', 'Wager Amount', 'Valid bet amount', 'Win gold', '']
    const lotteryTableHeaderCells = lotteryTable.findAll('thead .cell')
    const lotteryTableBodyText = ['2025-03-05', '10.00', '10.00', '-3.85', '']
    const lotteryTableBodyCells = lotteryTable.findAll('tbody .cell')

    expect(topTableHeaderCells.every((cell, index) => cell.text() === topTableHeaderText[index] )).toBe(true)
    expect(topTableBodyCells.every((cell, index) => cell.text() === topTableBodyText[index] )).toBe(true)
    expect(topTableBodyCells[3].find('span').attributes('style')).toBeTruthy()
    expect(lotteryTableHeaderCells.every((cell, index) => cell.text() === lotteryTableHeaderText[index] )).toBe(true)
    expect(lotteryTableBodyCells.every((cell, index) => cell.html().includes(lotteryTableBodyText[index]) )).toBe(true)
    expect(lotteryTableBodyCells[3].find('span').attributes('style')).toBeTruthy()
    expect(wrapper.findComponent({ name: 'LotteryDetailDialog' }).exists()).toBe(true)

    await lotteryTableBodyCells[4].find('.el-table__expand-icon').trigger('click')
    await flushPromises()

    expect(handleExpandSpy).toHaveBeenCalled()

    const lotteryExpandTable = lotteryTable.find('.el-table__expanded-cell').findComponent({ name: 'el-table' })
    const lotteryExpandTableHeaderText = ['Game Type', 'Wager Amount', 'Valid bet amount', 'Win gold']
    const lotteryExpandTableHeaderCells = lotteryExpandTable.findAll('thead .cell')
    const lotteryExpandTableBodyText = ['3D彩', '10.00', '10.00', '-3.85']
    const lotteryExpandTableBodyCells = lotteryExpandTable.find('tbody').findAll('.cell')

    expect(lotteryExpandTableHeaderCells.every((cell, index) => cell.text() === lotteryExpandTableHeaderText[index] )).toBe(true)
    expect(lotteryExpandTableBodyCells.every((cell, index) => cell.html().includes(lotteryExpandTableBodyText[index]) )).toBe(true)
    expect(lotteryExpandTableBodyCells[3].find('span').attributes('style')).toBeTruthy()

    vm.tableData[0].dateDetail = [{
      name: "BB 马力欧梯子",
      count: 2,
      gold: "10.00",
      wingold: "3.85",
      valid: "10.00"
    }]

    await vm.$nextTick()

    expect(lotteryExpandTableBodyCells[3].find('span').attributes('style')).toBeFalsy()

    vm.topTableData = [{
      sum: "总计",
      totalBetAmount: "10.00",
      totalCommissionable: "10.00",
      totalPayoff: "3.85"
    }]
    vm.tableData = [{
      roundDate: "2025-03-05",
      betAmount: "10.00",
      commissionable: "10.00",
      payoff: "3.85"
    }]

    await vm.$nextTick()

    expect(topTableBodyCells[3].find('span').attributes('style')).toBeFalsy()
    expect(lotteryTableBodyCells[3].find('span').attributes('style')).toBeFalsy()
  })

  it('calls the openDialog on click the game-name span', async () => {
    const wrapper = mount(LotteryCompleteTable, {
      global: {
        plugins: [i18n],
      },
    })

    const vm = wrapper.vm as any
    const openDialogSpy = vi.spyOn(vm, 'openDialog')

    await wrapper.vm.$nextTick()
    await nextTick()
    await flushPromises()

    const lotteryTable = wrapper.findAllComponents({ name: 'el-table' })[1]

    await lotteryTable.find('.el-table__expand-icon').trigger('click')
    await flushPromises()

    const lotteryExpandTable = lotteryTable.find('.el-table__expanded-cell').findComponent({ name: 'el-table' })
    const gameNameEl = lotteryExpandTable.find('tbody .cell .game-name')

    await gameNameEl.trigger('click')

    expect(openDialogSpy).toHaveBeenCalledWith({
      gameType: 'BJ3D',
      gameName: '3D彩',
      betAmount: "10.00",
      commissionable: "10.00",
      payoff: "-3.85"
    }, '2025-03-05')
  })
})