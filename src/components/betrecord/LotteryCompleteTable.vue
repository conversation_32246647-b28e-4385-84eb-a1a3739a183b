<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { getLotteryComplete, getLotteryCompleteByGame } from '@/api/betrecord'
import LotteryDetailDialog from '@/components/betrecord/LotteryDetailDialog.vue'
import { useCookies } from 'vue3-cookies'

const { t } = useI18n()
const { cookies } = useCookies()
const tableData: Ref<{
  roundDate: string
  betAmount: string
  commissionable: string
  payoff: string
  dateDetail?: {
    gameType: string
    gameName: string
    betAmount: string
    commissionable: string
    payoff: string
  }[]
}[]> = ref([{
  roundDate: '',
  betAmount: '0',
  commissionable: '0',
  payoff: '0',
  dateDetail: []
}])
const topTableData = ref([
  {
    sum: t('S_TOTAL'),
    totalBetAmount: '--',
    totalCommissionable: '--',
    totalPayoff: '--',
  }
])
const isLoading = ref(false)
const isExpandLoading = ref<{ [key: string]: boolean }>({})
const visible = ref(false)
const gameItem = ref({})
const tableRef: globalThis.Ref = ref(null)
const emit = defineEmits(['maintain'])

const fetchDetail = async (row: { roundDate: string }) => {
  isExpandLoading.value[row.roundDate] = true

  try {
    const params = {
      session_id: cookies.get('SESSION_ID'),
      lang: cookies.get('lang'),
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
      rounddate: row.roundDate
    }
    
    const res = await getLotteryCompleteByGame(params)
    const dateDetail = res.list

    if (res.maintain) {
      tableRef.value.toggleRowExpansion(row, false)
      ElMessage.error({
        dangerouslyUseHTMLString: true,
        message: res.maintainInfo
      })
      return
    }

    if(dateDetail) {
      const dateItem = tableData.value.find((item) => item.roundDate == row.roundDate)
      if (dateItem) dateItem.dateDetail = dateDetail
    }
  } catch (error) {
    console.log(error)
    ElMessage.error((error as Error).message)
  } finally {
    isExpandLoading.value[row.roundDate] = false
  }  
}

const fetchLotteryComplete = async () => {
  isLoading.value = true
  try {
    const params = {
      session_id: cookies.get('SESSION_ID'),
      hall_id: Number(localStorage.getItem('hallinfo_hallid'))
    }

    const res = await getLotteryComplete(params)

    if (res.maintain) {
      emit('maintain', true, res.maintainInfo)
    } else {
      topTableData.value = [{
        sum: t('S_TOTAL'),
        ...res.total
      }]
      tableData.value = res.list
    }
  } catch (error) {
    console.log(error)
    ElMessage.error((error as Error).message)
  } finally {
    isLoading.value = false
  }
}

const openDialog = (item: any, date: string) => {
  gameItem.value = {
    date,
    ...item
  }
  visible.value = true
}

const handleExpand = (row: any, expendRows: any) => {
  const isExpend = expendRows.some((item: any) => item.date === row.date)
  if (isExpend) fetchDetail(row)
}

fetchLotteryComplete()
</script>

<template>
  <div class="table-container" v-loading="isLoading">
    <el-table
      :data="topTableData"
      :border="true"
      header-cell-class-name="table-header"
      cell-class-name="table-cell"
      :empty-text="t('S_NO_DATA')"
    >
      <el-table-column prop="sum" label="" />
      <el-table-column prop="totalBetAmount" :label="t('S_WAGER_AMOUNT')" />
      <el-table-column prop="totalCommissionable" :label="t('S_AG_VALID_BET')" />
      <el-table-column prop="totalPayoff" :label="t('S_PAYOFF_PAYOFF')">
        <template #default="scope">
          <span v-if="scope.row.totalPayoff < 0" style="color: #f00">
            {{ scope.row.totalPayoff }}
          </span>
          <span v-else>
            {{ scope.row.totalPayoff }}
          </span>
        </template>
      </el-table-column>
    </el-table>
    <el-table
      ref="tableRef"
      :data="tableData"
      header-cell-class-name="table-header2"
      cell-class-name="table-cell2"
      :row-key="(row) => row.roundDate"
      @expand-change="handleExpand"
      :empty-text="t('S_NO_DATA')"
    >
      <el-table-column prop="roundDate" :label="t('S_DATE')" />
      <el-table-column prop="betAmount" :label="t('S_WAGER_AMOUNT')" />
      <el-table-column prop="commissionable" :label="t('S_EF_STR')" />
      <el-table-column prop="payoff" :label="t('S_PAYOFF_PAYOFF')">
        <template #default="scope">
          <span v-if="scope.row.payoff < 0" style="color: #f00">
            {{ scope.row.payoff }}
          </span>
          <span v-else>
            {{ scope.row.payoff }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="" label="" type="expand">
        <template #default="scope">
          <el-table
            class="inner_table"
            :data="scope.row.dateDetail"
            :border="true"
            :empty-text="t('S_NODATA')"
            v-loading = "isExpandLoading[scope.row.roundDate]"
          >
            <el-table-column prop="gameName" :label="t('S_GAMETYPE2')">
              <template #default="_scope">
                <span class="game-name" @click="openDialog(_scope.row, scope.row.roundDate)">
                  {{ _scope.row.gameName }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="betAmount" :label="t('S_WAGER_AMOUNT')" />
            <el-table-column prop="commissionable" :label="t('S_EF_STR')" />
            <el-table-column prop="payoff" :label="t('S_PAYOFF_PAYOFF')">
              <template #default="_scope">
                <span v-if="_scope.row.payoff < 0" style="color: #f00">
                  {{ _scope.row.payoff }}
                </span>
                <span v-else>
                  {{ _scope.row.payoff }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </el-table-column>
    </el-table>
    <LotteryDetailDialog
      v-model="visible"
      type="compelete"
      :game-item="gameItem"
    />
  </div>
</template>

<style lang="scss" scoped>
.table-container {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;

  .el-table {
    margin: 15px;

    &.inner_table {
      margin: 0;
    }
  }
}

.game-name {
  color: #54b9ff;
  text-decoration: underline;
  cursor: pointer;
}

:deep(.el-table__inner-wrapper){
  .table-header {
    color: var(--mcenter-collapse-item-text);
    font-size: 15px;
    text-align: center !important;
    background: #eee !important;
  }

  .table-header2 {
    color: var(--mcenter-table-header2--text);
    font-size: 15px;
    text-align: center !important;
    background: var(--mcenter-betinfo-table-header-bg) !important;
  }

  .table-cell {
    color: var(--mcenter-table-cell2--text);
    text-align: center !important;
  }

  .table-cell2 {
    color: var(--mcenter-table-cell2--text);
    text-align: center !important;
  }

  .el-table__empty-text {
    color: var(--mcenter-nodata);
  }
}

:deep(.el-table__footer) {
  .el-table__cell {
    background: var(--mcenter-table-footer--bg);
    
    .cell {
      color: var(--mcenter-table-cell2--text);
      text-align: center !important;
    }
  }
}

:deep(.el-table__expanded-cell) {
  padding: 8px !important;
  background: #f9f9f9;

  .el-table {
    .cell {
      color: var(--mcenter-table-cell2--text);
      text-align: center;
    }
  }
}
</style>