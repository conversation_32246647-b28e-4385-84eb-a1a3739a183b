import { flushPromises, mount } from '@vue/test-utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import { getLotteryCompleteDetail, getLotteryUnCompleteDetail } from '@/api/betrecord'
import { getLotteryGameList } from '@/api/game'
import LotteryDetailDialog from './LotteryDetailDialog.vue'
import { ElMessage } from 'element-plus'

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      'S_ALL': 'All',
      'S_TOTAL': 'Total',
      'S_WAGER_AMOUNT': 'Wager Amount',
      'S_AG_VALID_BET': 'Valid bets',
      'S_PAYOFF_PAYOFF': 'Win gold',
      'S_WIN_GOLD': 'Possible Winning',
      'S_WAGER_DATE': 'Wager Date',
      'S_BET_NUM': 'Bet Number',
      'S_GAMETYPE2': 'Game Type',
      'S_GAMETYPE': 'Game Type',
      'S_CONTENT_MSG': 'Content',
      'S_ENTER_GAME_NAME': 'Please Enter Game Name',
      'S_NODATA': 'There is no betting information today, or your bet today has already resulted.'
    },
  },
})

vi.mock('@/api/betrecord', () => ({
  getLotteryCompleteDetail: vi.fn(),
  getLotteryUnCompleteDetail: vi.fn(),
}))

vi.mock('@/api/game', () => ({
  getLotteryGameList: vi.fn()
}))

vi.mock('@/components/mcenter/PaginationComponent.vue', () => ({
  default: {
    name: 'PaginationComponent',
    props: ['currentPage', 'totalPage'],
    template: '<div class="mock-pagination-component">Mock PaginationComponent</div>',
  },
}))

const mockCompeleteGameItem = {
  gameName: "BB 马力欧梯子",
  count: 2,
  betAmount: "10.00",
  payoff: "-3.85",
  commissionable: "10.00",
  date: '2025-03-05',
  gameType: 'MADR'
}

const mockCompeleteDatailData = {
  winTypeList: [
    {
      id: 'L001',
      name: '定位'
    }
  ],
  wagersList: [
    {
      id: 9215028160,
      wagersDate: '2024-11-28 00:29:51',
      gameType: 'B128',
      winType: '定位',
      betDetail:
        '<font color="red"></font> <font class="show_result" data-title="15,70,37,69,08,39,52,54,07,14,29,75,79,10,38,50,33,03,60,67,07,06,02" data-bingo="">第202411280031期</font> <font color="red"></font><font color="red">定位 1</font>@<strong><font color="red">320.00</font></strong>',
      betAmount: '5.00',
      commissionable: '5.00',
      payoff: '-5.00'
    }
  ],
  total: {
    totalBetAmount: '10.00',
    totalCommissionable: '10.00',
    totalPayoff: '-10.00'
  },
  pagination: {
    currentPage: 1,
    totalPage: 1
  },
  maintain: false,
  maintainInfo: ''
}

const mockUncompeleteGameItem = {

  gameType: "HKLT",
  gameName: "香港六合彩",
  count: 2,
  betAmount: "40.00"
}

const mockUncompeleteDatailData = {
  winTypeList: [
    {
      id: 'L001',
      name: '特別號'
    }
  ],
  wagersList: [
    {
      id: 9215027912,
      wagersDate: '2024-11-18 04:50:41',
      gameType: 'HKLT',
      winType: '特別號',
      betDetail:
        '<font color="red"></font> <font class="show_result" data-title="" data-bingo="">第124期</font> <font color="red"></font><font color="red">特別號01</font>@<strong><font color="red">48.00</font></strong>',
      betAmount: '5.00',
      maxPayoff: '235.00'
    }
  ],
  total: {
    totalBetAmount: '5.00',
    totalMaxPayoff: '235.00'
  },
  pagination: {
    currentPage: 1,
    totalPage: 1
  },
  maintain: false,
  maintainInfo: ''
}

const mockLotteryGameList = {
  'HKLT': '香港六合彩',
  'MADR': 'BB 马力欧梯子'
}

describe('LotteryDetailDialog', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders correctly when type is compelete and not in maintenance', async () => {
    vi.mocked(getLotteryGameList).mockResolvedValue(mockLotteryGameList)
    vi.mocked(getLotteryCompleteDetail).mockResolvedValue(mockCompeleteDatailData)

    const wrapper = mount(LotteryDetailDialog, {
      props: {
        modelValue: false,
        gameItem: { ...mockCompeleteGameItem },
        type: 'compelete'
      },
      global: {
        plugins: [i18n]
      },
    })

    const vm = wrapper.vm as any

    await wrapper.setProps({ modelValue: true })
    await flushPromises()

    const elSelects = wrapper.findAllComponents({ name: 'el-select' })
    const gameListSelect = elSelects[0]
    const gameListSelectOptions = gameListSelect.findAllComponents({ name: 'el-option' })
    const gameListSelectOptionsText = ['香港六合彩', 'BB 马力欧梯子']
    const wTypesSelect = elSelects[1]
    const wTypesSelectOptions = wTypesSelect.findAllComponents({ name: 'el-option' })
    const wTypesSelectOptionsText = ['All', '定位']
    const elTables = wrapper.findAllComponents({ name: 'el-table' })
    const topTable = elTables[0]
    const topTableHeaderText = ['', 'Wager Amount', 'Valid bets', 'Win gold']
    const topTableHeaderCells = topTable.findAll('thead .cell')
    const topTableBodyText = ['Total', '10.00', '10.00', '-10.00']
    const topTableBodyCells = topTable.findAll('tbody .cell')
    const detailTable = elTables[1]
    const detailTableHeaderText = ['Wager Date', 'Bet Number', 'Game Type', 'Game Type', 'Content', 'Wager Amount', 'Valid bets', 'Win gold']
    const detailTableHeaderCells = detailTable.findAll('thead .cell')
    const detailTableBodyText = [
      ...['2024-11-28 00:29:51', '9215028160', 'BB 马力欧梯子', '定位', '第202411280031期 定位 1@320.00', '5.00', '5.00', '-5.00'],
    ]
    const detailTableBodyCells = detailTable.findAll('tbody .cell')
    const paginationComponent = wrapper.findComponent({ name: 'PaginationComponent' })

    expect(elSelects).toHaveLength(2)
    expect(gameListSelect.text()).toBe('BB 马力欧梯子')
    expect(gameListSelectOptions.every((option, index) => option.text() === gameListSelectOptionsText[index])).toBe(true)
    expect(wTypesSelectOptions.every((option, index) => option.text() === wTypesSelectOptionsText[index])).toBe(true)
    expect(topTableHeaderCells.every((cell, index) => cell.text() === topTableHeaderText[index] )).toBe(true)
    expect(topTableBodyCells.every((cell, index) => cell.text() === topTableBodyText[index] )).toBe(true)
    expect(topTableBodyCells[3].find('span').attributes('style')).toBeTruthy()
    expect(detailTableHeaderCells.every((cell, index) => cell.text() === detailTableHeaderText[index] )).toBe(true)
    expect(detailTableBodyCells.every((cell, index) => cell.text() === detailTableBodyText[index] )).toBe(true)
    expect(detailTableBodyCells[7].find('span').attributes('style')).toBeTruthy()
    expect(paginationComponent.exists()).toBe(true)

    vm.compTopData = [{
      sum: "总计",
      gold: "10.00",
      valid: "10.00",
      wingold: "3.85"
    }]
    await vm.$nextTick()

    expect(topTableBodyCells[3].find('span').attributes('style')).toBeFalsy()
  })

  it('renders correctly when type is uncompelete and not in maintenance', async () => {
    vi.mocked(getLotteryGameList).mockResolvedValue(mockLotteryGameList)
    vi.mocked(getLotteryUnCompleteDetail).mockResolvedValue(mockUncompeleteDatailData)

    const wrapper = mount(LotteryDetailDialog, {
      props: {
        modelValue: false,
        gameItem: { ...mockUncompeleteGameItem },
        type: 'uncompelete'
      },
      global: {
        plugins: [i18n],
      },
    })

    await wrapper.setProps({ modelValue: true })
    await flushPromises()

    const elSelects = wrapper.findAllComponents({ name: 'el-select' })
    const gameListSelect = elSelects[0]
    const gameListSelectOptions = gameListSelect.findAllComponents({ name: 'el-option' })
    const gameListSelectOptionsText = ['香港六合彩', 'BB 马力欧梯子']
    const wTypesSelect = elSelects[1]
    const wTypesSelectOptions = wTypesSelect.findAllComponents({ name: 'el-option' })
    const wTypesSelectOptionsText = ['All', '特別號']
    const elTables = wrapper.findAllComponents({ name: 'el-table' })
    const topTable = elTables[0]
    const topTableHeaderText = ['', 'Wager Amount', 'Possible Winning']
    const topTableHeaderCells = topTable.findAll('thead .cell')
    const topTableBodyText = ['Total', '5.00', '235.00']
    const topTableBodyCells = topTable.findAll('tbody .cell')
    const detailTable = elTables[1]
    const detailTableHeaderText = ['Wager Date', 'Bet Number', 'Game Type', 'Game Type', 'Content', 'Wager Amount', 'Possible Winning']
    const detailTableHeaderCells = detailTable.findAll('thead .cell')
    const detailTableBodyText = [
      ...['2024-11-18 04:50:41', '9215027912', '香港六合彩', '特別號', '第124期 特別號01@48.00', '5.00', '235.00'],
    ]
    const detailTableBodyCells = detailTable.findAll('tbody .cell')
    const paginationComponent = wrapper.findComponent({ name: 'PaginationComponent' })

    expect(elSelects).toHaveLength(2)
    expect(gameListSelect.text()).toBe('香港六合彩')
    expect(gameListSelectOptions.every((option, index) => option.text() === gameListSelectOptionsText[index])).toBe(true)
    expect(wTypesSelectOptions.every((option, index) => option.text() === wTypesSelectOptionsText[index])).toBe(true)
    expect(topTableHeaderCells.every((cell, index) => cell.text() === topTableHeaderText[index] )).toBe(true)
    expect(topTableBodyCells.every((cell, index) => cell.text() === topTableBodyText[index] )).toBe(true)
    expect(detailTableHeaderCells.every((cell, index) => cell.text() === detailTableHeaderText[index] )).toBe(true)
    expect(detailTableBodyCells.every((cell, index) => cell.text() === detailTableBodyText[index] )).toBe(true)
    expect(paginationComponent.exists()).toBe(true)
  })

  it('renders maintenance message when type is compelete in maintenance', async () => {
    vi.mocked(getLotteryGameList).mockResolvedValue(mockLotteryGameList)
    vi.mocked(getLotteryCompleteDetail).mockResolvedValue({
      maintain: true,
      maintainInfo: 'Maintenance in progress',
    } as any)
    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())

    const wrapper = mount(LotteryDetailDialog, {
      props: {
        modelValue: false,
        gameItem: { ...mockCompeleteGameItem },
        type: 'compelete'
      },
      global: {
        plugins: [i18n]
      },
    })

    await wrapper.setProps({ modelValue: true })
    await flushPromises()

    expect(ElMessage.error).toHaveBeenCalledWith({
      dangerouslyUseHTMLString: true,
      message: "Maintenance in progress",
    })
    expect(wrapper.emitted('update:modelValue')).toEqual([[false]])
  })

  it('renders maintenance message when type is uncompelete in maintenance', async () => {
    vi.mocked(getLotteryGameList).mockResolvedValue(mockLotteryGameList)
    vi.mocked(getLotteryUnCompleteDetail).mockResolvedValue({
      maintain: true,
      maintainInfo: 'Maintenance in progress',
    } as any)
    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())

    const wrapper = mount(LotteryDetailDialog, {
      props: {
        modelValue: false,
        gameItem: { ...mockUncompeleteGameItem },
        type: 'uncompelete'
      },
      global: {
        plugins: [i18n]
      },
    })

    await wrapper.setProps({ modelValue: true })
    await flushPromises()

    expect(ElMessage.error).toHaveBeenCalledWith({
      dangerouslyUseHTMLString: true,
      message: "Maintenance in progress",
    })
    expect(wrapper.emitted('update:modelValue')).toEqual([[false]])
  })

  it('shows error message when type is compelete and fetch fails', async () => {
    const errorMessage = 'Failed to fetch data'
    vi.mocked(getLotteryCompleteDetail).mockRejectedValue(new Error(errorMessage))
    vi.mocked(getLotteryGameList).mockResolvedValue(mockLotteryGameList)
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())

    const wrapper = mount(LotteryDetailDialog, {
      props: {
        modelValue: false,
        gameItem: { ...mockCompeleteGameItem },
        type: 'compelete'
      },
      global: {
        plugins: [i18n],
      },
    })

    await wrapper.setProps({ modelValue: true })
    await flushPromises()

    expect(console.error).toHaveBeenCalledWith(new Error(errorMessage))
    expect(ElMessage.error).toHaveBeenCalledWith(errorMessage)
  })

  it('shows error message when type is uncompelete and fetch fails', async () => {
    const errorMessage = 'Failed to fetch data'
    vi.mocked(getLotteryUnCompleteDetail).mockRejectedValue(new Error(errorMessage))
    vi.mocked(getLotteryGameList).mockResolvedValue(mockLotteryGameList)
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())

    const wrapper = mount(LotteryDetailDialog, {
      props: {
        modelValue: false,
        gameItem: { ...mockUncompeleteGameItem },
        type: 'uncompelete'
      },
      global: {
        plugins: [i18n],
      },
    })

    await wrapper.setProps({ modelValue: true })
    await flushPromises()

    expect(console.error).toHaveBeenCalledWith(new Error(errorMessage))
    expect(ElMessage.error).toHaveBeenCalledWith(errorMessage)
  })

  it('fetchs detail data on game or wType change', async () => {
    vi.mocked(getLotteryGameList).mockResolvedValue(mockLotteryGameList)
    vi.mocked(getLotteryCompleteDetail).mockResolvedValue(mockCompeleteDatailData)

    const wrapper = mount(LotteryDetailDialog, {
      props: {
        modelValue: false,
        gameItem: { ...mockCompeleteGameItem },
        type: 'compelete'
      },
      global: {
        plugins: [i18n]
      },
    })

    const vm = wrapper.vm as any
    const changeDialogSelectSpy = vi.spyOn(vm, 'changeDialogSelect')

    await wrapper.setProps({ modelValue: true })
    await flushPromises()

    const elSelects = wrapper.findAllComponents({ name: 'el-select' })
    const gameListSelect = elSelects[0]
    const wTypesSelect = elSelects[1]

    await gameListSelect.vm.$emit('change')

    expect(changeDialogSelectSpy).toHaveBeenCalledTimes(1)

    await wTypesSelect.vm.$emit('change')

    expect(changeDialogSelectSpy).toHaveBeenCalledTimes(2)
  })

  it('fetchs detail data on current page change', async () => {
    vi.mocked(getLotteryGameList).mockResolvedValue(mockLotteryGameList)
    vi.mocked(getLotteryCompleteDetail).mockResolvedValue(mockCompeleteDatailData)

    const wrapper = mount(LotteryDetailDialog, {
      props: {
        modelValue: false,
        gameItem: { ...mockCompeleteGameItem },
        type: 'compelete'
      },
      global: {
        plugins: [i18n]
      },
    })

    const vm = wrapper.vm as any
    const handleChangePageSpy = vi.spyOn(vm, 'handleChangePage')

    await wrapper.setProps({ modelValue: true })
    await flushPromises()

    const paginationComponent = wrapper.findComponent({ name: 'PaginationComponent' })

    await paginationComponent.vm.$emit('currentChange')

    expect(handleChangePageSpy).toHaveBeenCalled()
  })
})
