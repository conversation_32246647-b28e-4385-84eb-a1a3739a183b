<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { getLotteryCompleteDetail, getLotteryUnCompleteDetail } from '@/api/betrecord'
import { getLotteryGameList } from '@/api/game'
import { ILotteryCompleteDetail, ILotteryUnCompleteDetail } from '@/types/betrecord'
import PaginationComponent from '@/components/mcenter/PaginationComponent.vue'
import { useCookies } from 'vue3-cookies'

const visible = defineModel({
  type: Boolean,
  default: false,
  required: true
})

const props = defineProps({
  gameItem: {
    type: Object,
    default: () => ({}),
    required: true
  },
  type: {
    type: String,
    default: 'uncompelete',
    required: true
  }
})

const { t } = useI18n()
const { cookies } = useCookies()
const title = ref('')
const date = ref('')
const game = ref('')
const gameList = ref<{[key: string]: string}>({})
const wTypes = ref<{id: string, name: string}[]>([])
const wType = ref('')
const compTopData = ref([
  {
    sum: t('S_TOTAL'),
    totalBetAmount: '--',
    totalCommissionable: '--',
    totalPayoff: '--'
  }
])

const unCompTopData = ref([
  {
    sum: t('S_TOTAL'),
    totalBetAmount: '--',
    totalMaxPayoff: '--',
  }
])

const dialogData = ref<{
  id: number,
  wagersDate: string,
  betAmount: string,
  betDetail: string,
  winType: string,
  payoff?: string,
  maxPayoff?: string,
  commissionable?: string
}[]>([])
const isLoading = ref(false)
const currPage = ref(1)
const pageCount = computed(() =>  Math.ceil(dialogData.value.length / 5))

const fetchCompDialogDetail = async () => {
  const params = {
    session_id: cookies.get('SESSION_ID'),
    lang: cookies.get('lang'),
    hall_id: Number(localStorage.getItem('hallinfo_hallid')),
    game_id: game.value,
    win_type_id: wType.value,
    date: date.value
  }

  return getLotteryCompleteDetail(params)
}

const fetchUnCompDialogDetail = async () => {
  const params = {
    session_id: cookies.get('SESSION_ID'),
    lang: cookies.get('lang'),
    hall_id: Number(localStorage.getItem('hallinfo_hallid')),
    game_id: game.value,
    win_type_id: wType.value
  }

  return getLotteryUnCompleteDetail(params)
}

const fetchLotteryGameList = async () => {
  try {
    const params = {
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
      lang: cookies.get('lang'),
      list_type: 'report'
    }

    gameList.value = await getLotteryGameList(params)
  } catch(error) {
    console.log(error)
    ElMessage.error((error as Error).message)
  }
}

const changeDialogSelect = async() => {
  const selectGameName = gameList.value[game.value]

  isLoading.value = true

  if (selectGameName) title.value = selectGameName

  try {
    const fetchDetail = props.type === 'uncompelete' ? fetchUnCompDialogDetail : fetchCompDialogDetail
    let res = await fetchDetail()

    if (res.maintain) {
      ElMessage.error({
        dangerouslyUseHTMLString: true,
        message: res.maintainInfo,
      })
      visible.value = false
      return
    }

    if (props.type == 'uncompelete') {
      const data = res as ILotteryUnCompleteDetail
      unCompTopData.value = [{
        sum: t('S_TOTAL'),
        ...data.total
      }]
      dialogData.value = data.wagersList
      wTypes.value = [
        { id: '', name: t('S_ALL') },
        ...data.winTypeList
      ]
    } else {
      const data = res as ILotteryCompleteDetail
      compTopData.value = [{
        sum: t('S_TOTAL'),
        ...data.total
      }]
      dialogData.value = data.wagersList
      wTypes.value = [
        { id: '', name: t('S_ALL') },
        ...res.winTypeList
      ]
    }
  } catch(error) {
    console.error(error)
    ElMessage.error((error as Error).message)
  } finally {
    isLoading.value = false
    if (dialogData.value.length >= 5) {
      currPage.value = 1
      handleChangePage()
    }
  }  
}

watch(() => visible.value, (newValue) => {
  if(newValue) {
    title.value = props.gameItem.gameName
    game.value = props.gameItem.gameType
    date.value = props.gameItem.date
    changeDialogSelect()
  }
})

const handleChangePage = () => {
  if(props.type == 'uncompelete') {
    dialogData.value = dialogData.value.slice((currPage.value - 1) * 5, (currPage.value * 5) - 1)
  } else {
    dialogData.value = dialogData.value.slice((currPage.value - 1) * 5 , currPage.value * 5)
  }
}

fetchLotteryGameList()
</script>

<template>
  <el-dialog v-model="visible" :title="title" width="800">
    <div class="select-group" v-loading="isLoading">
      <el-select
        v-model="game"
        :placeholder="t('S_ENTER_GAME_NAME')"
        @change="changeDialogSelect"
      >
        <el-option
          v-for="(item,key) in gameList"
          :key="key"
          :label="item"
          :value="key"
        />
      </el-select>
      <el-select v-model="wType" @change="changeDialogSelect">
        <el-option
          v-for="item in wTypes"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </div>

    <el-table 
      :data="props.type == 'compelete' ? compTopData : unCompTopData"
      :border="true"
      header-cell-class-name="table-header"
      cell-class-name="table-cell"
      v-loading="isLoading"
    >
    <template v-if="props.type == 'compelete'">
      <el-table-column prop="sum" label="" />
      <el-table-column prop="totalBetAmount" :label="t('S_WAGER_AMOUNT')" />
      <el-table-column prop="totalCommissionable" :label="t('S_AG_VALID_BET')" />
      <el-table-column prop="totalPayoff" :label="t('S_PAYOFF_PAYOFF')">
        <template #default="scope">
          <span v-if="scope.row.totalPayoff < 0" style="color: #f00">
            {{ scope.row.totalPayoff }}
          </span>
          <span v-else>
            {{ scope.row.totalPayoff }}
          </span>
        </template>
      </el-table-column>
    </template>
    <template v-else>
      <el-table-column prop="sum" label="" />
      <el-table-column prop="totalBetAmount" :label="t('S_WAGER_AMOUNT')" />
      <el-table-column prop="totalMaxPayoff" :label="t('S_WIN_GOLD')" />
    </template>
    </el-table>
    <br/>
    <el-table
      :data="dialogData"
      header-cell-class-name="table-header2"
      cell-class-name="table-cell2"
      :empty-text="t('S_NODATA')"
      v-loading="isLoading"
    >
      <el-table-column prop="wagersDate" :label="t('S_WAGER_DATE')" />
      <el-table-column prop="id" :label="t('S_BET_NUM')" />
      <el-table-column prop="" :label="t('S_GAMETYPE2')" >
        {{ gameList[game] }}
      </el-table-column>
      <el-table-column prop="winType" :label="t('S_GAMETYPE')" />
      <el-table-column prop="betDetail" :label="t('S_CONTENT_MSG')">
        <template #default="_scope">
          <div v-html="_scope.row.betDetail">
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="betAmount" :label="t('S_WAGER_AMOUNT')" />
      <template v-if="props.type === 'compelete'">
        <el-table-column  prop="commissionable" :label="t('S_AG_VALID_BET')" />
        <el-table-column prop="payoff"  :label="t('S_PAYOFF_PAYOFF')">
          <template #default="_scope">
            <span :style="{ color: _scope.row.payoff < 0 ? '#f00' : '' }">
              {{ _scope.row.payoff }}
            </span>
          </template>
        </el-table-column>
      </template>
      <template v-else>
        <el-table-column prop="maxPayoff" :label="t('S_WIN_GOLD')">
          <template #default="_scope">
            <span :style="{ color: _scope.row.maxPayoff < 0 ? '#f00' : '' }">
              {{ _scope.row.maxPayoff }}
            </span>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <PaginationComponent
      v-if="dialogData.length > 0"
      v-model:current-page="currPage"
      :page-count="pageCount"
      @current-change="handleChangePage()"
    />
  </el-dialog>
</template>

<style lang="scss" scoped>
.el-select {
  width: 30%;
  margin: 10px;
}

:deep(.el-table__inner-wrapper){
  .table-header {
    color: var(--mcenter-collapse-item-text);
    font-size: 15px;
    text-align: center !important;
    background: #eee !important;
  }

  .table-header2 {
    color: var(--mcenter-table-header2--text);
    font-size: 15px;
    text-align: center !important;
    background: var(--mcenter-betinfo-table-header-bg) !important;
  }

  .table-cell {
    color: var(--mcenter-table-cell2--text);
    text-align: center !important;
  }

  .table-cell2 {
    color: var(--mcenter-table-cell2--text);
    text-align: center !important;
  }

  .el-table__empty-text {
    color: var(--mcenter-nodata);
  }
}

:deep(.el-table__footer) {
  .el-table__cell {
    background: var(--mcenter-table-footer--bg);
    
    .cell {
      color: var(--mcenter-table-cell2--text);
      text-align: center !important;
    }
  }
}
</style>