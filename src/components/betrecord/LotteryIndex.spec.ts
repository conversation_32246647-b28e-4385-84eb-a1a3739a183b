import { mount } from '@vue/test-utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import LotteryIndex from './LotteryIndex.vue'

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      'S_OPEN_BETS': 'OPEN BETS',
      'S_SETTLED_BETS': 'SETTLED BETS',
    },
  },
})

vi.mock('@/components/betrecord/LotteryCompleteTable.vue', () => ({
  default: {
    name: 'LotteryCompleteTable',
    template: '<div class="mock-lottery-complete-table">Mock LotteryCompleteTable</div>',
  },
}))
vi.mock('@/components/betrecord/LotteryUncompleteTable.vue', () => ({
  default: {
    name: 'LotteryUncompleteTable',
    template: '<div class="mock-lottery-uncomplete-table">Mock LotteryUncompleteTable</div>',
  },
}))


describe('LotteryIndex', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders correctly when not in maintenance', async () => {
    const wrapper = mount(LotteryIndex, {
      global: {
        plugins: [i18n],
      },
    })

    const vm = wrapper.vm as any

    await vm.$nextTick()
    await nextTick()

    const elRadioGroup = wrapper.findComponent({ name: 'el-radio-group' })
    const elRadios = wrapper.findAllComponents({ name: 'el-radio-button' })

    expect(elRadioGroup.exists()).toBe(true)
    expect(elRadios).toHaveLength(2)
    expect(elRadios.every(radio => radio.exists())).toBe(true)
    expect(elRadios[0].attributes('class')).toContain('is-active')
    expect(wrapper.findComponent({ name: 'LotteryUncompleteTable' }).exists()).toBe(true)
    expect(wrapper.findComponent({ name: 'LotteryCompleteTable' }).exists()).toBe(false)

    await elRadios[1].find('input').setValue(true)
    await vm.$nextTick()

    expect(elRadios[1].attributes('class')).toContain('is-active')
    expect(wrapper.findComponent({ name: 'LotteryUncompleteTable' }).exists()).toBe(false)
    expect(wrapper.findComponent({ name: 'LotteryCompleteTable' }).exists()).toBe(true)
  })

  it('renders maintenance message when LotteryUncompleteTable is in maintenance', async () => {
    const wrapper = mount(LotteryIndex, {
      global: {
        plugins: [i18n],
      },
    })

    const vm = wrapper.vm as any
    const openSpy = vi.spyOn(vm, 'handleMaintain' as any)

    await vm.$forceUpdate()

    const lotteryUncompleteTable = wrapper.findComponent({ name: 'LotteryUncompleteTable' })
    await lotteryUncompleteTable.vm.$emit('maintain', true, 'Maintenance in progress')

    expect(openSpy).toHaveBeenCalled()
    expect(wrapper.html()).toContain('Maintenance in progress')
  })
  
  it('renders maintenance message when LotteryCompleteTable is in maintenance', async () => {
    const wrapper = mount(LotteryIndex, {
      global: {
        plugins: [i18n],
      },
    })

    const vm = wrapper.vm as any
    const openSpy = vi.spyOn(vm, 'handleMaintain' as any)

    await vm.$nextTick()
    await nextTick()
    await wrapper.findAllComponents({ name: 'el-radio-button' })[1].find('input').setValue(true)
    await vm.$nextTick()

    const LotteryCompleteTable = wrapper.findComponent({ name: 'LotteryCompleteTable' })
    await LotteryCompleteTable.vm.$emit('maintain', true, 'Maintenance in progress')

    expect(openSpy).toHaveBeenCalled()
    expect(wrapper.html()).toContain('Maintenance in progress')
  })
})