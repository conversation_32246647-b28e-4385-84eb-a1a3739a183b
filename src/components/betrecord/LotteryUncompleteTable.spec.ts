import { flushPromises, mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import LotteryUncompleteTable from './LotteryUncompleteTable.vue'
import { getLotteryUncomplete } from '@/api/betrecord'

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      'S_NO_DATA': 'No Data',
      'S_TOTAL': 'Total',
      'S_GAMETYPE2': 'Game Type',
      'S_COUNT': 'Count',
      'S_WAGER_AMOUNT': 'Wager Amount'
    },
  },
})

vi.mock('@/components/betrecord/LotteryDetailDialog.vue', () => ({
  default: {
    name: 'LotteryDetailDialog',
    props: ['modelValue', 'type', 'gameItem'],
    template: '<div class="mock-lottery-detail-dialog">Mock LotteryDetailDialog</div>',
  },
}))

vi.mock('@/api/betrecord', () => ({
  getLotteryUncomplete: vi.fn(),
}))

vi.mock('@/utils/request', () => {
  return {
    default: {
      get: vi.fn(),
    }
  }
})

const mockLotteryUnCompleteData = {
  list: [{
      gameType: 'HKLT',
      gameName: '香港六合彩',
      count: 1,
      betAmount: '5.00'
  }],
  total: {
    totalCount: 1,
    totalBetAmount: '5.00'
  },
  maintain: false,
  maintainInfo: ''
}

vi.mocked(getLotteryUncomplete).mockResolvedValue(mockLotteryUnCompleteData)

describe('LotteryUncompleteTable', () => {
  it('renders correctly with default data', async () => {    
    const wrapper = mount(LotteryUncompleteTable, {
      global: {
        plugins: [i18n],
      },
    })
    const vm = wrapper.vm as any

    await vm.$nextTick()
    await nextTick()
    await flushPromises()

    const lotteryTable = wrapper.findComponent({ name: "el-table" })
    const lotteryTableHeaderText = ['Game Type', 'Count', 'Wager Amount']
    const lotteryTableHeaderCells = lotteryTable.findAll('thead .cell')
    const lotteryTableBodyText = [
      ...['香港六合彩', '1', '5.00'],
      ...['总计', '1', '5']
    ]
    const lotteryTableBodyCells = lotteryTable.findAll('tbody .cell')

    expect(lotteryTableHeaderCells.every((cell, index) => cell.text() === lotteryTableHeaderText[index] )).toBe(true)
    expect(lotteryTableBodyCells.every((cell, index) => cell.html().includes(lotteryTableBodyText[index]) )).toBe(true)
    expect(wrapper.findComponent({ name: 'LotteryDetailDialog' }).exists()).toBe(true)
  })

  it('calls the openDialog on click the game-name span', async () => {
    const wrapper = mount(LotteryUncompleteTable, {
      global: {
        plugins: [i18n],
      },
    })

    const vm = wrapper.vm as any
    const openDialogSpy = vi.spyOn(vm, 'openDialog')

    await wrapper.vm.$nextTick()
    await nextTick()
    await flushPromises()

    const lotteryTable = wrapper.findComponent({ name: 'el-table' })
    const gameNameEl = lotteryTable.find('tbody .cell .game-name')

    await gameNameEl.trigger('click')

    expect(openDialogSpy).toHaveBeenCalledWith({
      gameType: 'HKLT',
      gameName: '香港六合彩',
      count: 1,
      betAmount: '5.00'
    })
  })
})
