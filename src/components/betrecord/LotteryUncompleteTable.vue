<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { getLotteryUncomplete } from '@/api/betrecord'
import LotteryDetailDialog from '@/components/betrecord/LotteryDetailDialog.vue'
import { useCookies } from 'vue3-cookies'

const { t } = useI18n()
const { cookies } = useCookies()
const visible = ref(false)
const gameItem = ref({})
const tableData = ref([
  {
    gameName: '',
    count: 0,
    betAmount: '0'
  }
])
const isLoading = ref(false)

const emit = defineEmits(['maintain'])

const fetchLotteryUncomplete = async () => {
  isLoading.value = true

  try {
    const params = {
      session_id: cookies.get('SESSION_ID'),
      lang: cookies.get('lang'),
      hall_id: Number(localStorage.getItem('hallinfo_hallid'))
    }

    const res = await getLotteryUncomplete(params)
    
    if(res.maintain) {
      emit('maintain', true, res.maintainInfo)
    } else {
      tableData.value = res.list
    }
  } catch (error) {
    console.log(error)
    ElMessage.error((error as Error).message)
  } finally {
    isLoading.value = false
  }
}

const openDialog = (item: any) => {
  gameItem.value = item
  visible.value = true
}

fetchLotteryUncomplete()
</script>

<template>
  <div class="table-container" v-loading="isLoading">
    <el-table
      :data="tableData"
      header-cell-class-name="table-header2"
      cell-class-name="table-cell2"
      :border="true"
      show-summary
      :sum-text="t('S_TOTAL')"
      :empty-text="t('S_NO_DATA')"
    >
      <el-table-column prop="gameName" :label="t('S_GAMETYPE2')">
        <template #default="scope">
          <span class="game-name" @click="openDialog(scope.row)">
            {{ scope.row.gameName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="count" :label="t('S_COUNT')" />
      <el-table-column prop="betAmount" :label="t('S_WAGER_AMOUNT')" />
    </el-table>

    <LotteryDetailDialog
      v-model="visible"
      type="uncompelete"
      :game-item="gameItem"
    />
  </div>
</template>

<style lang="scss" scoped>
.table-container {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;

  .el-table {
    margin: 15px;

    &.inner_table {
      margin: 0;
    }
  }
}

.game-name {
  color: #54b9ff;
  text-decoration: underline;
  cursor: pointer;
}

:deep(.el-table__inner-wrapper){
  .table-header {
    color: var(--mcenter-collapse-item-text);
    font-size: 15px;
    text-align: center !important;
    background: #eee !important;
  }

  .table-header2 {
    color: var(--mcenter-table-header2--text);
    font-size: 15px;
    text-align: center !important;
    background: var(--mcenter-betinfo-table-header-bg) !important;
  }

  .table-cell {
    color: var(--mcenter-table-cell2--text);
    text-align: center !important;
  }

  .table-cell2 {
    color: var(--mcenter-table-cell2--text);
    text-align: center !important;
  }

  .el-table__empty-text {
    color: var(--mcenter-nodata);
  }
}

:deep(.el-table__footer) {
  .el-table__cell {
    background: var(--mcenter-table-footer--bg);
    
    .cell {
      color: var(--mcenter-table-cell2--text);
      text-align: center !important;
    }
  }
}
</style>