import { mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'
import { getSportComplete } from '@/api/betrecord'
import SportCompeleteTable from './SportCompeleteTable.vue'

vi.mock('vue-i18n', () => ({
  useI18n: vi.fn(() => ({
    t: vi.fn((msg: string) => msg),
  })),
}));

vi.mock('@/components/betrecord/SportCompleteDialog.vue', () => ({
  default: {
    name: 'SportCompleteDialog',
    props: ['modelValue', 'title', 'ifrUrl'],
    template: '<div class="mock-sport-complete-dialog">Mock SportCompleteDialog</div>',
  },
}))

vi.mock('@/api/betrecord', () => ({
  getSportComplete: vi.fn(),
}))

const mockSportCompeleteData = {
  wagers: [
    {
      date: '2024-11-20',
      weekday: 'Wed',
      statisList: [
        {
          id: 11,
          name: '足球',
          count: 2,
          betAmount: '20.00',
          payoff: '-0.20',
          commissionable: '19.80'
        },
        {
          id: 2,
          name: '複式過關',
          count: 1,
          betAmount: '10.00',
          payoff: '-10.00',
          commissionable: '10.00'
        }
      ],
      subtotal: {
        betAmount: '30.00',
        payoff: '-10.20',
        commissionable: '29.80'
      }
    }
  ],
  total: {
    betAmount: '140.00',
    payoff: '-61.80',
    commissionable: '121.80'
  },
  maintain: false,
  maintainInfo: ''
}

describe('SportCompeleteTable', () => {
  it('renders correctly with default props', async () => {
    vi.mocked(getSportComplete).mockResolvedValue(mockSportCompeleteData)

    const wrapper = mount(SportCompeleteTable)
    const vm = wrapper.vm as any

    await vm.$nextTick()
    await nextTick()

    const elTables = wrapper.findAllComponents({ name: "el-table" })
    const topTable = elTables[0]
    const topTableHeaderCells = topTable.findAll('thead .cell')
    const topTableBodyText = ['S_TOTAL', '140.00', '121.80', '-61.80']
    const topTableBodyCells = topTable.findAll('tbody .cell')
    const sportTable = elTables[1]
    const sportTableHeaderCells = sportTable.findAll('thead .cell')
    const sportTableBodyText = ['Wed2024-11-20', '30.00', '29.80', '-10.20', '']
    const sportTableBodyCells = sportTable.findAll('tbody .cell')

    expect(topTableHeaderCells).toHaveLength(4)
    expect(topTableBodyCells.every((cell, index) => cell.text() === topTableBodyText[index] )).toBe(true)
    expect(topTableBodyCells[3].find('span').attributes('style')).toBeTruthy()
    expect(sportTableHeaderCells).toHaveLength(5)
    expect(sportTableBodyCells.every((cell, index) => cell.text() === sportTableBodyText[index] )).toBe(true)
    expect(sportTableBodyCells[3].find('span').attributes('style')).toBeTruthy()
    expect(wrapper.findComponent({ name: 'SportCompleteDialog' }).exists()).toBe(true)

    await sportTableBodyCells[4].find('.el-table__expand-icon').trigger('click')

    const sportExpandTable = sportTable.find('.el-table__expanded-cell').findComponent({ name: 'el-table' })
    const sportExpandTableHeaderCells = sportExpandTable.findAll('thead .cell')
    const sportExpandTableBodyText = [
      ...['足球', '20.00', '19.80', '-0.20'],
      ...['複式過關', '10.00', '10.00', '-10.00']
    ]
    const sportExpandTableBodyCells = sportExpandTable.find('tbody').findAll('.cell')

    expect(sportExpandTableHeaderCells).toHaveLength(4)
    expect(sportExpandTableBodyCells.every((cell, index) => cell.html().includes(sportExpandTableBodyText[index]) )).toBe(true)

    vm.topTableData = [{
      sum: "总计",
      betAmount: '30.00',
      commissionable: '29.80',
      payoff: '10.20',
    }]
    vm.tableData = [{
      date: '2024-11-20',
      weekday: 'Wed',
      statisList: [],
      subtotal: {
        betAmount: '30.00',
        commissionable: '29.80',
        payoff: '10.20'
      }
    }]

    await vm.$nextTick()

    expect(topTableBodyCells[3].find('span').attributes('style')).toBeFalsy()
    expect(sportTableBodyCells[3].find('span').attributes('style')).toBeFalsy()
  })

  it('calls the openDialog on click the game-name span', async () => {
    vi.mocked(getSportComplete).mockResolvedValue(mockSportCompeleteData)
    const wrapper = mount(SportCompeleteTable)

    const vm = wrapper.vm as any
    const openDialogSpy = vi.spyOn(vm, 'openDialog')

    await vm.$nextTick()
    await nextTick()

    const elTables = wrapper.findAllComponents({ name: "el-table" })
    const sportTable = elTables[1]
    const sportTableBodyCells = sportTable.findAll('tbody .cell')

    await sportTableBodyCells[4].find('.el-table__expand-icon').trigger('click')

    const sportExpandTable = sportTable.find('.el-table__expanded-cell').findComponent({ name: 'el-table' })
    const gameNameEl = sportExpandTable.find('tbody .cell .game-name')

    await gameNameEl.trigger('click')

    expect(openDialogSpy).toHaveBeenCalledWith(
      {
        date: '2024-11-20',
        weekday: 'Wed',
        statisList: [
          {
            id: 11,
            name: '足球',
            count: 2,
            betAmount: '20.00',
            payoff: '-0.20',
            commissionable: '19.80'
          },
          {
            id: 2,
            name: '複式過關',
            count: 1,
            betAmount: '10.00',
            payoff: '-10.00',
            commissionable: '10.00'
          }
        ],
        subtotal: { betAmount: '30.00', payoff: '-10.20', commissionable: '29.80' }
      },
      '2024-11-20',
      11
    )
  })
})
