<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { getSportComplete } from '@/api/betrecord'
import SportCompeleteDialog from '@/components/betrecord/SportCompleteDialog.vue'
import { useCookies } from 'vue3-cookies'
import { ISportComplete } from '@/types/betrecord'

const { t } = useI18n()
const { cookies } = useCookies()
const dialogVisible = ref(false)
const gameItem = ref({})
const searchData = ref<{ type: number | undefined, date: string, gameName: string }>({ type: undefined, date: '', gameName: '' })
const topTableData = ref([
  {
    sum: t('S_TOTAL'),
    betAmount: '--',
    payoff: '--',
    commissionable: '--'
  }
])
const tableData = ref([
  {
    date: '',
    weekday: '',
    statisList: [
      {
        id: 0,
        name: '',
        count: 0,
        betAmount: '',
        payoff: '',
        commissionable: '',
      }
    ],
    subtotal: {
      betAmount: '',
      payoff: '',
      commissionable: ''
    }
  }
])
const isLoading = ref(false)
const emit = defineEmits(['maintain'])

const fetchSportComplete = async () => {
  isLoading.value = true
  try {
    const params = {
      session_id: cookies.get('SESSION_ID'),
      lang: cookies.get('lang'),
      hall_id: Number(localStorage.getItem('hallinfo_hallid'))
    }

    const res = await getSportComplete(params)
    
    if(res.maintain) {
      emit('maintain', true, res.maintainInfo)
    } else {
      const data = res as ISportComplete
      topTableData.value = [{
        sum: t('S_TOTAL'),
        ...data.total
      }]
      tableData.value = data.wagers
    }
  } catch (error) {
    console.log(error)
    ElMessage.error((error as Error).message)
  } finally {
    isLoading.value = false
  }
}

  
const openDialog = (item: any, _date: string, _type: number) => {
  const gameName = item.statisList.find((game: any) => game.id === _type)?.name || ''
  gameItem.value = item
  searchData.value = { type: _type, date: _date, gameName }
  dialogVisible.value = true
}

fetchSportComplete()
</script>

<template>
  <div>
    <div class="table-container" v-loading="isLoading">
      <el-table
        :data="topTableData"
        :border="true"
        header-cell-class-name="table-header"
        cell-class-name="table-cell"
        :empty-text="t('S_NO_DATA')"
      >
        <el-table-column prop="sum" label="" />
        <el-table-column prop="betAmount" :label="t('S_WAGER_AMOUNT')" />
        <el-table-column prop="commissionable" :label="t('S_AG_VALID_BET')" />
        <el-table-column prop="payoff" :label="t('S_PAYOFF_PAYOFF')">
          <template #default="_scope">
            <span :style="_scope.row.payoff < 0 ? 'color: #f00;' : ''">
              {{ _scope.row.payoff }}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <el-table
        :data="tableData"
        header-cell-class-name="table-header2"
        cell-class-name="table-cell2"
        :empty-text="t('S_NO_DATA')"
      >
        <el-table-column prop="date" :label="t('S_DATE')">
          <template #default="scope">
            <div>{{ scope.row.weekday }}</div>
            <div>{{ scope.row.date }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="subtotal.betAmount" :label="t('S_WAGER_AMOUNT')" />
        <el-table-column prop="subtotal.commissionable" :label="t('S_AG_VALID_BET')" />
        <el-table-column prop="subtotal.payoff" :label="t('S_PAYOFF_PAYOFF')">
          <template #default="scope">
            <span :style="scope.row.subtotal.payoff < 0 ? 'color: #f00' : ''">
              {{ scope.row.subtotal.payoff }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="" label="" type="expand">
          <template #default="scope">
            <el-table
              class="inner_table"
              :data="scope.row.statisList"
              :border="true"
              :empty-text="t('S_NODATA')"
            >
              <el-table-column prop="name" :label="t('S_GAMETYPE2')">
                <template #default="_scope">
                  <span class="game-name" @click="openDialog( scope.row, scope.row.date, _scope.row.id)">
                    {{ _scope.row.name }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="betAmount" :label="t('S_WAGER_AMOUNT')" />
              <el-table-column prop="commissionable" :label="t('S_EF_STR')" />
              <el-table-column prop="payoff" :label="t('S_PAYOFF_PAYOFF')" />
            </el-table>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <SportCompeleteDialog
      v-model="dialogVisible"
      :search-data="searchData"
    />
  </div>
</template>

<style lang="scss" scoped>
.table-container {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;

  .el-table {
    margin: 15px;
    &.inner_table {
      margin: 0;
    }
  }

  :deep(.no-head) {
    display: none;
  }
}

.game-name {
  color: #54b9ff;
  text-decoration: underline;
  cursor: pointer;
}

:deep(.el-table__inner-wrapper){
  .table-header {
    color: var(--mcenter-collapse-item-text);
    font-size: 15px;
    text-align: center !important;
    background: #eee !important;
  }

  .table-header2 {
    color: var(--mcenter-table-header2--text);
    font-size: 15px;
    text-align: center !important;
    background: var(--mcenter-betinfo-table-header-bg) !important;
  }

  .table-cell {
    color: var(--mcenter-table-cell2--text);
    text-align: center !important;
  }

  .table-cell2 {
    color: var(--mcenter-table-cell2--text);
    text-align: center !important;
  }

  .el-table__empty-text {
    color: var(--mcenter-nodata);
  }
}

:deep(.el-table__footer) {
  .el-table__cell {
    background: var(--mcenter-table-footer--bg);
    
    .cell {
      color: var(--mcenter-table-cell2--text);
      text-align: center !important;
    }
  }
}

:deep(.el-table__expanded-cell) {
  padding: 8px !important;
  background: #f9f9f9;

  .el-table {
    .cell {
      color: var(--mcenter-table-cell2--text);
      text-align: center;
    }
  }
}
</style>