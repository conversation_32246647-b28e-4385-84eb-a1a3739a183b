import { mount } from '@vue/test-utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import {
  getSportCompleteDetail,
  getSportCompleteContent,
} from '@/api/betrecord'
import SportCompleteDialog from './SportCompleteDialog.vue'
import { ElMessage } from 'element-plus'

vi.mock('vue-i18n', () => ({
  useI18n: vi.fn(() => ({
    t: vi.fn((msg: string) => msg),
  })),
}));

vi.mock('@/api/betrecord', () => ({
  getSportCompleteDetail: vi.fn(),
  getSportCompleteContent: vi.fn(),
}))

vi.mock('@/components/mcenter/PaginationComponent.vue', () => ({
  default: {
    name: 'PaginationComponent',
    props: ['currentPage', 'totalPage'],
    template: '<div class="mock-pagination-component">Mock PaginationComponent</div>',
  },
}))

const mockCompleteDetail = () => ({
  gameList: [
    { id: 2, name: '複式過關' },
    { id: 11, name: '足球' }
  ],
  wagers: [
    {
      wagersId: 4651403822,
      sportName: '足球',
      addDate: '2024-11-20 20:59:47',
      oddType: '歐洲盤',
      betState: '贏',
      betAmount: '10.00',
      payoff: '9.80',
      commissionable: '9.80'
    },
    {
      wagersId: 4651404011,
      sportName: '足球',
      addDate: '2024-11-20 20:59:57',
      oddType: '歐洲盤',
      betState: '輸',
      betAmount: '10.00',
      payoff: '-10.00',
      commissionable: '10.00'
    }
  ],
  total: { betAmount: '20.00', payoff: '-0.20', commissionable: '19.80' },
  maintain: false,
  maintainInfo: ''
})

const mockCompleteContent = () => ({
  content: `
    英雄联盟 - 中国 - LPL 中国职业联赛
    <p style="color: #00FF00;">(贏)</p>
    <p> JingDong Gaming - Top Esports</p>
    <p>
      <span style="color:red;">第1场击杀次数大小</span> - 
      <span style="color:red;">小 (28.5)</span> @ 
      <span style="color:red;">0.8</span>
    </p>
    2025-02-27 05:15
  `,
  maintain: false,
  maintainInfo: ""
})

const mockProps = {
  modelValue: false,
  searchData: {
    type: 11,
    date: '2024-11-20',
    gameName: 'Test'
  }
}

describe('SportCompleteDialog', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders correctly when not in maintenance', async () => {
    vi.mocked(getSportCompleteDetail).mockResolvedValue(mockCompleteDetail())
    vi.mocked(getSportCompleteContent).mockResolvedValue(mockCompleteContent())
    
    const wrapper = mount(SportCompleteDialog, {
      props: mockProps
    })

    const vm = wrapper.vm as any

    await wrapper.setProps({ modelValue: true })
    await nextTick()

    const gameListSelect = wrapper.findComponent({ name: 'el-select' })
    const gameListSelectOptions = gameListSelect.findAllComponents({ name: 'el-option' })
    const gameListSelectOptionsText = ['複式過關', '足球']
    const elTables = wrapper.findAllComponents({ name: 'el-table' })
    const topTable = elTables[0]
    const topTableHeaderCells = topTable.findAll('thead .cell')
    const topTableBodyText = ['S_TOTAL', '20.00', '19.80', '-0.20']
    const topTableBodyCells = topTable.findAll('tbody .cell')
    const detailTable = elTables[1]
    const detailTableHeaderCells = detailTable.findAll('thead .cell')
    const detailTableBodyText = [
      ...['2024-11-20 20:59:47', '4651403822', '足球', 'S_PM_VIEW', '歐洲盤', '贏', '10.00', '9.80', '9.80'],
      ...['2024-11-20 20:59:57', '4651404011', '足球', 'S_PM_VIEW', '歐洲盤', '輸', '10.00', '10.00', '-10.00']
    ]
    const detailTableBodyCells = detailTable.findAll('tbody .cell')
    const paginationComponent = wrapper.findComponent({ name: 'PaginationComponent' })

    expect(gameListSelect.text()).toBe('足球')
    expect(gameListSelectOptions.every((option, index) => option.text() === gameListSelectOptionsText[index])).toBe(true)
    expect(topTableHeaderCells).toHaveLength(4)
    expect(topTableBodyCells.every((cell, index) => cell.text() === topTableBodyText[index] )).toBe(true)
    expect(topTableBodyCells[3].find('span').attributes('style')).toBeTruthy()
    expect(detailTableHeaderCells).toHaveLength(9)
    expect(detailTableBodyCells.every((cell, index) => cell.text() === detailTableBodyText[index] )).toBe(true)
    expect(detailTableBodyCells[8].find('span').attributes('style')).toBeFalsy()
    expect(detailTableBodyCells[17].find('span').attributes('style')).toBeTruthy()
    expect(paginationComponent.exists()).toBe(true)

    await detailTableBodyCells[3].find('.check-btn').trigger('click')

    expect(detailTableBodyCells[3].text()).toBe(`英雄联盟 - 中国 - LPL 中国职业联赛
    (贏)
     JingDong Gaming - Top Esports
    
      第1场击杀次数大小 - 
      小 (28.5) @ 
      0.8
    
    2025-02-27 05:15`)

    vm.topData = [{
      sum: "总计",
      totalValidBet: "8.00",
      totalAmount: "10.00",
      totalPayout: "8.00"
    }]

    await vm.$nextTick()

    expect(topTableBodyCells[3].find('span').attributes('style')).toBeFalsy()
  })

  it('renders maintenance message when fetchs getSportCompleteDetail in maintenance', async () => {
    vi.mocked(getSportCompleteDetail).mockResolvedValue({
      maintain: true,
      maintainInfo: 'Maintenance in progress',
    } as any)
    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())
    
    const wrapper = mount(SportCompleteDialog, {
      props: mockProps
    })

    await wrapper.setProps({ modelValue: true })
    await nextTick()

    expect(ElMessage.error).toHaveBeenCalledWith({
      dangerouslyUseHTMLString: true,
      message: "Maintenance in progress",
    })
    expect(wrapper.emitted('update:modelValue')).toEqual([[false]])
  })

  it('renders maintenance message when fetchs getSportCompleteContent in maintenance', async () => {
    vi.mocked(getSportCompleteDetail).mockResolvedValue(mockCompleteDetail())
    vi.mocked(getSportCompleteContent).mockResolvedValue({
      maintain: true,
      maintainInfo: 'Maintenance in progress',
    } as any)
    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())
    
    const wrapper = mount(SportCompleteDialog, {
      props: mockProps
    })

    await wrapper.setProps({ modelValue: true })
    await nextTick()

    const detailTable = wrapper.findAllComponents({ name: 'el-table' })[1]
    const contentCell = detailTable.findAll('tbody .cell')[3]

    await contentCell.find('.check-btn').trigger('click')

    expect(ElMessage.error).toHaveBeenCalledWith({
      dangerouslyUseHTMLString: true,
      message: "Maintenance in progress",
    })
    expect(wrapper.emitted('update:modelValue')).toEqual([[false]])
  })

  it('shows error message when fetchs getSportCompleteDetail fails', async () => {
    const errorMessage = 'Failed to fetch data'
    vi.mocked(getSportCompleteDetail).mockRejectedValue(new Error(errorMessage))
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())

    const wrapper = mount(SportCompleteDialog, {
      props: mockProps
    })

    await wrapper.setProps({ modelValue: true })

    expect(console.error).toHaveBeenCalledWith(new Error(errorMessage))
    expect(ElMessage.error).toHaveBeenCalledWith(errorMessage)
  })

  it('shows error message when fetchs getSportCompleteContent fails', async () => {
    const errorMessage = 'Failed to fetch data'
    vi.mocked(getSportCompleteDetail).mockResolvedValue(mockCompleteDetail())
    vi.mocked(getSportCompleteContent).mockRejectedValue(new Error(errorMessage))
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())

    const wrapper = mount(SportCompleteDialog, {
      props: mockProps
    })

    await wrapper.setProps({ modelValue: true })
    await nextTick()

    const detailTable = wrapper.findAllComponents({ name: 'el-table' })[1]
    const contentCell = detailTable.findAll('tbody .cell')[3]

    await contentCell.find('.check-btn').trigger('click')

    expect(console.error).toHaveBeenCalledWith(new Error(errorMessage))
    expect(ElMessage.error).toHaveBeenCalledWith(errorMessage)
  })

  it('fetchs detail data on game change', async () => {
    vi.mocked(getSportCompleteDetail).mockResolvedValue(mockCompleteDetail())
    
    const wrapper = mount(SportCompleteDialog, {
      props: mockProps
    })
    const vm = wrapper.vm as any
    const changeDialogSelectSpy = vi.spyOn(vm, 'getCompleteDialogDetail')

    await wrapper.setProps({ modelValue: true })
    await nextTick()

    const gameListSelect = wrapper.findComponent({ name: 'el-select' })

    await gameListSelect.vm.$emit('change')

    expect(changeDialogSelectSpy).toHaveBeenCalled()
  })

  it('fetchs detail data on current page change', async () => {
    vi.mocked(getSportCompleteDetail).mockResolvedValue(mockCompleteDetail())
    
    const wrapper = mount(SportCompleteDialog, {
      props: mockProps
    })
    const vm = wrapper.vm as any
    const handleChangePageSpy = vi.spyOn(vm, 'handleChangePage')

    await wrapper.setProps({ modelValue: true })
    await nextTick()

    const paginationComponent = wrapper.findComponent({ name: 'PaginationComponent' })

    await paginationComponent.vm.$emit('currentChange')

    expect(handleChangePageSpy).toHaveBeenCalled()
  })
})
