<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import {
  getSportCompleteDetail,
  getSportCompleteContent,
} from '@/api/betrecord'
import { ISportCompleteDetail, ISportCompleteContent } from '@/types/betrecord'
import PaginationComponent from '@/components/mcenter/PaginationComponent.vue'
import { useCookies } from 'vue3-cookies'

const visible = defineModel({
  type: Boolean,
  default: false,
  required: true
})

const props = defineProps({
  searchData: {
    type: Object as PropType<{ type: number | undefined, date: string, gameName: string }>,
    default: () => ({ type: undefined, date: '', gameName: '' }),
    required: true
  }
})

const { t } = useI18n()
const { cookies } = useCookies()
const topData = ref([
  {
    sum: t('S_TOTAL'),
    betAmount: '--',
    commissionable: '--',
    payoff: '--'
  }
])
const tableData = ref<{
  wagersId: number
  sportName: string
  addDate: string
  oddType: string
  betState: string
  betAmount: string
  payoff: string
  commissionable: string
  content?: string
}[]>([])
const title = ref('')
const game: Ref<number | undefined> = ref(undefined)
const gameList = ref<{id: number, name: string}[]>([])
const isLoading = ref(true)
const isCellLoading = ref<{[key: string]: boolean}>({})
const currPage = ref(1)
const pageCount = computed(() => {
  return Math.ceil(tableData.value.length / 5)
})

const getContent = async (wagersId: number) => {
  isCellLoading.value[wagersId] = true
  try {
    const params = {
      session_id: cookies.get('SESSION_ID'),
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
      lang: cookies.get('lang'),
      wagers_id: wagersId
    }

    const res = await getSportCompleteContent(params)
    
    if (res.maintain) {
      ElMessage.error({
        dangerouslyUseHTMLString: true,
        message: res.maintainInfo
      })
      visible.value = false
    } else {
      const data = res as ISportCompleteContent
      const wagersItem =  tableData.value.find((item) => item.wagersId == wagersId)

      if (wagersItem) {
        wagersItem.content =  data.content
      }
    }
  } catch(error) {
    console.error(error)
    ElMessage.error((error as Error).message)
  } finally {
    isCellLoading.value[wagersId] = false
  }  
}

const getCompleteDialogDetail = async () => {
  const selectGame = gameList.value.find(item => item.id === Number(game.value))

  isLoading.value = true

  if (selectGame) title.value = selectGame.name

  try {
    const params = {
      session_id: cookies.get('SESSION_ID'),
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
      lang: cookies.get('lang'),
      date: props.searchData.date,
      game_id: Number(props.searchData.type)
    }

    const res = await getSportCompleteDetail(params)
    
    if (res.maintain) {
      ElMessage.error({
        dangerouslyUseHTMLString: true,
        message: res.maintainInfo,
      })
      visible.value = false
    } else {
      const data = res as ISportCompleteDetail
      tableData.value = data.wagers
      gameList.value = removeDuplicatesById(data.gameList)
      topData.value = [{
        sum: t('S_TOTAL'),
        ...data.total
      }]
    }
  } catch(error) {
    console.error(error)
    ElMessage.error((error as Error).message)
  } finally {
    isLoading.value = false
    if (tableData.value.length >= 5) {
      currPage.value = 1
      handleChangePage()
    }
  }
}

const removeDuplicatesById = (arr: {id: number, name: string}[]) => {
  return arr.reduce((unique: any[], item: { id: any }) => {
    if (!unique.some(obj => obj.id === item.id)) {
      unique.push(item);
    }
    return unique;
  }, []);
}

watch(() => visible.value, (newValue) => {
  if (newValue) {
    title.value = props.searchData.gameName
    game.value = props.searchData.type
    getCompleteDialogDetail()
  }
})

const handleChangePage = () => {
  tableData.value = tableData.value.slice((currPage.value - 1) * 5, (currPage.value * 5) - 1)
}

</script>

<template>
  <el-dialog v-model="visible" :title="title" width="800">
    <div class="select-group">
      <el-select
        v-model="game"
        :placeholder="t('S_ENTER_GAME_NAME')"
        @change="getCompleteDialogDetail"
        v-loading="gameList.length < 1"
      >
        <el-option
          v-for="item in gameList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </div>

    <el-table 
      :data="topData"
      :border="true"
      header-cell-class-name="table-header"
      cell-class-name="table-cell"
      v-loading="isLoading"
    >
      <el-table-column prop="sum" label="" />
      <el-table-column prop="betAmount" :label="t('S_WAGER_AMOUNT')" />
      <el-table-column prop="commissionable" :label="t('S_EF_STR')" />
      <el-table-column prop="payoff" :label="t('S_PAYOFF_PAYOFF')">
        <template #default="scope">
          <span :style="scope.row.payoff < 0 ? 'color: #f00' : ''">
            {{ scope.row.payoff }}
          </span>
        </template>
      </el-table-column>
    </el-table>
    <br/>
    <el-table 
      :data="tableData"
      header-cell-class-name="table-header2"
      cell-class-name="table-cell2"
      :empty-text="t('S_NODATA')"
      v-loading="isLoading"
    >
      <el-table-column prop="addDate" :label="t('S_WAGER_DATE')" />
      <el-table-column prop="wagersId" :label="t('S_BET_NUM')" />
      <el-table-column prop="sportName" :label="t('S_GAMETYPE2')" />
      <el-table-column prop="" :label="t('S_CONTENT_MSG')" width="300">
        <template #default="scope">
          <div v-if="scope.row.content" v-html="scope.row.content"></div>
          <div v-else-if="isCellLoading[scope.row.wagersId]" >
            <el-skeleton :rows="1" animated />
          </div>
          <span v-else @click="getContent(scope.row.wagersId)" class="check-btn">
            {{ t('S_PM_VIEW') }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="oddType" :label="t('S_ODDS_TYPE')" />
      <el-table-column prop="betState" :label="t('S_RESULT')" />
      <el-table-column prop="betAmount" :label="t('S_WAGER_AMOUNT')" />
      <el-table-column prop="commissionable" :label="t('S_AG_VALID_BET')" />
      <el-table-column prop="payoff" :label="t('S_PAYOFF_PAYOFF')">
        <template #default="scope">
          <span :style="scope.row.payoff < 0 ? 'color: #f00' : ''">
            {{ scope.row.payoff }}
          </span>
        </template>
      </el-table-column>
    </el-table>
    <PaginationComponent
      v-if="tableData.length > 0"
      v-model:current-page="currPage"
      :page-count="pageCount"
      @current-change="handleChangePage()"
    />
  </el-dialog>
</template>

<style lang="scss" scoped>
.check-btn {
  cursor: pointer;
  color: gray;
}

.el-select {
  width: 30%;
  margin: 10px;
}

:deep(.el-table__inner-wrapper){
  .table-header {
    color: var(--mcenter-collapse-item-text);
    font-size: 15px;
    text-align: center !important;
    background: #eee !important;
  }

  .table-header2 {
    color: var(--mcenter-table-header2--text);
    font-size: 15px;
    text-align: center !important;
    background: var(--mcenter-betinfo-table-header-bg) !important;
  }

  .table-cell {
    color: var(--mcenter-table-cell2--text);
    text-align: center !important;
  }

  .table-cell2 {
    color: var(--mcenter-table-cell2--text);
    text-align: center !important;
  }

  .el-table__empty-text {
    color: var(--mcenter-nodata);
  }
}

:deep(.el-table__footer) {
  .el-table__cell {
    background: var(--mcenter-table-footer--bg);
    
    .cell {
      color: var(--mcenter-table-cell2--text);
      text-align: center !important;
    }
  }
}
</style>