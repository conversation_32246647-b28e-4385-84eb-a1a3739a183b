import { mount } from '@vue/test-utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import SportIndex from './SportIndex.vue'

vi.mock('vue-i18n', () => ({
  useI18n: vi.fn(() => ({
    t: vi.fn((msg: string) => msg),
  })),
}));

vi.mock('@/components/betrecord/SportCompeleteTable.vue', () => ({
  default: {
    name: 'SportCompeleteTable',
    template: '<div class="mock-sport-complete-table">Mock SportCompeleteTable</div>',
  },
}))
vi.mock('@/components/betrecord/SportUncompeleteTable.vue', () => ({
  default: {
    name: 'SportUncompeleteTable',
    template: '<div class="mock-sport-uncomplete-table">Mock SportUncompeleteTable</div>',
  },
}))

describe('LotteryIndex', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders correctly when not in maintenance', async () => {
    const wrapper = mount(SportIndex)

    const vm = wrapper.vm as any

    await vm.$nextTick()
    await nextTick()

    const elRadioGroup = wrapper.findComponent({ name: 'el-radio-group' })
    const elRadios = wrapper.findAllComponents({ name: 'el-radio-button' })

    expect(elRadioGroup.exists()).toBe(true)
    expect(elRadios).toHaveLength(2)
    expect(elRadios.every(radio => radio.exists())).toBe(true)
    expect(elRadios[0].attributes('class')).toContain('is-active')
    expect(wrapper.findComponent({ name: 'SportUncompeleteTable' }).exists()).toBe(true)
    expect(wrapper.findComponent({ name: 'SportCompeleteTable' }).exists()).toBe(false)

    await elRadios[1].find('input').setValue(true)
    await vm.$nextTick()

    expect(elRadios[1].attributes('class')).toContain('is-active')
    expect(wrapper.findComponent({ name: 'SportUncompeleteTable' }).exists()).toBe(false)
    expect(wrapper.findComponent({ name: 'SportCompeleteTable' }).exists()).toBe(true)
  })

  it('renders maintenance message when LotteryUncompleteTable is in maintenance', async () => {
    const wrapper = mount(SportIndex)

    const vm = wrapper.vm as any
    const openSpy = vi.spyOn(vm, 'handleMaintain' as any)

    await vm.$forceUpdate()

    const sportUncompleteTable = wrapper.findComponent({ name: 'SportUncompeleteTable' })
    await sportUncompleteTable.vm.$emit('maintain', true, 'Maintenance in progress')

    expect(openSpy).toHaveBeenCalled()
    expect(wrapper.html()).toContain('Maintenance in progress')
  })
  
  it('renders maintenance message when LotteryCompleteTable is in maintenance', async () => {
    const wrapper = mount(SportIndex)

    const vm = wrapper.vm as any
    const openSpy = vi.spyOn(vm, 'handleMaintain' as any)

    await vm.$nextTick()
    await nextTick()
    await wrapper.findAllComponents({ name: 'el-radio-button' })[1].find('input').setValue(true)
    await vm.$nextTick()

    const sportCompeleteTable = wrapper.findComponent({ name: 'SportCompeleteTable' })
    await sportCompeleteTable.vm.$emit('maintain', true, 'Maintenance in progress')

    expect(openSpy).toHaveBeenCalled()
    expect(wrapper.html()).toContain('Maintenance in progress')
  })
})