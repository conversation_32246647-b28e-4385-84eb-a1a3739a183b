<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import SportCompeleteTable from '@/components/betrecord/SportCompeleteTable.vue'
import SportUncompeleteTable from '@/components/betrecord/SportUncompeleteTable.vue'

const { t } = useI18n()
const maintainText = ref('')
const searchVal = ref('uncompelete')
const isSportMaintain = ref(false)

const handleMaintain = (isMaintain: boolean, text: string) => {
  isSportMaintain.value = isMaintain
  maintainText.value = text
}
</script>

<template>
  <template v-if="isSportMaintain === false">
    <div class="search">
      <el-radio-group v-model="searchVal" size="large">
        <el-radio-button :label="t('S_OPEN_BETS')" value="uncompelete" />
        <el-radio-button :label="t('S_SETTLED_BETS')" value="compelete" />
      </el-radio-group>
    </div>
    <!-- 未結算注單 Table-->
    <SportUncompeleteTable v-if="searchVal === 'uncompelete'" @maintain="handleMaintain" />
    <!-- 已結算注單 Table-->
    <SportCompeleteTable v-else @maintain="handleMaintain" />
  </template>
  <template v-else>
    <div v-html="maintainText"></div>
  </template>
</template>

<style lang="scss" scoped>
:deep(.el-radio-button) {
  &.is-active {
    .el-radio-button__inner {
      background: var(--mcenter-radio-button-active-bg);
      color: var(--mcenter-radio-button-active-text);
    }
  }

  .el-radio-button__inner {
    background: var(--mcenter-radio-button-bg);
    color: var(--mcenter-radio-button-text);
  }
}
</style>