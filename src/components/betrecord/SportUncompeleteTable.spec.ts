import { mount } from '@vue/test-utils'
import { describe, beforeEach, expect, it, vi } from 'vitest'
import { getSportUncomplete } from '@/api/betrecord'
import SportUncompeleteTable from './SportUncompeleteTable.vue'

vi.mock('vue-i18n', () => ({
  useI18n: vi.fn(() => ({
    t: vi.fn((msg: string) => msg),
  })),
}));

vi.mock('@/components/betrecord/SportUncompleteDialog.vue', () => ({
  default: {
    name: 'SportUncompleteDialog',
    props: ['modelValue', 'title', 'ifrUrl'],
    template: '<div class="mock-sport-uncomplete-dialog">Mock SportUncompleteDialog</div>',
  },
}))

vi.mock('@/api/betrecord', () => ({
  getSportUncomplete: vi.fn(),
}))

const mockSportUncompeleteData = {
  statisList: [{
    id: 174,
    name: "使命召喚",
    betAmount: "100.00",
    count: 1
  }],
  total: {
    count: 1,
    betAmount: '10.00'
  },
  maintain: false,
  maintainInfo: ""
}

describe('SportUncompeleteTable', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders correctly with default props', async () => {
    vi.mocked(getSportUncomplete).mockResolvedValue(mockSportUncompeleteData)

    const wrapper = mount(SportUncompeleteTable)
    const vm = wrapper.vm as any

    await vm.$nextTick()
    await nextTick()

    const sportTable = wrapper.findComponent({ name: "el-table" })
    const sportTableHeaderCells = sportTable.findAll('thead .cell')
    const sportTableBodyText = ['使命召喚', '1', '100.00']
    const sportTableBodyCells = sportTable.findAll('tbody .cell')
    const sportTableFootText = ['S_TOTAL', '1', '100']
    const sportTableFootCells = sportTable.findAll('tfoot .cell')

    expect(sportTableHeaderCells).toHaveLength(3)
    expect(sportTableBodyCells.every((cell, index) => cell.text() === sportTableBodyText[index] )).toBe(true)
    expect(sportTableFootCells.every((cell, index) => cell.text() === sportTableFootText[index] )).toBe(true)
    expect(wrapper.findComponent({ name: 'SportUncompleteDialog' }).exists()).toBe(true)
  })

  it('calls the openDialog on click the game-name span', async () => {
    vi.mocked(getSportUncomplete).mockResolvedValue(mockSportUncompeleteData)

    const wrapper = mount(SportUncompeleteTable)

    const vm = wrapper.vm as any
    const openDialogSpy = vi.spyOn(vm, 'openDialog')

    await vm.$nextTick()
    await nextTick()

    const sportTable = wrapper.findComponent({ name: "el-table" })
    const gameNameEl = sportTable.find('tbody .cell .game-name')

    await gameNameEl.trigger('click')

    expect(openDialogSpy).toHaveBeenCalledWith({
      id: 174,
      name: "使命召喚",
      betAmount: "100.00",
      count: 1
    })
  })
})
