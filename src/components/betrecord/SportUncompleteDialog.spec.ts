import { mount } from '@vue/test-utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import {
  getSportUnCompleteDetail,
  getSportCompleteContent,
} from '@/api/betrecord'
import SportUncompleteDialog from './SportUncompleteDialog.vue'
import { ElMessage } from 'element-plus'

vi.mock('vue-i18n', () => ({
  useI18n: vi.fn(() => ({
    t: vi.fn((msg: string) => msg),
  })),
}));

vi.mock('@/api/betrecord', () => ({
  getSportUnCompleteDetail: vi.fn(),
  getSportCompleteContent: vi.fn(),
}))

vi.mock('@/components/mcenter/PaginationComponent.vue', () => ({
  default: {
    name: 'PaginationComponent',
    props: ['currentPage', 'totalPage'],
    template: '<div class="mock-pagination-component">Mock PaginationComponent</div>',
  },
}))

const mockUncompleteDetail = () => ({
  gameList: [
    {
      id: 187,
      name: '越野摩托車'
    }
  ],
  wagers: [
    {
      wagersId: 4550916018,
      sportName: '越野摩托車',
      addDate: '2024-10-20 23:46:00',
      oddType: '歐洲盤',
      betState: '未結算',
      betAmount: '10.00'
    }
  ],
  total: {
    count: 1,
    betAmount: '10.00'
  },
  maintain: false,
  maintainInfo: ''
})
const mockCompleteContent = () => ({
  content: `
    使命召唤 - 世界 - 決勝時刻联赛 - 冠军
    <p style=""></p>
    <p>Call of Duty League 2025. Stage 1 Minor - </p>
    <p>
      <span style="color:red;">晋级至决赛</span> - 
      <span style="color:red;">LA Thieves</span> @ 
      <span style="color:red;">1.9</span>
    </p>
    2025-02-02 11:00
  `,
  maintain: false,
  maintainInfo: ""
})

describe('SportUncompleteDialog', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders correctly when not in maintenance', async () => {
    vi.mocked(getSportUnCompleteDetail).mockResolvedValue(mockUncompleteDetail())
    vi.mocked(getSportCompleteContent).mockResolvedValue(mockCompleteContent())
    
    const wrapper = mount(SportUncompleteDialog, {
      props: {
        modelValue: false,
        gameItem: {
          name: "越野摩托車",
          id: 187,
          totalCount: 1,
          totalAmount: "100.00"
        }
      }
    })

    await wrapper.setProps({ modelValue: true })
    await nextTick()

    const gameListSelect = wrapper.findComponent({ name: 'el-select' })
    const gameListSelectOptions = gameListSelect.findAllComponents({ name: 'el-option' })
    const gameListSelectOptionsText = ['越野摩托車']
    const elTables = wrapper.findAllComponents({ name: 'el-table' })
    const topTable = elTables[0]
    const topTableHeaderCells = topTable.findAll('thead .cell')
    const topTableBodyText = ['S_TOTAL', '10.00']
    const topTableBodyCells = topTable.findAll('tbody .cell')
    const detailTable = elTables[1]
    const detailTableHeaderCells = detailTable.findAll('thead .cell')
    const detailTableBodyText = [
      ...['2024-10-20 23:46:00', '4550916018', '越野摩托車', 'S_PM_VIEW', '歐洲盤', '10.00', '未結算']
    ]
    const detailTableBodyCells = detailTable.findAll('tbody .cell')
    const paginationComponent = wrapper.findComponent({ name: 'PaginationComponent' })

    expect(gameListSelect.text()).toBe('越野摩托車')
    expect(gameListSelectOptions.every((option, index) => option.text() === gameListSelectOptionsText[index])).toBe(true)
    expect(topTableHeaderCells).toHaveLength(2)
    expect(topTableBodyCells.every((cell, index) => cell.text() === topTableBodyText[index] )).toBe(true)
    expect(detailTableHeaderCells).toHaveLength(7)
    expect(detailTableBodyCells.every((cell, index) => cell.text() === detailTableBodyText[index] )).toBe(true)
    expect(paginationComponent.exists()).toBe(true)

    await detailTableBodyCells[3].find('.check-btn').trigger('click')

    expect(detailTableBodyCells[3].text()).toBe(`使命召唤 - 世界 - 決勝時刻联赛 - 冠军
    
    Call of Duty League 2025. Stage 1 Minor - 
    
      晋级至决赛 - 
      LA Thieves @ 
      1.9
    
    2025-02-02 11:00`)
  })

  it('renders maintenance message when fetchs getSportUncompleteDetail in maintenance', async () => {
    vi.mocked(getSportUnCompleteDetail).mockResolvedValue({
      maintain: true,
      maintainInfo: 'Maintenance in progress',
    } as any)
    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())
    
    const wrapper = mount(SportUncompleteDialog, {
      props: {
        modelValue: false,
        gameItem: {
          name: "使命召唤",
          id: "174",
          totalCount: 1,
          totalAmount: "100.00"
        }
      }
    })

    await wrapper.setProps({ modelValue: true })
    await nextTick()

    expect(ElMessage.error).toHaveBeenCalledWith({
      dangerouslyUseHTMLString: true,
      message: "Maintenance in progress",
    })
    expect(wrapper.emitted('update:modelValue')).toEqual([[false]])
  })

  it('renders maintenance message when fetchs getSportCompleteContent in maintenance', async () => {
    vi.mocked(getSportUnCompleteDetail).mockResolvedValue(mockUncompleteDetail())
    vi.mocked(getSportCompleteContent).mockResolvedValue({
      maintain: true,
      maintainInfo: 'Maintenance in progress',
    } as any)
    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())
    
    const wrapper = mount(SportUncompleteDialog, {
      props: {
        modelValue: false,
        gameItem: {
          name: "使命召唤",
          id: "174",
          totalCount: 1,
          totalAmount: "100.00"
        }
      }
    })

    await wrapper.setProps({ modelValue: true })
    await nextTick()

    const detailTable = wrapper.findAllComponents({ name: 'el-table' })[1]
    const contentCell = detailTable.findAll('tbody .cell')[3]

    await contentCell.find('.check-btn').trigger('click')

    expect(ElMessage.error).toHaveBeenCalledWith({
      dangerouslyUseHTMLString: true,
      message: "Maintenance in progress",
    })
    expect(wrapper.emitted('update:modelValue')).toEqual([[false]])
  })

  it('shows error message when fetchs getSportUncompleteDetail fails', async () => {
    const errorMessage = 'Failed to fetch data'
    vi.mocked(getSportUnCompleteDetail).mockRejectedValue(new Error(errorMessage))
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())

    const wrapper = mount(SportUncompleteDialog, {
      props: {
        modelValue: false,
        gameItem: {
          name: "使命召唤",
          id: "174",
          totalCount: 1,
          totalAmount: "100.00"
        }
      }
    })

    await wrapper.setProps({ modelValue: true })

    expect(console.error).toHaveBeenCalledWith(new Error(errorMessage))
    expect(ElMessage.error).toHaveBeenCalledWith(errorMessage)
  })

  it('shows error message when fetchs getSportCompleteContent fails', async () => {
    const errorMessage = 'Failed to fetch data'
    vi.mocked(getSportUnCompleteDetail).mockResolvedValue(mockUncompleteDetail())
    vi.mocked(getSportCompleteContent).mockRejectedValue(new Error(errorMessage))
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())

    const wrapper = mount(SportUncompleteDialog, {
      props: {
        modelValue: false,
        gameItem: {
          name: "使命召唤",
          id: "174",
          totalCount: 1,
          totalAmount: "100.00"
        }
      }
    })

    await wrapper.setProps({ modelValue: true })
    await nextTick()

    const detailTable = wrapper.findAllComponents({ name: 'el-table' })[1]
    const contentCell = detailTable.findAll('tbody .cell')[3]

    await contentCell.find('.check-btn').trigger('click')

    expect(console.error).toHaveBeenCalledWith(new Error(errorMessage))
    expect(ElMessage.error).toHaveBeenCalledWith(errorMessage)
  })

  it('fetchs detail data on game change', async () => {
    vi.mocked(getSportUnCompleteDetail).mockResolvedValue(mockUncompleteDetail())
    
    const wrapper = mount(SportUncompleteDialog, {
      props: {
        modelValue: false,
        gameItem: {
          name: "使命召唤",
          id: "174",
          totalCount: 1,
          totalAmount: "100.00"
        }
      }
    })
    const vm = wrapper.vm as any
    const changeDialogSelectSpy = vi.spyOn(vm, 'getUnDialogDetail')

    await wrapper.setProps({ modelValue: true })
    await nextTick()

    const gameListSelect = wrapper.findComponent({ name: 'el-select' })

    await gameListSelect.vm.$emit('change')

    expect(changeDialogSelectSpy).toHaveBeenCalled()
  })

  it('fetchs detail data on current page change', async () => {
    vi.mocked(getSportUnCompleteDetail).mockResolvedValue(mockUncompleteDetail())
    
    const wrapper = mount(SportUncompleteDialog, {
      props: {
        modelValue: false,
        gameItem: {
          name: "使命召唤",
          id: "174",
          totalCount: 1,
          totalAmount: "100.00"
        }
      }
    })
    const vm = wrapper.vm as any
    const handleChangePageSpy = vi.spyOn(vm, 'handleChangePage')

    await wrapper.setProps({ modelValue: true })
    await nextTick()

    const paginationComponent = wrapper.findComponent({ name: 'PaginationComponent' })

    await paginationComponent.vm.$emit('currentChange')

    expect(handleChangePageSpy).toHaveBeenCalled()
  })
})
