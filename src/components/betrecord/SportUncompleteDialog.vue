<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import {
  getSportUnCompleteDetail,
  getSportCompleteContent,
} from '@/api/betrecord'
import { ISportCompleteContent, ISportUnCompleteDetail } from '@/types/betrecord'
import PaginationComponent from '@/components/mcenter/PaginationComponent.vue'
import { useCookies } from 'vue3-cookies'

const visible = defineModel({
  type: Boolean,
  default: false,
  required: true
})

const props = defineProps({
  gameItem: {
    type: Object,
    default: () => ({}),
    required: true
  }
})

const { t } = useI18n()
const { cookies } = useCookies()
const isLoading = ref(true)
const title = ref('')
const game: Ref<number | undefined> = ref(undefined)
const tableData = ref<{
  addDate: string
  wagersId: number
  sportName: string
  oddType: string
  betState: string
  betAmount: string
  content?: string
}[]>([])
const gameList = ref<{id: number, name: string}[]>([])
const topData = ref([
  {
    sum: t('S_TOTAL'),
    betAmount: '--',
  }
])
const isCellLoading = ref<{[key: string]: boolean}>({})
const currPage = ref(1)
const pageCount = computed(() => {
  return Math.ceil(tableData.value.length / 5)
})

const removeDuplicatesById = (arr: {id: number, name: string}[]) => {
  return arr.reduce((unique: any[], item: { id: any }) => {
    if (!unique.some(obj => obj.id === item.id)) {
      unique.push(item);
    }
    return unique;
  }, []);
}

const getUnDialogDetail = async () => {
  const selectGame = gameList.value.find(item => item.id === Number(game.value))

  isLoading.value = true

  if (selectGame) title.value = selectGame.name

  try {
    const params = {
      session_id: cookies.get('SESSION_ID'),
      lang: cookies.get('lang'),
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
      game_id: Number(game.value)
    }

    const res = await getSportUnCompleteDetail(params)
    
    if (res.maintain) {
      ElMessage.error({
        dangerouslyUseHTMLString: true,
        message: res.maintainInfo,
      })
      visible.value = false
    } else {
      const data = res as ISportUnCompleteDetail
      tableData.value = data.wagers
      gameList.value = removeDuplicatesById(data.gameList)
      topData.value[0].betAmount = data.total.betAmount
    }
  } catch(error) {
    console.error(error)
    ElMessage.error((error as Error).message)
  } finally {
    isLoading.value = false
    if (tableData.value.length >= 5) {
      currPage.value = 1
      handleChangePage()
    }
  }
}

const getContent = async (wagersId: number) => {
  isCellLoading.value[wagersId] = true
 
  try {
    const params = {
      session_id: cookies.get('SESSION_ID'),
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
      lang: cookies.get('lang'),
      wagers_id: wagersId
    }

    const res = await getSportCompleteContent(params)

    if (res.maintain) {
      ElMessage.error({
        dangerouslyUseHTMLString: true,
        message: res.maintainInfo,
      })
      visible.value = false
    } else {
      const data = res as ISportCompleteContent
      const wagersItem = tableData.value.find((item) => item.wagersId == wagersId )
    
      if (wagersItem) {
        wagersItem.content = data.content
      }
    }
  } catch(error) {
    console.error(error)
    ElMessage.error((error as Error).message)
  } finally {
    isCellLoading.value[wagersId] = false
  }  
}

watch(() => visible.value, (newValue) => {
  if (newValue) {
    title.value = props.gameItem.name
    game.value = props.gameItem.id
    getUnDialogDetail()
  }
})

const handleChangePage = () => {
  tableData.value = tableData.value.slice((currPage.value - 1) * 5, (currPage.value * 5) - 1)
}

</script>

<template>
<el-dialog v-model="visible" :title="title" width="800">
  <div class="select-group">
    <el-select
      v-model="game"
      :placeholder="t('S_ENTER_GAME_NAME')"
      @change="getUnDialogDetail"
      v-loading="gameList.length < 1"
    >
      <el-option
        v-for="item in gameList"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      />
    </el-select>
  </div>

  <el-table 
    :data="topData"
    :border="true"
    header-cell-class-name="table-header"
    cell-class-name="table-cell"
    v-loading="isLoading"
  >
    <el-table-column prop="sum" label="" />
    <el-table-column prop="betAmount" :label="t('S_WAGER_AMOUNT')" />
  </el-table>
  <br/>
  <el-table 
    :data="tableData"
    header-cell-class-name="table-header2"
    cell-class-name="table-cell2"
    :empty-text="t('S_NODATA')"
    v-loading="isLoading"
  >
    <el-table-column prop="addDate" :label="t('S_WAGER_DATE')" />
    <el-table-column prop="wagersId" :label="t('S_BET_NUM')" />
    <el-table-column prop="sportName" :label="t('S_GAMETYPE2')" />
    <el-table-column prop="" :label="t('S_CONTENT_MSG')" width="300">
      <template #default="scope">
        <div v-if="scope.row.content" v-html="scope.row.content"></div>
        <div v-else-if="isCellLoading[scope.row.wagerId]" >
            <el-skeleton :rows="1" animated />
          </div>
        <span v-else @click="getContent(scope.row.wagersId)" class="check-btn">
          {{ t('S_PM_VIEW') }}
        </span>
      </template>
    </el-table-column>
    <el-table-column prop="oddType" :label="t('S_ODDS_TYPE')" />
    <el-table-column prop="betAmount" :label="t('S_WAGER_AMOUNT')" />
    <el-table-column prop="betState" :label="t('S_RB_STATUS')" />
  </el-table>
  <PaginationComponent
    v-if="tableData.length > 0"
    v-model:current-page="currPage"
    :page-count="pageCount"
    @current-change="handleChangePage()"
  />
</el-dialog>
</template>

<style lang="scss" scoped>
.check-btn {
  cursor: pointer;
  color: gray;
}

.el-select {
  width: 30%;
  margin: 10px;
}

:deep(.el-table__inner-wrapper){
  .table-header {
    color: var(--mcenter-collapse-item-text);
    font-size: 15px;
    text-align: center !important;
    background: #eee !important;
  }

  .table-header2 {
    color: var(--mcenter-table-header2--text);
    font-size: 15px;
    text-align: center !important;
    background: var(--mcenter-betinfo-table-header-bg) !important;
  }

  .table-cell {
    color: var(--mcenter-table-cell2--text);
    text-align: center !important;
  }

  .table-cell2 {
    color: var(--mcenter-table-cell2--text);
    text-align: center !important;
  }

  .el-table__empty-text {
    color: var(--mcenter-nodata);
  }
}

:deep(.el-table__footer) {
  .el-table__cell {
    background: var(--mcenter-table-footer--bg);
    
    .cell {
      color: var(--mcenter-table-cell2--text);
      text-align: center !important;
    }
  }
}
</style>