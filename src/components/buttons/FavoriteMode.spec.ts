import { mount } from '@vue/test-utils';
import { describe, it, expect } from 'vitest';
import FavoriteMode from './FavoriteMode.vue';
import { Star } from '@icon-park/vue-next'

describe('FavoriteMode', () => {
  it('render_favorite_true', () => {
    const wrapper = mount(FavoriteMode, {
      props: {
        modelValue: true
      },
    });

    // 檢查填充顏色是否為 var(--icon-bg-active)
    const star = wrapper.findComponent(Star);
    expect(star.props('fill')).toBe('var(--icon-bg-active)');
  });

  it('render_favorite_false', () => {
    const wrapper = mount(FavoriteMode, {
      props: {
        modelValue: false,
      },
    });

    // 檢查填充顏色是否為 var(--icon-bg)
    const star = wrapper.findComponent(Star);
    expect(star.props('fill')).toBe('var(--icon-bg)');
  });

  it('click_star', async () => {
    const wrapper = mount(FavoriteMode, {
      props: {
        modelValue: false
      },
    });

    const star = wrapper.findComponent(Star);
    await star.trigger('click');

    expect(wrapper.emitted()['update:modelValue'][0]).toEqual([true]);
  });
});
