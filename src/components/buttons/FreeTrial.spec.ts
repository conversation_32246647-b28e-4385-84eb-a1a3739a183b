import { mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import FreeTrial from '@/components/buttons/FreeTrial.vue'

const i18n = createI18n({
  locale: 'en',
  messages: {
    en: {
      S_FREE_TRIAL: 'Free Trial'
    }
  }
})

describe('FreeTrial', () => {
  it('renders correctly with default props', () => {
    const wrapper = mount(FreeTrial, {
      props: {
        isList: false,
        demoLink: '/demoLink'
      },
      global: {
        plugins: [i18n]
      }
    })

    expect(wrapper.text()).toContain('Free Trial')

    const button = wrapper.findComponent({ name: 'el-button' })
    expect(button.classes()).not.toContain('free-btn')
    expect(button.props('size')).toBeUndefined()
    expect(button.props('plain')).toBe(false)
  })

  it('renders correctly when isList is true', async () => {
    const wrapper = mount(FreeTrial, {
      props: {
        isList: true,
        demoLink: '/demoLink'
      },
      global: {
        plugins: [i18n]
      }
    })

    expect(wrapper.text()).toContain('Free Trial')

    const button = wrapper.findComponent({ name: 'el-button' })
    expect(button.classes()).toContain('free-btn')
    expect(button.props('size')).toBe('small')
    expect(button.props('plain')).toBe(true)
  })

  it('opens the demo link in a new window', async () => {
    vi.spyOn(window, 'open').mockImplementation(vi.fn())

    const wrapper = mount(FreeTrial, {
      props: {
        isList: false,
        demoLink: '/demoLink'
      },
      global: {
        plugins: [i18n]
      }
    })

    const button = wrapper.findComponent({ name: 'el-button' })
    await button.trigger('click')
    expect(window.open).toHaveBeenCalledWith('/demoLink', '', 'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no')
  })
})
