<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
  isList: {
    type: Boolean,
    required: true,
    default: false
  },
  demoLink: {
    type: String,
    required: true,
    default: ''
  }
})

const openFreeGame = () => {
  window.open(props.demoLink, '', 'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no')
}
</script>

<template>
  <el-button
    color="var(--button-color)"
    :class="props.isList ? 'free-btn' : ''"
    :size="props.isList ? 'small' : undefined"
    :plain="props.isList ? true : false"
    @click="openFreeGame"
  >
    {{ t('S_FREE_TRIAL') }}
  </el-button>
</template>
