import { mount } from '@vue/test-utils'
import { describe, expect, it } from 'vitest'
import { createI18n } from 'vue-i18n'
import GameEnter from '@/components/buttons/GameEnter.vue'

const i18n = createI18n({
  locale: 'en',
  messages: {
    en: {
      S_GAME_ENTER: 'Game Enter'
    }
  }
})

describe('GameEnter', () => {
  it('renders correctly with default props', () => {
    const wrapper = mount(GameEnter, {
      props: {
        isList: false
      },
      global: {
        plugins: [i18n]
      }
    })

    expect(wrapper.text()).toContain('Game Enter')

    const button = wrapper.findComponent({ name: 'el-button' })
    expect(button.classes()).not.toContain('enter-btn')
    expect(button.props('size')).toBeUndefined()
    expect(button.props('plain')).toBe(false)
  })

  it('renders correctly when isList is true', async () => {
    const wrapper = mount(GameEnter, {
      props: {
        isList: true
      },
      global: {
        plugins: [i18n]
      }
    })

    expect(wrapper.text()).toContain('Game Enter')

    const button = wrapper.findComponent({ name: 'el-button' })
    expect(button.classes()).toContain('enter-btn')
    expect(button.props('size')).toBe('small')
    expect(button.props('plain')).toBe(true)
  })
})
