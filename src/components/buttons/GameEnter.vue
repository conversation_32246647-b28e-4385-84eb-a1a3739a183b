<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
  isList: {
    type: Boolean,
    required: true,
    default: false
  }
})
</script>

<template>
  <el-button
    color="var(--button-color)"
    :class="props.isList ? 'enter-btn' : ''"
    :size="props.isList ? 'small' : undefined"
    :plain="props.isList ? true : false"
  >
    {{ t('S_GAME_ENTER') }}
  </el-button>
</template>
