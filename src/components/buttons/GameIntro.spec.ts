import { mount } from '@vue/test-utils'
import { describe, it, expect, vi } from 'vitest'
import { createRouter, createWebHistory } from 'vue-router'
import { createI18n } from 'vue-i18n'
import GameIntro from './GameIntro.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', name: 'home', component: { template: '<div>Home</div>' } },
    { path: '/intro', name: 'intro', component: { template: '<div>Intro</div>' } }
  ]
})

const i18n = createI18n({
  locale: 'en',
  messages: {
    en: {
      S_GAME_INTRO: 'Game Intro'
    }
  }
})

describe('GameIntro', () => {
  it('valid_button_text', () => {
    const wrapper = mount(GameIntro, {
      global: {
        plugins: [router, i18n],
      },
      props: {
        gameId: '123',
        currentPage: 'fishing',
        isList: false,
      }
    })

    const button = wrapper.find('.intro-btn')
    expect(button.exists()).toBe(true)
    expect(button.text()).toBe('Game Intro')
  })

  it('valid_button_click', async () => {
    vi.spyOn(router, 'push').mockImplementation(vi.fn())

    const wrapper = mount(GameIntro, {
      global: {
        plugins: [router, i18n],
      },
      props: {
        gameId: '123',
        currentPage: 'fishing',
        isList: false,
      }
    })

    const button = wrapper.find('.intro-btn')
    await button.trigger('click')

    expect(router.push).toHaveBeenCalledWith({ query: { to: 'intro', id: '123' } })
  })

  it('valid_isList_equal_true', () => {
    const wrapper = mount(GameIntro, {
      global: {
        plugins: [router, i18n],
      },
      props: {
        gameId: '123',
        currentPage: 'other',
        isList: true,
      }
    })

    const button = wrapper.find('.intro-btn')
    expect(button.exists()).toBe(true)
    expect(button.attributes('class')).contain('small')
    expect(button.attributes('class')).contain('plain')
  })
})
