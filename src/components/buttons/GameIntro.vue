<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'

const props = defineProps({
  gameId: {
    type: String,
    default: '',
    required: true
  },
  currentPage: {
    type: String,
    default: '',
    required: true
  },
  isList: {
    type: Boolean,
    default: false,
    required: false
  }
})

const router = useRouter()
const { t } = useI18n()

const handleShowInrto = (gameid: string) => {
  router.push({query: { to: 'intro', id: gameid}})
}
</script>

<template>
  <el-button
    :class="['intro-btn', props.currentPage]"
    type="info"
    :size="props.isList ? 'small' : undefined"
    :plain="props.isList"
    @click="handleShowInrto(props.gameId)"
  >
    {{ t('S_GAME_INTRO') }}
  </el-button>
</template>

<style lang="scss" scoped>
.intro-btn {
  display: none;

  &.fishing {
    display: block;
  }

  @media (max-width: 800px) {
    display: none;
  }
}
</style>