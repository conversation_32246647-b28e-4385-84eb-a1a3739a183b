import { mount } from '@vue/test-utils';
import { describe, it, expect } from 'vitest';
import { AllApplication, HamburgerButton } from '@icon-park/vue-next'
import ListMode from './ListMode.vue';

describe('ListMode', () => {
  it('render_listmode_true', () => {
    const wrapper = mount(ListMode, {
      props: {
        modelValue: true,
      },
    });

    expect(wrapper.findComponent(AllApplication).exists()).toBe(true);
    expect(wrapper.findComponent(HamburgerButton).exists()).toBe(false);
  });

  it('render_listmode_false', () => {
    const wrapper = mount(ListMode, {
      props: {
        modelValue: false,
      },
    });

    expect(wrapper.findComponent(AllApplication).exists()).toBe(false);
    expect(wrapper.findComponent(HamburgerButton).exists()).toBe(true);
  });

  it('click_allapplication', async () => {
    const wrapper = mount(ListMode, {
      props: {
        modelValue: true,
      },
    });

    await wrapper.findComponent(AllApplication).trigger('click');

    expect(wrapper.emitted()['update:modelValue'][0]).toEqual([false]);
  });

  it('click_hamburgerbutton', async () => {
    const wrapper = mount(ListMode, {
      props: {
        modelValue: false,
      },
    });

    await wrapper.findComponent(HamburgerButton).trigger('click');

    expect(wrapper.emitted()['update:modelValue'][0]).toEqual([true]);
  });
});
