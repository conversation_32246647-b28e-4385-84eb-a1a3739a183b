<script setup lang="ts">
import { AllApplication, HamburgerButton } from '@icon-park/vue-next'

const show = defineModel({
  type: Boolean,
  default: false,
  required: true
})
</script>

<template>
  <all-application
    v-if="show"
    theme="filled"
    size="25"
    fill="var(--icon-bg)"
    @click="show = !show"
  />
  <hamburger-button
    v-else
    theme="filled"
    size="25"
    fill="var(--icon-bg)"
    @click="show = !show"
  />
</template>
