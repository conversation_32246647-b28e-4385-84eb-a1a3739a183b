import { mount } from '@vue/test-utils'
import { describe, expect, it, vi, beforeEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import MemberCenter from './MemberCenter.vue'

vi.mock('vue-i18n', () => ({
  useI18n: vi.fn(() => ({
    locale: ref('en-us'),
    t: vi.fn((msg: string) => msg),
  })),
}));

Object.defineProperty(window, 'focus', {
  value: vi.fn(),
  writable: true
});

describe('MemberCenter', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('clicked', () => {
    vi.spyOn(window, 'open').mockImplementation(() => window)
    const wrapper = mount(MemberCenter)

    wrapper.find('.to_mcenter').trigger('click')

    expect(window.open).toHaveBeenCalledWith('/mcenter', 'MACENTER', 'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no')
  })
})