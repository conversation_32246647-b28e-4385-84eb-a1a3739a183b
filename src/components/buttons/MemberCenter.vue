<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
  windowConfig: {
    type: String,
    default: 'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no',
    required: false
  }
})

const openMcenter = () => {
  window.open('/mcenter', 'MACENTER', props.windowConfig)?.focus()
}
</script>

<template>
  <span class="to_mcenter" @click="openMcenter()">{{ t('S_MEM_CENTER') }}</span>
</template>
