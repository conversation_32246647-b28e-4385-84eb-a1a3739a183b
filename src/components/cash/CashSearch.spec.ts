import { mount, VueWrapper } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'
import CashSearch from './CashSearch.vue'
import { ElMessage } from 'element-plus'
import { useLobbySwitchStore } from '@/stores/lobbySwitch'

vi.mock('vue-i18n', () => ({
  useI18n: vi.fn(() => ({
    t: vi.fn((msg: string) => msg),
  })),
}));

vi.mock('@/stores/lobbySwitch', () => ({
  useLobbySwitchStore: vi.fn()
}))

vi.mocked(useLobbySwitchStore).mockReturnValue({
  cashSelectList: ref([
    {
      id: 0,
      name: '全部',
      disabled: false
    },
    {
      id: 3,
      name: 'BB 視訊',
      disabled: false
    },
    {
      id: 31,
      name: 'New BB体育',
      disabled: false
    }
  ])
} as any)

const propsData = {
  dateVal: ['2025-03-09', '2025-03-09'],
  gameKindVal: 0,
  typeVal: 'ALL',
  maintainInfo: [{ gameKind: 3, startTime: '', endTime: '', message: '' }]
}

vi.mock('@/utils/date', () => ({
  getDayBeforeRange: vi.fn(() => ['2025-03-02', '2025-03-09']),
  getTodayRange: vi.fn(() => ['2025-03-09', '2025-03-09']),
  getYesterdayRange: vi.fn(() => ['2025-03-08', '2025-03-09']),
}));

describe('CashSearch', () => {
  it('renders correctly with default props', async () => {
    const wrapper = mount(CashSearch, {
      props: { ...propsData }
    })

    const vm = wrapper.vm as any

    await vm.$nextTick()
    await nextTick()

    const gameKindSelect = wrapper.findComponent({ name: 'el-select' })
    const gameKindOptions = gameKindSelect.findAllComponents({ name: 'el-option' })
    const gameKindOptionsText = ['全部', 'BB 視訊 (S_MAINTAINING)', 'New BB体育']
    const datePicker = wrapper.findComponent({ name: 'el-date-picker' })
    const datePickerInputs = datePicker.findAll('input')
    const quickButtons = wrapper.find('.quick-selector').findAllComponents({ name: 'el-button' }) as VueWrapper<any, any>[]
    const searchButton = wrapper.find('.search-btn').findComponent({ name: 'el-button' })

    expect(gameKindSelect.text()).toBe('全部')
    expect(gameKindOptions.every((option, index) => option.text() === gameKindOptionsText[index])).toBe(true)
    expect(datePickerInputs[0].element.value).toBe('2025-03-09')
    expect(datePickerInputs[1].element.value).toBe('2025-03-09')
    expect(quickButtons).toHaveLength(3)
    expect(searchButton.exists()).toBe(true)

    await wrapper.setProps({ gameKindVal: 31 })

    const typeSelect = wrapper.findAllComponents({ name: 'el-select' })[1]
    const typeOptions = typeSelect.findAllComponents({ name: 'el-option' })

    expect(typeOptions).toHaveLength(4)
    await quickButtons[2].trigger('click')

    expect(datePickerInputs[0].element.value).toBe('2025-03-02')
    expect(datePickerInputs[1].element.value).toBe('2025-03-09')
  })

  it('emits search event when the search button is clicked', async () => {
    const wrapper = mount(CashSearch, {
      props: { ...propsData }
    })

    const vm = wrapper.vm as any

    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())

    await vm.$nextTick()
    await nextTick()

    const searchButton = wrapper.find('.search-btn').findComponent({ name: 'el-button' })

    await searchButton.trigger('click')

    expect(wrapper.emitted('search')).toBeTruthy()
  })
})
