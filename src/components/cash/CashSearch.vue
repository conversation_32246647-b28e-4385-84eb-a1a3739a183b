<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { getDayBeforeRange, getTodayRange, getYesterdayRange } from '@/utils/date'
import { useLobbySwitchStore } from '@/stores/lobbySwitch'
import { storeToRefs } from 'pinia'
import type { IMaintainInfoData } from '@/types/maintain'

const { t } = useI18n()
const lobbySwitchStore = useLobbySwitchStore()
const { cashSelectList } = storeToRefs(lobbySwitchStore)

const props = defineProps({
  maintainInfo: {
    type: Array as PropType<IMaintainInfoData[]>,
    default: () => ([]),
    required: true
  }
})

const dateVal = defineModel('dateVal', {
  type: Array as PropType<any[]>,
  default: getTodayRange()
})

const gameKindVal = defineModel('gameKindVal', {
  type: Number,
  default: 0
})

const typeVal = defineModel('typeVal', {
  type: String,
  default: 'ALL'
})

const quickOption = ref('today')
const btnOptions = ref([
  {
    item: 'today',
    txt: t('S_TODDAY')
  },
  {
    item: 'yesterday',
    txt: t('S_YESTERDAY')
  },
  {
    item: 'week',
    txt: t('S_NEARLY_DAY').replace('%s', '7')
  }
])
const typeItems = ref({
  ALL: t('S_ALL'),
  BETTING: t('ras'),
  PAYOFF: t('S_PAYOFF_PAYOFF'),
  CANCEL: t('S_CANCEL_CANCEL')
})

const emit = defineEmits(['search'])

const gameKindItems = computed(() => {
  let result = cashSelectList.value.slice()
  
  props.maintainInfo.forEach(info => {
    let gameKindItem = result.find(item => item.id == info.gameKind)
    if (gameKindItem) gameKindItem.disabled = true
  })

  return result
})

const setDate = (option: string) => {
  quickOption.value = option

  switch (option) {
    case 'today':
      dateVal.value = getTodayRange()
      break

    case 'yesterday':
      dateVal.value = getYesterdayRange()
      break

    case 'week':
      dateVal.value = getDayBeforeRange(6)
      break
  }
}

const handleClick = () => {
  emit('search')
}

const resetActive = () => {
  quickOption.value = ''
}
</script>

<template>
  <div class="cash-search">
    <el-select v-model="gameKindVal" placeholder="Select" style="width: 120px">
      <el-option
        v-for="item in gameKindItems"
        :key="item.id"
        :label="item.name"
        :value="item.id"
        :disabled="item.disabled"
      >
        <span>{{ item.disabled ? `${item.name} (${t('S_MAINTAINING')})` : item.name }}</span>
      </el-option>
    </el-select>
    <el-select v-if="gameKindVal !== 0" v-model="typeVal" placeholder="Select" style="width: 120px">
      <el-option
        v-for="(item, key) in typeItems"
        :key="key"
        :label="item"
        :value="key"
      />
    </el-select>
    <el-date-picker
      v-model="dateVal"
      type="daterange"
      unlink-panels
      range-separator="To"
      :start-placeholder="t('S_START_TIME')"
      :end-placeholder="t('S_END_TIME')"
      value-format="YYYY-MM-DD"
      @change="resetActive"
    />
    <div class="quick-selector">
      <el-button-group v-for="(opt, key) in btnOptions" :key="key">
        <el-button
          :color="quickOption === opt.item ? 'var(--mcenter-effectivebetting-button-bg)' : ''"
          @click="setDate(opt.item)"
        >
          {{ opt.txt }}
        </el-button>
      </el-button-group>
    </div>
    <div class="search-btn">
      <el-button color="var(--mcenter-effectivebetting-button-bg)" @click="handleClick">{{ t('S_SEARCH') }}</el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.cash-search {
  display: flex;
  flex-wrap: wrap;
  padding: 15px;

  .el-select {
    margin: 0 5px;
  }

  .quick-selector {
    margin: 0 20px;

    .el-button {
      &:hover {
        color: var(--mcenter-cash-cash-quick-selector-button-hover-text);
      }
    }
  }

  :deep(.el-button) {
    &:hover {
      background: var(--mcenter-effectivebetting-button-bg);
      border-color: var(--mcenter-effectivebetting-button-bg);
    }
    &:active {
      background: var(--mcenter-effectivebetting-button-bg);
    }
  }

  .search-btn {
    :deep(.el-button) {
      span {
        color: white;
      }
    }
  }
}
</style>
