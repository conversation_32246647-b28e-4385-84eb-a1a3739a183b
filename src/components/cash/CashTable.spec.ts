import { mount, VueWrapper } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'
import CashTable from './CashTable.vue'

vi.mock('vue-i18n', () => ({
  useI18n: vi.fn(() => ({
    t: vi.fn((msg: string) => msg),
  })),
}));

const propsData = {
  topTableData: [
    {
      sum: '总计',
      amount: '127.10',
      balance: '4146876.34'
    }
  ],
  tableData: [
    {
      amount: '-50.00',
      balance: '2010258461.68',
      createTime: '2024-11-08T01:50:31-04:00',
      opCode: 40000,
      note: {
        refId: '520000093879',
        result: 'W'
      }
    },
    {
      amount: '-50.00',
      balance: '2010258461.68',
      createTime: '2024-11-08T01:44:31-04:00',
      opCode: 40000,
      note: {
        refId: '520000093878',
        result: ''
      }
    },
    {
      amount: '-50.00',
      balance: '2010258511.68',
      createTime: '2024-11-08T01:44:13-04:00',
      opCode: 1433,
      note: {
        refId: '520000093877',
        result: 'S'
      }
    }
  ]
}

describe('CashTable', () => {
  it('renders correctly with default props', async () => {
    const wrapper = mount(CashTable, {
      props: { ...propsData }
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    const elTables = wrapper.findAllComponents({ name: "el-table" })
    const topTable = elTables[0]
    const topTableHeaderCells = topTable.findAll('thead .cell')
    const topTableBodyText = ['总计', '127.10', '4,146,876.34']
    const topTableBodyCells = topTable.findAll('tbody .cell')
    const cashTable = elTables[1]
    const cashTableHeaderCells = cashTable.findAll('thead .cell')
    const cashTableBodyText = [
      ...['2024-11-08T01:50:31-04:00', 'opcode.40000', '-50.00', '2,010,258,461.68', 'S_BETTYPE_1: 520000093879S_WIN'],
      ...['2024-11-08T01:44:31-04:00', 'opcode.40000', '-50.00', '2,010,258,461.68', 'S_BETTYPE_1: 520000093878'],
      ...['2024-11-08T01:44:13-04:00', 'opcode.1433', '-50.00', '2,010,258,511.68', 'S_SPORT_FIFA_ACTIVITY_NUM: 520000093877S_WAITING']
    ]
    const cashTableBodyCells = cashTable.findAll('tbody .cell')

    expect(topTableHeaderCells).toHaveLength(3)
    expect(topTableBodyCells.every((cell, index) => cell.text() === topTableBodyText[index] )).toBe(true)
    expect(cashTableHeaderCells).toHaveLength(5)
    expect(cashTableBodyCells.every((cell, index) => cell.text() === cashTableBodyText[index] )).toBe(true)
  })
  it('emits sort event when the sort button is clicked', async () => {
    const wrapper = mount(CashTable, {
      props: { ...propsData }
    })

    const vm = wrapper.vm as any
    const changeDataSortSpy = vi.spyOn(vm, 'ChangeDataSort')

    await wrapper.vm.$nextTick()
    await nextTick()

    const sortButtons = wrapper.find('.sort-group').findAllComponents({ name: 'el-button' }) as VueWrapper<any, any>[]
    const descButton = sortButtons[0]
    const ascButton = sortButtons[1]

    await descButton.trigger('click')

    expect(changeDataSortSpy).toHaveBeenCalledWith('desc')
    expect(wrapper.emitted('update:sortVal')?.[0]).toEqual(['desc']);
    expect(wrapper.emitted('sort')).toHaveLength(1)

    await ascButton.trigger('click')

    expect(changeDataSortSpy).toHaveBeenCalledWith('asc')
    expect(wrapper.emitted('update:sortVal')?.[1]).toEqual(['asc']);
    expect(wrapper.emitted('sort')).toHaveLength(2)
  })
})
