<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
  topTableData: {
    type: Array,
    default: () => ([]),
    required: true
  },
  tableData: {
    type: Array,
    default: () => ([]),
    required: true
  }
})

const sortVal = defineModel('sortVal', {
  type: String,
  default: 'desc'
})

const emit = defineEmits(['sort'])

const ChangeDataSort = (sort: string) => {
  sortVal.value = sort
  emit('sort')
}

const resultMapI18n = {
  "W":  t('S_WIN'),
	"D":  t('S_UNACCEPTED'),
	"S":  t('S_WAITING'),
	"L":  t('S_LOSE'),
	"0":  t('S_MN'),
	"DB": t('S_MN'),
	"LL": t('S_LOSE_WIN'),
	"LW": t('S_WIN_LOSE'),
	"C":  t('S_DEL_FUN'),
	"F":  t('S_WAGER_STATUS_F'),
	"SC": t('S_SYSTEM_CANCEL'),
	"DC": t('S_DANGEROUS_CANCEL'),
	"N":  t('S_CANCEL'),
	"RT": t('S_BC_RETURNED'),
	"CO": t('S_BC_CASH_OUT')
} as any
</script>

<template>
  <div class="cash-table">
    <div v-if="tableData.length > 0" class="table-1">
      <el-table 
        :data="props.topTableData"
        :border="true"
        header-cell-class-name="table-header"
        cell-class-name="table-cell"
        :empty-text="t('S_NO_DATA')"
      >
        <el-table-column prop="sum" label="" />
        <el-table-column prop="amount" :label="t('S_TRANSACTION_GOLD')">
          <template #default="scope">
            <span>{{ Number(scope.row.amount).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="balance" :label="t('S_CURRENTLY_EXIST_BALANCE')">
          <template #default="scope">
            <span>{{ Number(scope.row.balance).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="sort-group">
      <el-button class="sort-down" :class="{'active': sortVal === 'desc'}" @click="ChangeDataSort('desc')">
        <span></span>
      </el-button>
      <el-button class="sort-up" :class="{'active': sortVal === 'asc'}" @click="ChangeDataSort('asc')">
        <span></span>
      </el-button>
    </div>
    <div class="table-2">
      <el-table 
        :data="props.tableData"
        :border="true"
        header-cell-class-name="table-header2"
        cell-class-name="table-cell2"
        :empty-text="t('S_NO_DATA')"
      >
        <el-table-column prop="createTime" :label="t('S_TIME_FU')" width="200px"/>
        <el-table-column prop="opCode" :label="t('S_TRANSACTION_TYPE')" >
          <template #default="scope">
            <span>{{ t(`opcode.${scope.row.opCode}`) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="amount" :label="t('S_TRANSACTION_GOLD')">
          <template #default="scope">
            <span>{{ Number(scope.row.amount).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="balance" :label="t('S_CURRENTLY_EXIST_BALANCE')">
          <template #default="scope">
            <span>{{ Number(scope.row.balance).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="note" :label="t('S_REMARK')" width="200px">
          <template #default="scope">
            <span>{{ scope.row.opCode === 1433 ? t('S_SPORT_FIFA_ACTIVITY_NUM') : t('S_BETTYPE_1') }}{{ `: ${scope.row.note.refId}` }}</span>
            <br/>
            <span>{{ resultMapI18n[scope.row.note.result] }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.cash-table {
  .result-content {
    width: 100%;
    height: 500px;
    margin: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .no-data {
      margin: auto;
      display: flex;
      flex-direction: column;
      color: gray;
      span {
        margin: 5px;
      }
    }
  }

  .sort-group {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;

    .el-button {
      background-color: var(--mcenter-radio-button-bg);
      padding: 8px 10px;
    }

    .active {
      background-color: var(--mcenter-radio-button-active-bg);
    }

    .sort-up {
      :deep(span) {
        background: url('/client/site/default/image/record_sort_icon.png') 0 -40px no-repeat;
        width: 40px;
        height: 40px;
      }
    }

    .sort-down {
      :deep(span) {
        background: url('/client/site/default/image/record_sort_icon.png') 0 0 no-repeat;
        width: 40px;
        height: 40px;
      }
    }
  }

  .table-1,
  .table-2 {
    width: 100%;
    margin: 0 auto;
    margin-top: 10px;
  }

  :deep(.el-table){
    .table-header {
      color: var(--mcenter-collapse-item-text);
      font-size: 15px;
      text-align: center !important;
      background: #eee !important;
    }

    .table-header2 {
      color: var(--mcenter-table-header2--text);
      font-size: 15px;
      text-align: center !important;
      background: var(--mcenter-betinfo-table-header-bg) !important;
    }

    .table-cell {
      color: var(--mcenter-table-cell2--text);
      text-align: center !important;
    }

    .table-cell2 {
      color: var(--mcenter-table-cell2--text);
      text-align: center !important;
    }

    .el-table__empty-text {
      color: var(--mcenter-nodata);
    }
  }
}
</style>
