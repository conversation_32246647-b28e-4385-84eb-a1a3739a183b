import { mount, VueWrapper } from '@vue/test-utils'
import { describe, it, expect, vi } from 'vitest'
import EffectiveBettingSearch from './EffectiveBettingSearch.vue'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)
dayjs.extend(timezone)

vi.mock('vue-i18n', () => ({
  useI18n: vi.fn(() => ({
    t: vi.fn((msg: string) => msg),
  })),
}))

describe('EffectiveBettingSearch.vue', () => {
  let wrapper: VueWrapper<any>

  const mountComponent = (propsData: { dateVal?: string[] } = {}) => {
    return mount(EffectiveBettingSearch, {
      props: propsData
    })
  }

  it('renders correctly with initial empty date and disabled search button', () => {
    wrapper = mountComponent()
    expect(wrapper.find('.effectivebetting-search').exists()).toBe(true)

    const datePicker = wrapper.findComponent({ name: 'ElDatePicker' })
    expect(datePicker.exists()).toBe(true)
    expect(datePicker.props('modelValue')).toEqual([])
    expect(datePicker.props('valueFormat')).toBe('YYYY-MM-DD')

    const searchButton = wrapper.findComponent({ name: 'ElButton' })
    expect(searchButton.exists()).toBe(true)
    expect(searchButton.text()).toBe('S_SEARCH')
    expect(searchButton.props('disabled')).toBe(true)
  })

  it('updates internal dateVal and enables search button when ElDatePicker changes value', async () => {
    wrapper = mountComponent()
    const datePicker = wrapper.findComponent({ name: 'ElDatePicker' })
    const searchButton = wrapper.findComponent({ name: 'ElButton' })
    const newDateRange = ['2023-01-01', '2023-01-05']

    await datePicker.vm.$emit('update:modelValue', newDateRange)

    expect(wrapper.vm.dateVal).toEqual(newDateRange)
    expect(datePicker.props('modelValue')).toEqual(newDateRange)
    expect(searchButton.props('disabled')).toBe(false)
  })

  it('emits "search" event when search button is clicked', async () => {
    const initialDateRange = ['2023-10-01', '2023-10-05']
    wrapper = mountComponent({ dateVal: initialDateRange })

    const searchButton = wrapper.findComponent({ name: 'ElButton' })
    expect(searchButton.props('disabled')).toBe(false)

    await searchButton.trigger('click')

    expect(wrapper.emitted().search).toBeTruthy()
  })

  it('confirm disabledDateRange return value', () => {
    wrapper = mountComponent()

    let veryDate = dayjs.tz(new Date(), 'Etc/GMT+4').subtract(32, 'day')

    expect(wrapper.vm.disabledDateRange((veryDate))).toBe(true)

    veryDate = dayjs.tz(new Date(), 'Etc/GMT+4').add(1, 'day')

    expect(wrapper.vm.disabledDateRange(veryDate)).toBe(true)

    veryDate = dayjs.tz(new Date(), 'Etc/GMT+4').subtract(30, 'day')

    expect(wrapper.vm.disabledDateRange(veryDate)).toBe(false)
  })
})