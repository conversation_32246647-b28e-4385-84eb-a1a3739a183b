<script setup lang="ts">

import { useI18n } from 'vue-i18n'
import dayjs from 'dayjs'

const { t } = useI18n()

const dateVal = defineModel('dateVal', {
  type: Array as PropType<string[]>,
  default: () => ([])
})

const emit = defineEmits(['search'])

const disabledDateRange = (date: dayjs.Dayjs) => {
  const dateEasten = dayjs.tz(date, 'Etc/GMT+4').add(12, 'hour')
  return dateEasten < dayjs.tz(new Date(), 'Etc/GMT+4').subtract(31, 'day') || dateEasten > dayjs.tz(new Date(), 'Etc/GMT+4')
}

const search = () => {
  emit('search')
}
</script>

<template>
  <div class="effectivebetting-search">
    <div class="date-pick">
      <el-date-picker
        v-model="dateVal"
        type="daterange"
        range-separator="To"
        :start-placeholder="t('S_START_TIME')"
        :end-placeholder="t('S_END_TIME')"
        style="width: 660px"
        value-format="YYYY-MM-DD"
        :disabled-date="disabledDateRange"
      />
    </div>
    <el-button
      color="var(--mcenter-effectivebetting-button-bg)"
      size="small"
      round
      :disabled="dateVal.length === 0"
      @click="search">
      {{ t('S_SEARCH') }}
    </el-button>
  </div>
</template>

<style lang="scss" scoped>
.effectivebetting-search {
  width: 90%;
  display: flex;
  margin: 10px;

  .date-pick {
    margin-right: auto;
  }

  .el-select {
    width: 100%;
  }

  :deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
    width: 100%;
  }

  .el-button {
    padding: 15px 0;
    width: 200px;
    font-size: 1.1rem;
  }

  :deep(.el-button) {
    span {
      color: white;
    }

    &:hover {
      background: var(--mcenter-effectivebetting-button-bg);
      border-color: var(--mcenter-effectivebetting-button-bg);
    }

    &:active {
      background: var(--mcenter-effectivebetting-button-bg);
    }
  }
}
</style>
