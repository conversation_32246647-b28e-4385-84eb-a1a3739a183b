import { mount, VueWrapper } from '@vue/test-utils'
import { describe, it, expect, vi } from 'vitest'
import EffectiveBettingTable from './EffectiveBettingTable.vue'
import { IGameBetList } from '@/types/effectivebetting'

vi.mock('vue-i18n', () => ({
  useI18n: vi.fn(() => ({
    t: vi.fn((msg: string) => msg)
  }))
}))

const topTableDataDefault = [{ sum: 'Total', betting: '1000', jackpot: '500' }]

const ebTableDataDefault: IGameBetList[] = [
  { gameKind: 3, bet: 100, payoff: 50 },
  { gameKind: 5, bet: 200, payoff: 150 },
  { gameKind: 66, bet: 300, payoff: 250 }
]

describe('EffectiveBettingTable.vue', () => {
  let wrapper: VueWrapper<any>

  const mountComponent = (propsData: any = {}) => {
    return mount(EffectiveBettingTable, {
      props: propsData
    })
  }

  it('renders correctly with default empty props', () => {
    wrapper = mountComponent({ topTableData: [], ebTableData: [] })
    expect(wrapper.find('.effectivebetting-table').exists()).toBe(true)
    const tables = wrapper.findAllComponents({ name: 'ElTable' })
    expect(tables.length).toBe(2)

    const topTable = tables[0]
    expect(topTable.props('data')).toEqual([])

    const ebTable = tables[1]
    expect(ebTable.props('data')).toEqual([])
    expect(ebTable.props('emptyText')).toBe('S_NO_DATA')
  })

  it('renders correctly with default props', async () => {
    wrapper = mountComponent({ topTableData: topTableDataDefault, ebTableData: ebTableDataDefault })
    const vm = wrapper.vm as any

    await vm.$nextTick()
    await nextTick()

    const topTable = wrapper.findAllComponents({ name: 'ElTable' })[0]
    const topTableHeaderCells = topTable.findAll('thead .cell')
    const topTableBodyText = ['Total', '1000', '500']
    const topTableBodyCells = topTable.findAll('tbody .cell')

    expect(topTableHeaderCells).toHaveLength(3)
    expect(topTableBodyCells.every((cell, index) => cell.text() === topTableBodyText[index] )).toBe(true)

    const ebTable = wrapper.findAllComponents({ name: 'ElTable' })[1]
    const ebTableHeaderCells = ebTable.findAll('thead .cell')
    const ebTableBodyText = [
      ...['game_3_sub', '100', '50'],
      ...['game_5_sub', '200', '150'],
      ...['game_66_sub', '300', '250']
    ]
    const ebTableBodyCells = ebTable.findAll('tbody .cell')

    expect(ebTableHeaderCells).toHaveLength(3)
    expect(ebTableBodyCells.every((cell, index) => cell.text() === ebTableBodyText[index] )).toBe(true)
  })
})