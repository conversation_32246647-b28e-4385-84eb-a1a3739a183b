<script setup lang="ts">

import { useI18n } from 'vue-i18n'
import { IGameBetList } from '@/types/effectivebetting'
import { PropType } from 'vue';

const { t } = useI18n()

const props = defineProps({
  topTableData: {
    type: Array,
    default: () => ([]),
    required: true
  },
  ebTableData: {
    type: Array as PropType<IGameBetList[]>,
    default: () => ([]),
    required: true
  }
})

const gameKindMap = {
  3: t('game_3_sub'),
  5: t('game_5_sub'),
  12: t('game_12_sub'),
  31: t('game_31_sub_nbb'),
  38: t('game_38_sub'),
  66: t('game_66_sub'),
} as any
</script>

<template>
  <div class="effectivebetting-table">
    <div class="table">
      <el-table 
        :data="props.topTableData" 
        :border="true"
        header-cell-class-name="table-header"
        cell-class-name="table-cell"
      >
        <el-table-column prop="sum" label="" />
        <el-table-column prop="betting" :label="t('S_AG_VALID_BET')" />
        <el-table-column prop="jackpot" :label="t('S_PAYOFF_PAYOFF')" />
      </el-table>
    </div>

    <div class="table">
      <el-table
        :data="props.ebTableData"
        :border="true"
        header-cell-class-name="table-header2"
        cell-class-name="table-cell2"
        :empty-text="t('S_NO_DATA')"
      >
        <el-table-column prop="gameKind" :label="t('S_GAME_LOBBY_LIST')">
          <template #default="scope">
            <div>{{ gameKindMap[scope.row.gameKind] || scope.row.gameKind }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="bet" :label="t('S_AG_VALID_BET')" />
        <el-table-column prop="payoff" :label="t('S_PAYOFF_PAYOFF')" />
      </el-table>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.effectivebetting-table {
  width: 100%;
  
  .table{
    width: 90%;
    margin: 0 auto;
    margin-top: 20px;
  }

  :deep(.el-table){
    .table-header {
      color: var(--mcenter-collapse-item-text);
      font-size: 15px;
      text-align: center !important;
      background: #eee !important;
    }

    .table-header2 {
      color: var(--mcenter-table-header2--text);
      font-size: 15px;
      text-align: center !important;
      background: var(--mcenter-betinfo-table-header-bg) !important;
    }

    .table-cell {
      color: var(--mcenter-table-cell2--text);
      text-align: center !important;
    }

    .table-cell2 {
      color: var(--mcenter-table-cell2--text);
      text-align: center !important;
    }

    .el-table__empty-text {
      color: var(--mcenter-nodata);
    }
  }
}
</style>
