import { flushPromises, mount, VueWrapper } from '@vue/test-utils'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { nextTick, ref } from 'vue'
import fishIntro from './fishIntro.vue'
import type { IGameInfo, IGameIntro } from '@/types/game'
import { type TabsPaneContext } from 'element-plus'

const mockT = vi.fn((key: string) => key)
const mockLocale = ref('en-us')
vi.mock('vue-i18n', () => ({
  useI18n: () => ({
    t: mockT,
    locale: mockLocale,
  }),
}))

const mockRouterPush = vi.fn()
const mockRouteQuery = ref<{ id?: string; to?: string }>({})
vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')
  return {
    ...actual,
    useRoute: () => ({
      query: mockRouteQuery.value,
    }),
    useRouter: () => ({
      push: mockRouterPush,
    }),
  }
})

const mockFetchFishingIntro = vi.fn()
const mockAllIntroListStore = ref<IGameIntro[]>([])
vi.mock('@/stores/fishingGame', () => ({
  useFishingGameStore: vi.fn(() => ({
    fetchFishingIntro: mockFetchFishingIntro,
    fishingIntro: mockAllIntroListStore,
  })),
}))

const mockWindowOpen = vi.fn()
global.open = mockWindowOpen

const mockFishGameList: IGameInfo[] = [
  { gameId: 101, gameKind: 5, icon: 'game1.icon', name: 'Aqua Adventure', link: 'http://game1.link/play' },
  { gameId: 102, gameKind: 5, icon: 'game2.icon', name: 'Deep Sea Treasures', link: 'http://game2.link/play' },
]

const mockStoreIntroData: IGameIntro[] = [
  { gameId: 101, feature: ['101_feat_a', '101_feat_b'], video: true, ratio: ['101_ratio_a'] },
  { gameId: 102, feature: ['102_feat_a'], video: false, ratio: [] },
]

const defaultProps = {
  fishGameList: mockFishGameList,
  cdnUrl: 'https://cdn.example.com',
  isLogin: false,
  unixTime: 1678886400,
}

describe('fishIntro.vue', () => {
  let wrapper: VueWrapper<any>

  beforeEach(() => {
    setActivePinia(createPinia())
    mockFetchFishingIntro.mockClear()
    mockFetchFishingIntro.mockImplementation(() => Promise.resolve())
    mockRouterPush.mockClear()
    mockWindowOpen.mockClear()
    mockLocale.value = 'en-us'
  })

  const createWrapper = (props = {}, initialRouteQuery = { id: '101', to: 'intro' }) => {
    mockAllIntroListStore.value = [...mockStoreIntroData]
    mockRouteQuery.value = initialRouteQuery

    return mount(fishIntro, {
      props: { ...defaultProps, ...props },
      global: {
        stubs: {
          transition: false,
        },
      },
    })
  }

  it('should render back button and game tabs', async () => {
    mockFetchFishingIntro.mockResolvedValue(undefined)
    wrapper = createWrapper()

    await nextTick()

    expect(wrapper.find('.back-btn').exists()).toBe(true)
    expect(wrapper.find('.back-btn').text()).toBe('Back')

    const gameTabHeaders = wrapper.findAllComponents({ name: 'ElTabs' })[0].findAll('.el-tabs__item.is-top')
    const gameTabPanes = wrapper.findAllComponents({ name: 'ElTabPane' })
    
    expect(gameTabPanes.length).toBe(2)
    expect(gameTabHeaders[0].text()).toBe('Aqua Adventure')
    expect(gameTabHeaders[1].text()).toBe('Deep Sea Treasures')
  })

  it('should fetch and display intro data on mount for the active tab', async () => {
    mockFetchFishingIntro.mockResolvedValue(undefined)
    wrapper = createWrapper({}, { id: '101', to: 'intro' })

    await flushPromises()

    expect(mockFetchFishingIntro).toHaveBeenCalled()
    expect(wrapper.vm.activeTab).toBe('101')
    expect(wrapper.vm.currentIntroList.length).toBe(1)
    expect(wrapper.vm.currentIntroList[0].gameId).toBe(101)

    const featureImages = wrapper.findAllComponents({ name: 'el-tab-pane' })[0].findAll('.feature-bg')
    expect(featureImages.length).toBeGreaterThan(0)
    expect(featureImages[0].attributes('src')).toContain(`https://cdn.example.com/client/static/image/gamePicture/fisharea/feature/en/101_feat_a.jpg`)
  })

  it('should handle error during fetchFishingIntro', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())
    const errorMessage = new Error('Network Error')
    mockFetchFishingIntro.mockRejectedValue(errorMessage)
    wrapper = createWrapper()
    
    await nextTick()

    expect(console.error).toHaveBeenCalledWith(errorMessage)
  })

  it('should switch game tabs and update content on handleTabClick', async () => {
    mockFetchFishingIntro.mockResolvedValue(undefined)
    wrapper = createWrapper({}, { id: '101', to: 'intro' })

    await nextTick()

    const tabToClick = { props: { name: '102' } } as TabsPaneContext
    wrapper.vm.handleTabClick(tabToClick)

    expect(mockRouterPush).toHaveBeenCalledWith({ query: { to: 'intro', id: '102' } })
  })

  it('should switch content tabs', async () => {
    mockFetchFishingIntro.mockResolvedValue(undefined)
    wrapper = createWrapper({}, { id: '101', to: 'intro' })

    await nextTick()

    wrapper.vm.activeContentName = 'video'

    await nextTick()

    expect(wrapper.find('video').exists()).toBe(true)
    expect(wrapper.find('video source').attributes('src')).toContain(`https://cdn.example.com/client/static/image/gamePicture/fisharea/video/101.mp4`)
    expect(wrapper.find('.video-bg').exists()).toBe(true)
    expect(wrapper.find('.video-bg').attributes('src')).toContain(`https://cdn.example.com/client/static/image/gamePicture/fisharea/videoBg/101.jpg`)

    wrapper.vm.activeContentName = 'ratio'

    await nextTick()

    const ratioImages = wrapper.findAll('.ratio-bg')

    expect(ratioImages.length).toBeGreaterThan(0)
    expect(ratioImages[0].attributes('src')).toContain(`https://cdn.example.com/client/static/image/gamePicture/fisharea/ratio/en/101_ratio_a.jpg`)
  })

  it('should use different locale for image paths', async () => {
    mockFetchFishingIntro.mockResolvedValue(undefined)
    mockLocale.value = 'zh-cn'
    wrapper = createWrapper({}, { id: '101', to: 'intro' })

    await flushPromises()

    const featureImages = wrapper.findAll('.feature-bg')

    expect(featureImages[0].attributes('src')).toContain(`https://cdn.example.com/client/static/image/gamePicture/fisharea/feature/zh-cn/101_feat_a.jpg`)
  })

  it('should navigate back on backToGamelist', async () => {
    mockFetchFishingIntro.mockResolvedValue(undefined)
    wrapper = createWrapper()

    await nextTick()
    await wrapper.find('.back-btn').trigger('click')

    expect(mockRouterPush).toHaveBeenCalledWith({ name: 'fishing', query: { to: '' } })
    expect(wrapper.vm.showIntro).toBe(false)
    expect(wrapper.vm.activeContentName).toBe('feature')
  })

  describe('openGame method', () => {
    it('should show login alert if not logged in', async () => {
      mockFetchFishingIntro.mockResolvedValue(undefined)
      wrapper = createWrapper({ isLogin: false }, { id: '101', to: 'intro' })

      await flushPromises()

      const enterGameButton = wrapper.findAll('.entergame-btn').at(0)

      expect(enterGameButton?.exists()).toBe(true)

      await enterGameButton!.trigger('click')

      expect(mockWindowOpen).not.toHaveBeenCalled()
    })

    it('should open game window if logged in', async () => {
      mockFetchFishingIntro.mockResolvedValue(undefined)
      wrapper = createWrapper({ isLogin: true }, { id: '101', to: 'intro' })
      
      await flushPromises()

      const enterGameButton = wrapper.findAll('.entergame-btn').at(0)

      expect(enterGameButton?.exists()).toBe(true)

      await enterGameButton!.trigger('click')

      expect(mockWindowOpen).toHaveBeenCalledWith(
        'http://game1.link/play',
        '',
        'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no'
      )
    })
  })

  it('should correctly render enter game button background image', async () => {
    mockFetchFishingIntro.mockResolvedValue(undefined)
    wrapper = createWrapper({}, { id: '101', to: 'intro' })

    await flushPromises()

    const enterGameButton = wrapper.findAll('.entergame-btn').at(0)

    expect(enterGameButton?.attributes('style')).toContain(`background-image: url(https://cdn.example.com/client/static/image/hall/casino/fisharea/fishing_btn.png?v=1678886400)`)
  })

  it('should not render video tab if intro.video is undefined', async () => {
    mockFetchFishingIntro.mockResolvedValue(undefined)
    wrapper = createWrapper({}, { id: '102', to: 'intro' })
    
    await flushPromises()

    expect(wrapper.vm.currentIntroList[0].video).toBe(false)

    wrapper.vm.activeContentName = 'video'
    
    await nextTick()

    expect(wrapper.find('video').exists()).toBe(false)
  })
})