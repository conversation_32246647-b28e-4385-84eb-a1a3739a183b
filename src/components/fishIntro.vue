<script setup lang="ts">

import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import type { TabsPaneContext } from 'element-plus'
import { Back } from '@element-plus/icons-vue'
import type { IGameInfo, IGameIntro } from '@/types/game'
import { useFishingGameStore } from '@/stores/fishingGame'
import { ElMessageBox } from 'element-plus'
import { storeToRefs } from 'pinia'

const props = defineProps({
  fishGameList: {
    type: Array as PropType<IGameInfo[]>,
    default: () => [],
    required: true
  },
  cdnUrl: {
    type: String,
    default: '',
    required: true
  },
  isLogin: {
    type: Boolean,
    default: false,
    required: true
  },
  unixTime: {
    type: Number,
    default: 0,
    required: false
  }
})

const { t, locale } = useI18n()
const route = useRoute()
const router = useRouter()
const fishingGameStore = useFishingGameStore()
const { fishingIntro: allIntroList } = storeToRefs(fishingGameStore)
const currentIntroList = ref<IGameIntro[]>([])
const activeTab = ref<string>('')
const activeContentName = ref('feature')
const showIntro = ref(false)

const handleTabClick = async (tab: TabsPaneContext) => {
  router.push({query: { to: 'intro', id: tab.props.name}})
  activeContentName.value = 'feature'
  currentIntroList.value = allIntroList.value.filter((intro: IGameIntro) => {
    return intro.gameId == tab.props.name
  })
}

const backToGamelist = () => {
  router.push({ name: 'fishing', query: { to: '' }})
  showIntro.value = false
  activeContentName.value = 'feature'
}

const openGame = (url: string) => {
  if (!props.isLogin) {
    ElMessageBox.alert(
      t('S_LOGIN_TIPS'), 
      '', 
      { center: true, showClose: false}
    )
    return
  }
  window.open(
    url,
    '',
    'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no'
  )
}

onMounted(async () => {
  try {
    await fishingGameStore.fetchFishingIntro()
    currentIntroList.value = allIntroList.value.filter((intro: IGameIntro) => {
      return String(intro.gameId) == route.query.id
    })
    activeTab.value = route.query.id as string
  } catch (error) {
    ElMessage.error((error as Error).message)
    console.error(error)
  }
})

</script>

<template>
    <div class="game-intro">
        <el-button class="back-btn" type="info" :icon="Back" @click="backToGamelist()">
          {{ 'Back' }}
        </el-button>

        <el-tabs
          v-model="activeTab"
          type="card"
          class="fishing-tabs"
          @tab-click="handleTabClick"
        >
          <template v-for="game in props.fishGameList" :key="game.gameId">
            <el-tab-pane :label="game.name" :name="game.gameId.toString()">
              <el-tabs v-model="activeContentName" class="fishing-content-tabs" tab-position="left">
                <div v-for="intro in currentIntroList" :key="intro.gameId" class="intro-content">
                  <el-tab-pane
                    v-if="intro.feature.length > 0"
                    :label="t('S_GAME_FEATURE')"
                    name="feature"
                  >
                    <el-carousel trigger="click" height="550px">
                      <el-carousel-item v-for="feature in intro.feature" :key="feature">
                        <img
                          class="feature-bg"
                          :src="`${props.cdnUrl}/client/static/image/gamePicture/fisharea/feature/${locale == 'en-us' ? 'en' : locale}/${feature}.jpg?v=${props.unixTime}`"
                          :alt="feature"
                          lazy
                        />
                      </el-carousel-item>
                    </el-carousel>
                    <button
                      class="entergame-btn"
                      :style="{
                        backgroundImage: `url(${props.cdnUrl}/client/static/image/hall/casino/fisharea/fishing_btn.png?v=${props.unixTime})`
                      }"
                      @click="openGame(game.link)"
                    >
                      {{ t('S_GAME_ENTER') }}
                    </button>
                  </el-tab-pane>
                  <el-tab-pane v-if="intro.video" :label="t('S_BRILIANT_VIDEO')" name="video">
                    <img
                      class="video-bg"
                      :src="`${props.cdnUrl}/client/static/image/gamePicture/fisharea/videoBg/${game.gameId}.jpg?v=${props.unixTime}`"
                    />
                    <video controls autoplay playsinline muted>
                      <source
                        :src="`${props.cdnUrl}/client/static/image/gamePicture/fisharea/video/${game.gameId}.mp4?v=${props.unixTime}`"
                        type="video/mp4"
                      />
                    </video>
                    <button
                      class="video entergame-btn"
                      :style="{
                        backgroundImage: `url(${props.cdnUrl}/client/static/image/hall/casino/fisharea/fishing_btn.png?v=${props.unixTime})`
                      }"
                      @click="openGame(game.link)"
                    >
                      {{ t('S_GAME_ENTER') }}
                    </button>
                  </el-tab-pane>
                  <el-tab-pane
                    v-if="intro.ratio.length > 0"
                    :label="t('S_RATIO')"
                    name="ratio"
                  >
                    <el-carousel trigger="click" height="550px">
                      <el-carousel-item v-for="ratio in intro.ratio" :key="ratio">
                        <img
                          class="ratio-bg"
                          :src="`${props.cdnUrl}/client/static/image/gamePicture/fisharea/ratio/${locale == 'en-us' ? 'en' : locale}/${ratio}.jpg?v=${props.unixTime}`"
                          :alt="ratio"
                          lazy
                        />
                        <button
                          class="entergame-btn"
                          :style="{
                        backgroundImage: `url(${props.cdnUrl}/client/static/image/hall/casino/fisharea/fishing_btn.png?v=${props.unixTime})`
                          }"
                          @click="openGame(game.link)"
                        >
                          {{ t('S_GAME_ENTER') }}
                        </button>
                      </el-carousel-item>
                    </el-carousel>
                  </el-tab-pane>
                </div>
              </el-tabs>
            </el-tab-pane>
          </template>
        </el-tabs>
    </div>
</template>

<style lang="scss" scoped>

    .fishing-tabs {
        position: relative;
        width: 70%;
        height: 600px;
        background: #fff;

        .feature-bg, .video-bg, .ratio-bg  {
          position: relative;
          width: 100%;
          height: auto;
        }
        video {
          position: absolute;
          width: 63.5%;
          top: 17%;
          left: 17.5%;
        }
        .entergame-btn {
            width: 12rem;
            height: 2.5rem;
            line-height: 2.5rem;
            position: absolute;
            bottom: 90px;
            right: 20px;
            font-size: 16px;
            border: none;
            text-align: center;
            color: #8c4600;
            background-position: 0px 0px;
            background-repeat: no-repeat;
            border-radius: 11.5rem;
            cursor: pointer;
            &:hover {
              background-position: 0px 100%;
            }
            &.video {
              bottom: 20px;
            }
        }
        @media (max-width: 800px) {
          width: 100%;
        }
    }

    .game-intro {
      display: flex;
      justify-content: center;
      padding: 10px;

      .back-btn {
        margin: 0 10px;
      }
    }

</style>