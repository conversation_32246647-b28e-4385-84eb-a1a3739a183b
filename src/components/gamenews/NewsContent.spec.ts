import { mount } from '@vue/test-utils'
import { createI18n } from 'vue-i18n'
import NewsContent from '@/components/gamenews/NewsContent.vue'
import { describe, expect, it } from 'vitest'
import { nextTick } from 'vue'

const messages = {
  en: {
    S_DATE: 'Date',
    S_CONTENT_MSG: 'Content Message',
    S_NO_DATA: 'No data',
  },
}

const i18n = createI18n({
  locale: 'en',
  messages,
})

describe('NewsContent', () => {
  it('renders table with correct headers and data', async () => {
    const mockData = [
      { dateTime: '2025-01-01', content: 'News content 1' },
      { dateTime: '2025-01-02', content: 'News content 2' },
    ]

    const wrapper = mount(NewsContent, {
      global: {
        plugins: [i18n],
      },
      props: {
        data: mockData,
      },
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    expect(wrapper.find('th').text()).toBe('Date')
    expect(wrapper.findAll('th')[1].text()).toBe('Content Message')

    const rows = wrapper.findAll('tr')
    expect(rows.length).toBe(mockData.length + 1)

    mockData.forEach((item, index) => {
      const cells = rows[index + 1].findAll('td')
      expect(cells[0].text()).toBe(item.dateTime)
      expect(cells[1].text()).toBe(item.content)
    })
  })
})
