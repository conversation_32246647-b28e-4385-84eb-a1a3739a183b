<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { GameNewsData } from '@/types/gamenews'

const props = defineProps({
  data: {
    type: Array as PropType<GameNewsData[]>,
    default: () => [],
    required: true
  }
})

const { t } = useI18n()

</script>

<template>
  <div class="table-container">
    <el-table 
      :data="props.data"
      header-cell-class-name="table-header2"
      cell-class-name="table-cell2"
      :empty-text="t('S_NO_DATA')"
    >
      <el-table-column prop="dateTime" :label="t('S_DATE')" />
      <el-table-column prop="content" :label="t('S_CONTENT_MSG')" />
    </el-table>
  </div>
</template>

<style lang="scss" scoped>
.table-container {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;

  .el-table {
    margin: 15px;
  }

  :deep(.el-table){
    .table-header2 {
      color: var(--mcenter-table-header2--text);
      font-size: 15px;
      text-align: center !important;
      background: var(--mcenter-betinfo-table-header-bg) !important;
    }

    .table-cell2 {
      color: var(--mcenter-table-cell2--text);
      text-align: center !important;
    }

    .el-table__empty-text {
      color: var(--mcenter-nodata);
    }
  }
}
</style>
