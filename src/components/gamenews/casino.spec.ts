import { mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'
import { nextTick } from 'vue'
import CasinoNews from '@/components/gamenews/casino.vue'
import { getCasinoNews } from '@/api/mcenter'
import { ICasinoNews } from '@/types/gamenews'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

dayjs.extend(timezone)
dayjs.extend(utc)

vi.mock('@/api/mcenter', () => ({
  getCasinoNews: vi.fn(),
}))

vi.mock('vue3-cookies', () => ({
  useCookies: vi.fn().mockReturnValue({
    cookies: {
      get: (key: string) => {
        if (key === 'session_id') return 'session_id'
        if (key === 'lang') return 'en'
        return null
      }
    }
  }),
}))

vi.mock('@/components/selector/ArraySelector.vue', () => ({
  default: {
    name: 'ArraySelector',
    props: ['dataList'],
    template: '<div class="mock-array-selector">Mock ArraySelector</div>',
  },
}))

vi.mock('@/components/gamenews/NewsContent.vue', () => ({
  default: {
    name: 'NewsContent',
    props: ['data'],
    template: '<div class="mock-news-content">Mock NewsContent</div>',
  },
}))

describe('CasinoNews', () => {
  it('fetches casino news and handles maintenance', async () => {
    const mockGameNews = {
      maintain: true,
      maintainInfo: 'Maintenance in progress'
    }

    const mockGetCasinoNews = vi.mocked(getCasinoNews)
    mockGetCasinoNews.mockResolvedValue(mockGameNews as ICasinoNews)

    const wrapper = mount(CasinoNews, {
      props: {
        isCasinomaintain: true
      }
    })
  
    await wrapper.vm.$nextTick()
    await nextTick()

    expect(wrapper.html()).toContain('Maintenance in progress')
    expect(mockGetCasinoNews).toHaveBeenCalledOnce()
  })

  it('renders news content when not in maintenance', async () => {
    const mockGameNews = {
      startTime: '2025-01-07',
      data: [
        { dateTime: '2025-01-01', content: 'News 1' },
        { dateTime: '2025-01-02', content: 'News 2' }
      ],
      maintain: false,
      maintainInfo: ''
    }

    vi.mocked(getCasinoNews).mockResolvedValue(mockGameNews as ICasinoNews)

    const wrapper = mount(CasinoNews)

    expect(wrapper.findComponent({ name: 'el-skeleton' }).exists()).toBe(true)

    await wrapper.vm.$nextTick()
    await vi.waitFor(() => {
      expect(getCasinoNews).toHaveResolved()
    })
    await nextTick()

    expect(wrapper.findComponent({ name: 'el-skeleton' }).exists()).toBe(false)
    expect(wrapper.findComponent({ name: 'ArraySelector' }).exists()).toBe(true)
    expect(wrapper.findComponent({ name: 'NewsContent' }).exists()).toBe(true)
    expect(wrapper.findComponent({ name: 'NewsContent' }).props('data')).toEqual(mockGameNews.data)
  })

  it('fetches casino news error', async () => {
    const mockGetCasinoNews = vi.mocked(getCasinoNews)
    const mockError = new Error('test error')
    mockGetCasinoNews.mockRejectedValue(mockError)
    vi.spyOn(console, 'error').mockImplementation(() => {})

    const wrapper = mount(CasinoNews)
  
    await wrapper.vm.$nextTick()
    await nextTick()

    expect(console.error).toHaveBeenCalledWith(mockError)
  })
})