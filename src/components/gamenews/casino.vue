<script setup lang="ts">
import { ref } from 'vue'
import { getCasinoNews } from '@/api/mcenter'
import type { GameNewsData } from '@/types/gamenews'
import ArraySelector from '@/components/selector/ArraySelector.vue'
import NewsContent from '@/components/gamenews/NewsContent.vue'
import { useCookies } from 'vue3-cookies'
import dayjs from 'dayjs'

const { cookies } = useCookies()
const maintainText = ref('')
const gameNews = ref<GameNewsData[]>([] as GameNewsData[])
const dateSelected = ref('')
const isCasinoMaintain = ref(false)
const isLoading = ref(false)
const isShowSkeleton = ref(true)
const dateList = ref([] as string[])

const getDateRange = (dateTime: number) => {
  let result = [dayjs.tz(dateTime, 'Etc/GMT+4').format('YYYY-MM-DD')]
  for (let x = 1; x <= 6; x++) {
    result.push(dayjs.tz(dateTime, 'Etc/GMT+4').subtract(x, 'day').format('YYYY-MM-DD'))
  }
  return result
}

const fetchCasinoNews = async () => {
  isLoading.value = true

  try {
    const param = {
      date: dateSelected.value,
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
      session_id: cookies.get('SESSION_ID'),
      lang: cookies.get('lang')
    }

    const res = await getCasinoNews(param)
  
    if(res.maintain) {
      isCasinoMaintain.value = true
      maintainText.value = res.maintainInfo as string
    } else {
      gameNews.value = res.data
      if (dateSelected.value === '') dateSelected.value = dateList.value[0]
    }
  } catch (error) {
    ElMessage.error((error as Error).message)
    console.error(error)
  } finally {
    isLoading.value = false
    isShowSkeleton.value = false
  }
}

dateList.value = getDateRange(new Date().getTime())
fetchCasinoNews()
</script>

<template>
  <div v-if="isCasinoMaintain === false">
    <el-skeleton v-if="isShowSkeleton" :rows="5" animated />
    <div
      v-else
      v-loading="isLoading"
    >
      <div>
        <!-- Date Selector -->
        <ArraySelector
          v-model="dateSelected"
          :data-list="dateList || []"
          @change="fetchCasinoNews"
        />
      </div>
      <NewsContent :data="gameNews || []" />
    </div>
  </div>
  <div
    v-else
    v-html="maintainText"
  />
</template>
