import { flushPromises, mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'
import { nextTick } from 'vue'
import LiveNews from '@/components/gamenews/live.vue'
import { getLiveGameCode, getLiveNews } from '@/api/mcenter'
import { ILiveNews } from '@/types/gamenews'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

dayjs.extend(timezone)
dayjs.extend(utc)

vi.mock('@/api/mcenter', () => ({
  getLiveNews: vi.fn(),
  getLiveGameCode: vi.fn()
}))

vi.mock('@/api/game', () => ({
  getLiveGameList: vi.fn(() => ({
    3001: '百家乐'
  }))
}))

vi.mock('vue-i18n', () => ({
  useI18n: vi.fn(() => ({
    t: vi.fn((msg: string) => msg),
  })),
}));

const mockGameCode = {
  gameId: {
    '3001': 3001,
    '3002': 3002
  },
  tableCode: {
    '3001': {
      1: 'A',
      2: 'B'
    },
    '3002': {
      3: 'C',
      4: 'D'
    }
  } 
}

vi.mock('@/components/selector/ArraySelector.vue', () => ({
  default: {
    name: 'ArraySelector',
    props: ['dataList'],
    template: '<div class="mock-array-selector">Mock ArraySelector</div>',
  },
}))

vi.mock('@/components/selector/KeyValueSelector.vue', () => ({
  default: {
      name: 'KeyValueSelector',
      props: ['dataList', 'dataInitialLabel'],
      template: '<div class="mock-key-value-selector">Mock KeyValueSelector</div>',
  },
}))

vi.mock('@/components/gamenews/NewsContent.vue', () => ({
  default: {
    name: 'NewsContent',
    props: ['data'],
    template: '<div class="mock-news-content">Mock NewsContent</div>',
  },
}))

describe('LiveNews', () => {
  it('fetches casino news and handles maintenance', async () => {
    const mockGameNews = {
      maintain: true,
      maintainInfo: 'Maintenance in progress',
    }

    const mockGetLiveNews = vi.mocked(getLiveNews)
    mockGetLiveNews.mockResolvedValue(mockGameNews as ILiveNews)
    vi.mocked(getLiveGameCode).mockResolvedValue(mockGameCode)

    const wrapper = mount(LiveNews, {
      props: {
        isLivemaintain: true
      }
    })
  
    await flushPromises()

    expect(wrapper.html()).toContain('Maintenance in progress')
    expect(mockGetLiveNews).toHaveBeenCalledOnce()
  })

  it('renders news content when not in maintenance', async () => {
    const mockGameNews = {
      maintain: false,
      maintainInfo: '',
      startTime: '2025-01-01',
      data: [
        {
          dateTime: "2024-11-11 23:17:21",
          content: "敬請客戶留意!! 【 香港時間 2024-11-12 遊戲： 百家樂 桌號： AS5 局號： 324061319 】因系統異常，本牌局將作廢。下注金額將會退還，本遊戲將會開始新局，不便之處敬請見諒。",
          gameId: 3001,
          tableCode: 86
        },
        {
          dateTime: "2024-11-11 18:06:30",
          content: "敬請客戶留意!! 【 香港時間 2024-11-12 遊戲： 百家樂 桌號： AS5 局號： 324061319 】因系統異常，本牌局將作廢。下注金額將會退還，本遊戲將會開始新局，不便之處敬請見諒。",
          gameId: 3001,
          tableCode: 86
        }
      ],
    }

    vi.mocked(getLiveNews).mockResolvedValue(mockGameNews as ILiveNews)
    vi.mocked(getLiveGameCode).mockResolvedValue(mockGameCode)

    const wrapper = mount(LiveNews)

    expect(wrapper.findComponent({ name: 'el-skeleton' }).exists()).toBe(true)

    await wrapper.vm.$nextTick()
    await vi.waitFor(() => {
      expect(getLiveNews).toHaveResolved()
    })
    await nextTick()

    expect(wrapper.findComponent({ name: 'el-skeleton' }).exists()).toBe(false)
    expect(wrapper.findComponent({ name: 'ArraySelector' }).exists()).toBe(true)
    expect(wrapper.findComponent({ name: 'KeyValueSelector' }).exists()).toBe(true)
    expect(wrapper.findComponent({ name: 'NewsContent' }).exists()).toBe(true)
    expect(wrapper.findComponent({ name: 'NewsContent' }).props('data')).toEqual(mockGameNews.data)
  })

  it('updates game code when game type changes', async () => {
    const mockGameNews = {
      maintain: false,
      maintainInfo: '',
      startTime: '2025-01-01',
      data: [
        {
          dateTime: "2024-11-11 23:17:21",
          content: "敬請客戶留意!! 【 香港時間 2024-11-12 遊戲： 百家樂 桌號： AS5 局號： 324061319 】因系統異常，本牌局將作廢。下注金額將會退還，本遊戲將會開始新局，不便之處敬請見諒。",
          gameId: 3001,
          tableCode: 86
        },
        {
          dateTime: "2024-11-11 18:06:30",
          content: "敬請客戶留意!! 【 香港時間 2024-11-12 遊戲： 百家樂 桌號： AS5 局號： 324061319 】因系統異常，本牌局將作廢。下注金額將會退還，本遊戲將會開始新局，不便之處敬請見諒。",
          gameId: 3001,
          tableCode: 86
        }
      ],
    }

    vi.mocked(getLiveNews).mockResolvedValue(mockGameNews)
    vi.mocked(getLiveGameCode).mockResolvedValue(mockGameCode)

    const wrapper = mount(LiveNews)

    await flushPromises()

    await wrapper.findComponent({ name: 'KeyValueSelector' }).vm.$emit('update:modelValue', 3002)

    expect((wrapper.vm as any).gameId).toBe(3002)
    expect(getLiveNews).toHaveBeenCalled()
  })

  it('fetches live news error', async () => {
    const mockGetLiveNews = vi.mocked(getLiveNews)
    const mockError = new Error('test error')
    mockGetLiveNews.mockRejectedValue(mockError)
    vi.spyOn(console, 'log').mockImplementation(() => {})

    const wrapper = mount(LiveNews)
  
    await wrapper.vm.$nextTick()
    await nextTick()

    expect(console.log).toHaveBeenCalledWith(mockError)
  })
})