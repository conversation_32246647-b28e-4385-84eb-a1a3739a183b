<script setup lang="ts">
import { ref } from 'vue'
import { getLiveNews, getLiveGameCode } from '@/api/mcenter'
import { getLiveGameList } from '@/api/game'
import type { ILiveGameNewsData } from '@/types/gamenews'
import ArraySelector from '@/components/selector/ArraySelector.vue'
import KeyValueSelector from '@/components/selector/KeyValueSelector.vue'
import NewsContent from '@/components/gamenews/NewsContent.vue'
import { useI18n } from 'vue-i18n'
import { useCookies } from 'vue3-cookies'
import dayjs from 'dayjs'

const { t } = useI18n()
const maintainText = ref('')
const gameNews = ref<ILiveGameNewsData[]>([] as ILiveGameNewsData[])
const date = ref('')
const gameId = ref()
const tableCode = ref()
const isLiveMaintain = ref(false)
const isLoading = ref(false)
const isShowSkeleton = ref(true)
const gameIdList = ref({} as {[key: string]: string})
const tableCodeList = ref({} as {[key: string]: {[key: string]: string} } )
const dateList = ref([] as string[])
const gameList = ref({} as {[key: string]: string })
const { cookies } = useCookies()

const getDateRange = (dateTime: number) => {
  let result = [dayjs.tz(dateTime, 'Etc/GMT+4').format('YYYY-MM-DD')]
  for (let x = 1; x <= 2; x++) {
    result.push(dayjs.tz(dateTime, 'Etc/GMT+4').subtract(x, 'day').format('YYYY-MM-DD'))
  }
  return result
}

const fetchGameCode = async () => {
  try {
    const res = await getLiveGameCode({ hall_id: Number(localStorage.getItem('hallinfo_hallid')) })
    gameIdList.value = Object.values(res.gameId).reduce((acc, gameId) => {
      acc[gameId] = gameList.value[gameId]
      return acc
    }, {} as {[key: string]: string})
    tableCodeList.value = res.tableCode
  } catch (error) {
    ElMessage.error((error as Error).message)
    console.log(error)
  }
}

const fetchGameList = async () => {
  try {
    const res = await getLiveGameList({ hall_id: Number(localStorage.getItem('hallinfo_hallid')), lang: cookies.get('lang') })
    gameList.value = res
  } catch (error) {
    ElMessage.error((error as Error).message)
    console.log(error)
  }
}

const fetchLiveNews = async () => {
  isLoading.value = true

  try {
    const param = {
      session_id: cookies.get('SESSION_ID'),
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
      lang: cookies.get('lang'),
      date: date.value,
      game_id: gameId.value,
      table_code: tableCode.value
    }

    const res = await getLiveNews(param)

    if(res.maintain) {
      isLiveMaintain.value = true
      maintainText.value = res.maintainInfo as string
    } else {
      gameNews.value = res.data

      if (date.value === '') {
        date.value = dateList.value[0]
      }
    }
  } catch (error) {
    ElMessage.error((error as Error).message)
    console.log(error)
  } finally {
    isLoading.value = false
    isShowSkeleton.value = false
  }
}

const fetchData = async () => {
  try {
    await fetchGameList()
    fetchGameCode()
    fetchLiveNews()
  } catch (error) {
    ElMessage.error((error as Error).message)
    console.log(error)
  }
}

const onGameTypeChange = () => {
  tableCode.value = ''
  fetchLiveNews()
}

dateList.value = getDateRange(new Date().getTime())
fetchData()
</script>

<template>
  <div v-if="isLiveMaintain === false">
    <el-skeleton v-if="isShowSkeleton" :rows="5" animated />
    <div
      v-else
      v-loading="isLoading"
    >
      <div>
        <!-- Date Selector -->
        <ArraySelector
          v-model="date"
          :data-list="dateList || []"
          @change="fetchLiveNews()"
        />
        <!-- GameId Selector -->
        <KeyValueSelector
          v-model="gameId"
          :data-list="gameIdList"
          :data-initial-label="t('S_ALL')"
          @change="onGameTypeChange"
        />
        <!-- TableCode Selector -->
        <KeyValueSelector
          v-show="gameId"
          v-model="tableCode"
          :data-list="tableCodeList[gameId] || {}"
          :data-initial-label="t('S_ALL')"
          @change="fetchLiveNews"
        />
      </div>
      <NewsContent :data="gameNews || []" />
    </div>
  </div>
  <div
    v-else
    v-html="maintainText"
  />
</template>
