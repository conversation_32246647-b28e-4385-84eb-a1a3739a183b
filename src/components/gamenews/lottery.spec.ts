import { mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'
import { nextTick } from 'vue'
import { createI18n } from 'vue-i18n'
import LotteryNews from '@/components/gamenews/lottery.vue'
import { getLotteryNews } from '@/api/mcenter'
import { ILotteryNews } from '@/types/gamenews'

const i18n = createI18n({
  locale: 'en',
  messages: {
    en: {
      S_GENERAL_MSG: 'General',
      S_SYSTEM_MSG: 'System',
    },
  },
})

vi.mock('vue3-cookies', () => ({
  useCookies: vi.fn().mockReturnValue({
    cookies: {
      get: (key: string) => {
        if (key === 'session_id') return 'session_id'
        if (key === 'lang') return 'en'
        return null
      }
    }
  }),
}))

vi.mock('@/api/mcenter', () => ({
  getLotteryNews: vi.fn(),
}))

vi.mock('@/components/selector/ObjectSelector.vue', () => ({
  default: {
    name: 'ObjectSelector',
    props: ['dataList'],
    template: '<div class="mock-object-selector">Mock ObjectSelector</div>',
  },
}))

vi.mock('@/components/gamenews/NewsContent.vue', () => ({
  default: {
    name: 'NewsContent',
    props: ['data'],
    template: '<div class="mock-news-content">Mock NewsContent</div>',
  },
}))

describe('LotteryNews', () => {
  it('fetches lottery news and handles maintenance', async () => {
    const mockGameNews = {
      maintain: true,
      maintainInfo: 'Maintenance in progress',
      data: [],
    }

    const mockGetLotteryNews = vi.mocked(getLotteryNews)
    mockGetLotteryNews.mockResolvedValue(mockGameNews as ILotteryNews)

    const wrapper = mount(LotteryNews, {
      global: {
        plugins: [i18n],
      },
      props: {
        isLotterymaintain: true
      }
    })
  
    await wrapper.vm.$nextTick()
    await nextTick()

    expect(wrapper.html()).toContain('Maintenance in progress')
    expect(mockGetLotteryNews).toHaveBeenCalledOnce()
  })

  it('renders news content when not in maintenance', async () => {
    const mockGameNews = {
      maintain: false,
      data: [
        { dateTime: '2025-01-01', content: 'News 1' },
        { dateTime: '2025-01-02', content: 'News 2' }
      ]
    }

    vi.mocked(getLotteryNews).mockResolvedValue(mockGameNews as ILotteryNews)

    const wrapper = mount(LotteryNews, {
      global: {
        plugins: [i18n],
      },
    })

    expect(wrapper.findComponent({ name: 'el-skeleton' }).exists()).toBe(true)

    await wrapper.vm.$nextTick()
    await vi.waitFor(() => {
      expect(getLotteryNews).toHaveResolved()
    })
    await nextTick()

    expect(wrapper.findComponent({ name: 'el-skeleton' }).exists()).toBe(false)
    expect(wrapper.findComponent({ name: 'ObjectSelector' }).exists()).toBe(true)
    expect(wrapper.findComponent({ name: 'NewsContent' }).exists()).toBe(true)
    expect(wrapper.findComponent({ name: 'NewsContent' }).props('data')).toEqual(mockGameNews.data)
  })

  it('fetches lottery news error', async () => {
    const mockGetLotteryNews = vi.mocked(getLotteryNews)
    const mockError = new Error('test error')
    mockGetLotteryNews.mockRejectedValue(mockError)
    vi.spyOn(console, 'log').mockImplementation(() => {})

    const wrapper = mount(LotteryNews, {
      global: {
        plugins: [i18n],
      },
    })
  
    await wrapper.vm.$nextTick()
    await nextTick()

    expect(console.log).toHaveBeenCalledWith(mockError)
  })
})