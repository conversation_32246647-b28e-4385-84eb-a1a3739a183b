<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { getLotteryNews } from '@/api/mcenter'
import ObjectSelector from '@/components/selector/ObjectSelector.vue'
import NewsContent from '@/components/gamenews/NewsContent.vue'
import { ILotteryNewsCategory, GameNewsData } from '@/types/gamenews'
import { useCookies } from "vue3-cookies"

const { t } = useI18n()
const { cookies } = useCookies()
const maintainText = ref('')
const newsTypeSelected = ref<ILotteryNewsCategory>(ILotteryNewsCategory.General)
const newsTypeList = ref<{label: string, value: number}[]>([])
const gameNews = ref<GameNewsData[]>([] as GameNewsData[])
const isLotteryMaintain = ref(false)
const isLoading = ref(false)
const isShowSkeleton = ref(true)
const defaultNewsTypeList = [
  {
    label: t('S_GENERAL_MSG'),
    value: ILotteryNewsCategory.General
  },
  {
    label: t('S_SYSTEM_MSG'),
    value: ILotteryNewsCategory.System
  }
]

const fetchLotteryNews = async () => {
  isLoading.value = true

  try {
    const param = {
      session_id: cookies.get('SESSION_ID'),
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
      lang: cookies.get('lang'),
      category: newsTypeSelected.value
    }

    const res = await getLotteryNews(param)
    
    if(res.maintain) {
      isLotteryMaintain.value = true
      maintainText.value = res.maintainInfo as string
    } else {
      gameNews.value = res.data
    }
  } catch (error) {
    ElMessage.error((error as Error).message)
    console.log(error)
  } finally {
    isLoading.value = false
    isShowSkeleton.value = false
    newsTypeList.value = defaultNewsTypeList
  }
}

fetchLotteryNews()
</script>

<template>
  <div v-if="isLotteryMaintain === false">
    <el-skeleton v-if="isShowSkeleton" :rows="5" animated />
    <div
      v-else
      v-loading="isLoading"
    >
      <div>
        <!-- Newstype Selector -->
        <ObjectSelector
          v-model="newsTypeSelected"
          :data-list="newsTypeList"
          @change="fetchLotteryNews"
        />
      </div>
      <NewsContent :data="gameNews || []" />
    </div>
  </div>
  <div
    v-else
    v-html="maintainText"
  />
</template>
