import { mount, VueWrapper } from '@vue/test-utils'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import GamePanel from '@/components/lottery/gamePanel.vue' // 確保路徑正確
import { ILotteryGamePanel, ILotteryTimer } from '@/types/lottery'
import { useImportTheme } from '@/composables/useImportTheme'

vi.mock('vue-i18n', () => ({
  useI18n: () => ({
    t: (key: string) => key
  }),
}))

const mockGetGameLink = vi.fn((url: string) => `mocked_game_link_${url}`)

vi.mock('@/composables/useLotteryGameLink', () => ({
  useLotteryGameLink: vi.fn(() => ({
    getGameLink: mockGetGameLink,
  })),
}))

const mockImportStyleFn = vi.fn()

vi.mock('@/composables/useImportTheme', () => ({
  useImportTheme: vi.fn(() => ({
    importStyle: mockImportStyleFn,
  })),
}))

const createWrapper = (propsData: any): VueWrapper<any> => {
  return mount(GamePanel, {
    props: propsData
  })
}

const mockGamePanelList: ILotteryGamePanel[] = [
  {
    name: 'Test Game 1',
    ruleLink: 'http://rule.link/1',
    prevNum: '12345',
    prevResult: [
      { key: '01', value: 1 },
      { key: '02', value: 20 },
      { key: '07', value: 30 },
    ],
    resultGroup: 'LT',
    link: 'game_link_1',
    openTimestamp: 0,
    closeTimestamp: 0
  },
  {
    name: 'Test Game 2',
    ruleLink: '', // 沒有規則連結
    prevNum: '67890',
    prevResult: [
      { key: '01', value: 5 },
      { key: '02', value: 15 },
    ],
    resultGroup: 'OTHER', // 不會使用 ballcolor
    link: 'game_link_2',
      openTimestamp: 0,
    closeTimestamp: 0
  },
]

const mockGamePanelTimerActive: ILotteryTimer = {
  time: '00:10:30',
  status: true, // 對應 'time-start' class
}

const mockGamePanelTimerStopped: ILotteryTimer = {
  time: '00:00:00',
  status: false, // 對應 'time-stop' class
}

describe('Lottery GamePanel', () => {
  let wrapper: VueWrapper<any>

  beforeEach(() => {
    global.open = vi.fn().mockReturnValue({ focus: vi.fn() } as any) // Mock focus 方法
  })

  describe('Props Rendering', () => {
    it('should correctly render game panel information when isLogin is true', () => {
      wrapper = createWrapper({
        gamePanelList: [mockGamePanelList[0]],
        gamePanelTimer: mockGamePanelTimerActive,
        serverTime: 1,
        isLogin: true,
      })

      expect(wrapper.find('h3').text()).toBe('Test Game 1')
      expect(wrapper.find('.info').exists()).toBe(true)
      expect(wrapper.find('.gamepanel-time-box').exists()).toBe(true)
      expect(wrapper.find('.count-time').text()).toBe('00:10:30')
      expect(wrapper.find('.prev_num').text()).toBe('No.12345')
      
      const results = wrapper.findAll('.prev_result')

      expect(results.length).toBe(3)
      expect(results[0].text()).toBe('01')
      expect(results[1].text()).toBe('20')
      expect(results[2].text()).toBe('30')
      expect(results[2].classes()).toContain('last')
      expect(wrapper.text()).toContain('+')

      expect(wrapper.find('.enter-game').text()).toBe('S_GAME_ENTER')
    })

    it('should not render timer and previous results when isLogin is false', () => {
      wrapper = createWrapper({
        gamePanelList: [mockGamePanelList[0]],
        gamePanelTimer: mockGamePanelTimerActive,
        serverTime: 1,
        isLogin: false,
      })

      expect(wrapper.find('h3').text()).toBe('Test Game 1')
      expect(wrapper.find('.info').exists()).toBe(true)
      expect(wrapper.find('.content').exists()).toBe(false)
      expect(wrapper.find('.gamepanel-time-box').exists()).toBe(false)
      expect(wrapper.find('.prev_num').exists()).toBe(false)
      expect(wrapper.findAll('.prev_result').length).toBe(0)
      expect(wrapper.find('.enter-game').text()).toBe('S_GAME_ENTER')
    })

    it('should not render timer when serverTime is 0', () => {
        wrapper = createWrapper({
          gamePanelList: [mockGamePanelList[0]],
          gamePanelTimer: mockGamePanelTimerActive,
          serverTime: 0, // serverTime 為 0
          isLogin: true,
        })
        expect(wrapper.find('.gamepanel-time-box').exists()).toBe(false)
    })

    it('should not render rule link icon (.info) if p_game.ruleLink is an empty string', () => {
        wrapper = createWrapper({
          gamePanelList: [mockGamePanelList[1]],
          gamePanelTimer: mockGamePanelTimerActive,
          serverTime: 1,
          isLogin: true,
        })
        expect(wrapper.find('.info').exists()).toBe(false)
    })

    it('should correctly render multiple game blocks when gamePanelList contains multiple items', () => {
        wrapper = createWrapper({
          gamePanelList: mockGamePanelList,
          gamePanelTimer: mockGamePanelTimerActive,
          serverTime: 1,
          isLogin: true,
        })

        const gameNames = wrapper.findAll('h3')

        expect(gameNames.length).toBe(2)
        expect(gameNames[0].text()).toBe('Test Game 1')
        expect(gameNames[1].text()).toBe('Test Game 2')

        const enterGameButtons = wrapper.findAll('.enter-game')

        expect(enterGameButtons.length).toBe(2)
    })
  })

  describe('ballcolor method tests', () => {
    beforeEach(() => {
        wrapper = createWrapper({
            gamePanelList: [mockGamePanelList[0]],
            gamePanelTimer: mockGamePanelTimerActive,
            serverTime: 1,
            isLogin: true,
        })
    })

    it('should return red background for red ball numbers', () => {
      expect(wrapper.vm.ballcolor(1)).toEqual({ background: '#D90000' })
      expect(wrapper.vm.ballcolor(46)).toEqual({ background: '#D90000' })
    })

    it('should return blue background for blue ball numbers', () => {
      expect(wrapper.vm.ballcolor(3)).toEqual({ background: '#3990B4' })
      expect(wrapper.vm.ballcolor(48)).toEqual({ background: '#3990B4' })
    })

    it('should return green background for green ball numbers', () => {
      expect(wrapper.vm.ballcolor(5)).toEqual({ background: '#10AB17' })
      expect(wrapper.vm.ballcolor(49)).toEqual({ background: '#10AB17' })
    })

    it('should return "none" background for numbers not in color lists', () => {
      expect(wrapper.vm.ballcolor(99)).toEqual({ background: 'none' })
      expect(wrapper.vm.ballcolor(0)).toEqual({ background: 'none' })
    })

    it('should have undefined style attribute for prev_result when resultGroup is not "LT"', () => {
        wrapper = createWrapper({
            gamePanelList: [mockGamePanelList[1]], // resultGroup 是 'OTHER'
            gamePanelTimer: mockGamePanelTimerActive,
            serverTime: 1,
            isLogin: true,
        })

        const results = wrapper.findAll('.prev_result')

        expect(results[0].attributes('style')).toBe('')
        expect(results[1].attributes('style')).toBe('')
    })

    it('should apply style returned by ballcolor to prev_result when resultGroup is "LT"', () => {
        wrapper = createWrapper({
            gamePanelList: [mockGamePanelList[0]],
            gamePanelTimer: mockGamePanelTimerActive,
            serverTime: 1,
            isLogin: true,
        })

        const results = wrapper.findAll('.prev_result')

        expect(results[0].attributes('style')).toContain('background: rgb(217, 0, 0);')
        expect(results[1].attributes('style')).toContain('background: rgb(57, 144, 180);')
        expect(results[2].attributes('style')).toContain('background: rgb(217, 0, 0);')
    })
  })

  describe('gameStatusClass computed property tests', () => {
    it('timer box should have "time-start" class when gamePanelTimer.status is true', () => {
      wrapper = createWrapper({
        gamePanelList: [mockGamePanelList[0]],
        gamePanelTimer: mockGamePanelTimerActive,
        serverTime: 1,
        isLogin: true,
      })

      const timeBox = wrapper.find('.gamepanel-time-box')

      expect(timeBox.classes()).toContain('time-start')
    })

    it('timer box should have "time-stop" class when gamePanelTimer.status is false', () => {
      wrapper = createWrapper({
        gamePanelList: [mockGamePanelList[0]],
        gamePanelTimer: mockGamePanelTimerStopped, // status: false
        serverTime: 1,
        isLogin: true,
      })
      const timeBox = wrapper.find('.gamepanel-time-box')
      expect(timeBox.classes()).toContain('time-stop')
    })
  })

  describe('openGameLink method tests', () => {
    it('should call window.open and getGameLink when enter game button is clicked and isLogin is true', async () => {
      wrapper = createWrapper({
        gamePanelList: [mockGamePanelList[0]],
        gamePanelTimer: mockGamePanelTimerActive,
        serverTime: 1,
        isLogin: true,
      })

      const enterGameButton = wrapper.findAll('.enter-game')[0]
      await enterGameButton.trigger('click')
      
      expect(mockGetGameLink).toHaveBeenCalledWith('game_link_1')
      expect(global.open).toHaveBeenCalledWith('mocked_game_link_game_link_1', '')
      expect((global.open as any).mock.results[0].value.focus).toHaveBeenCalledTimes(1);
    })

    it('should call ElMessageBox.alert and not call window.open when enter game button is clicked and isLogin is false', async () => {
      wrapper = createWrapper({
        gamePanelList: [mockGamePanelList[0]],
        gamePanelTimer: mockGamePanelTimerActive,
        serverTime: 1,
        isLogin: false,
      })
      const enterGameButton = wrapper.findAll('.enter-game')[0]
      await enterGameButton.trigger('click')

      expect(global.open).not.toHaveBeenCalled()
    })
  })

  describe('openRuleLink method tests', () => {
    it('should call window.open with correct parameters when rule icon is clicked', async () => {
      wrapper = createWrapper({
        gamePanelList: [mockGamePanelList[0]], // mockGamePanelList[0] 有 ruleLink
        gamePanelTimer: mockGamePanelTimerActive,
        serverTime: 1,
        isLogin: true,
      })
      const ruleLinkIcon = wrapper.findAll('.info')[0] // 由於 v-for
      await ruleLinkIcon.trigger('click')

      expect(global.open).toHaveBeenCalledTimes(1)
      expect(global.open).toHaveBeenCalledWith(
        mockGamePanelList[0].ruleLink,
        '',
        'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no'
      )
      // 驗證 focus 被調用
      expect((global.open as any).mock.results[0].value.focus).toHaveBeenCalledTimes(1);
    })
  })

  describe('useImportTheme composable tests', () => {
    it('should call importStyle method returned by useImportTheme when component is mounted', () => {
      createWrapper({
        gamePanelList: [mockGamePanelList[0]],
        gamePanelTimer: mockGamePanelTimerActive,
        serverTime: 1,
        isLogin: true,
      })

      expect(useImportTheme).toHaveBeenCalled()
      expect(mockImportStyleFn).toHaveBeenCalledWith('pc/gamePanel')
    })
  })
})
