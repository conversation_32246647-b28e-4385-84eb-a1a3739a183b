<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { ILotteryGamePanel, ILotteryTimer} from '@/types/lottery'
import { useImportTheme } from '@/composables/useImportTheme'
import { ElMessageBox } from 'element-plus'
import { useLotteryGameLink } from '@/composables/useLotteryGameLink'

const props = defineProps({
  gamePanelList: {
    type: Array as PropType<ILotteryGamePanel[]>,
    default: () => [],
    required: true
  },
  gamePanelTimer: {
    type: Object as PropType<ILotteryTimer>,
    default: () => {},
    required: true
  },
  serverTime: {
    type: Number,
    default: 0,
    required: true
  },
  isLogin: {
    type: Boolean,
    default: false,
    required: true
  }
})

const { t } = useI18n()
const { getGameLink } = useLotteryGameLink()
const redBall = ref([ 1, 2, 7,8,12,13,18,19,23,24,29,30,34,35,40,45,46 ])
const greenBall= ref([ 5,6,11,16,17,21,22,27,28,32,33,38,39,43,44,49 ])
const blueBall = ref([ 3,4,9,10,14,15,20,25,26,31,36,37,41,42,47,48 ])
const { importStyle } = useImportTheme()

const ballcolor = (num: any) => {
  let backgroundColor = 'none';
  const checkRed = redBall.value.includes(num)
  const checkBlue = blueBall.value.includes(num)
  const checkGreen =  greenBall.value.includes(num)

  if (checkRed) {
      backgroundColor = '#D90000';
  }
  if (checkBlue) {
      backgroundColor = '#3990B4';
  }
  if (checkGreen) {
      backgroundColor = '#10AB17';
  }
  
  return {
      background: backgroundColor
  };
}

const gameStatus = () => {
    return props.gamePanelTimer.status ? 'time-start' : 'time-stop'
}

const openGameLink = (url: string) => {
  if (props.isLogin === false) {
    ElMessageBox.alert(
      t('S_LOGIN_TIPS'), 
      '', 
      { center: true, showClose: false}
    )
    return
  }

  window.open(
    getGameLink(url),
    '',
  )?.focus()
}

const openRuleLink = (url: string) => {
  window.open(
    url,
    '',
    'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no'
  )?.focus()
}

importStyle('pc/gamePanel')
</script>

<template>
    <div class="game-panel">
      <template v-for="p_game in props.gamePanelList" :key="p_game.name">
        <div class="top">
          <h3>{{ p_game.name }}</h3>
          <span
            v-if="p_game.ruleLink"
            class="info"
            @click="openRuleLink(p_game.ruleLink)"
          ></span>
        </div>
        <div v-if="isLogin" class="content">
          <!-- 倒數時間 -->
          <div v-if="props.serverTime > 0" :class="['gamepanel-time-box', gameStatus()]">
              <div class="count-time">
                  {{ gamePanelTimer.time }}
              </div>
          </div>
          <!-- 上期期數 -->
          <p v-if="p_game.prevNum" class="prev_num">{{ 'No.' + p_game.prevNum }}</p>
          <!-- 上期結果 -->
          <template v-for="item in p_game.prevResult" :key="item.key">
            <template v-if="item.key == '07'">
              +
              <span
                class="prev_result last"
                :style="p_game.resultGroup === 'LT' ? ballcolor(item.value) : ''"
              >
              {{ item.value > 10 ? item.value : '0'+ item.value }}
            </span>
          </template>
            <span
              v-else
              class="prev_result"
              :style="p_game.resultGroup === 'LT' ? ballcolor(item.value) : ''"
            >
              {{ item.value > 10 ? item.value : '0'+ item.value  }}
            </span>
          </template>
        </div>
        <div class="enter-game" @click="openGameLink(p_game.link)">
          {{ t('S_GAME_ENTER') }}
        </div>
      </template> 
    </div>
</template>

<style lang="scss" scoped>
  .game-panel {
    width: 32.5%;
    background: var(--pc-gamePanel-game-panel-bg);
    display: flex;
    flex-direction: column;
    justify-content: space-around;

    .top {
      margin: 20px auto;
      margin-top: auto;

      h3 {
        display: inline-block;
        font-size: 2.6rem;
        color: #fff;
      }

      .info {
        display: inline-block;
        &::after {
          content: '?';
          margin-left: 5px;
          padding: 1px 8px;
          color: #888;
          font-size: 1.4rem;
          font-weight: bold;
          border: 2px solid #777;
          border-radius: 50%;
          cursor: pointer;
        }
        &:hover {
          &::after {
            color: #69eece;
            border: 2px solid #69eece;
          }
        }
      }

      @media (max-width: 1000px) {
        margin: 10px auto;
      }
    }

    .content {
      margin: 0 auto;
      margin-top: auto;
      text-align: center;
      color: #fff;

      .gamepanel-time-box {
        width: 290px;
        min-height: 40px;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;

        @media (max-width: 1000px) {
          width: 240px;
          margin: 10px auto;
        }
      }

      .time-start {
        background: url('/client/static/image/hall/lottery/icon_play.png') 2.5vw 0.1vw no-repeat;
      }

      .time-stop {
        background: url('/client/static/image/hall/lottery/icon_stop.png') 2.5vw 0.1vw no-repeat;
      }

      .prev_num {
        margin: 1.5vw;
        display: block;
        text-align: left;
        font-size: 1.2rem;
        color: #bbb;
      }

      .prev_result {
        margin: 3px;
        padding: 4px 6px;
        border-radius: 50%;
        @media (max-width: 1000px) {
          padding: 6px;
        }
      }

      @media (max-width: 1000px) {
        display: flex;
        align-items: center;
      }
    }

    .enter-game {
      margin-top: auto;
      width: 100%;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: 1.2rem;
      background: linear-gradient(270deg, #f5e047, #f7d846, #fd6661, #ff4469);
      color: #fff;
      cursor: pointer;

      @media (max-width: 1000px) {
        width: 10%;
        height: auto;
        margin: 0 auto;
        margin-right: 0;
      }
    }
    

    &.full {
      width: 100%;
      flex-direction: row;

      .info {
        margin: 10px auto;
      }

      .enter-game {
        width: 10%;
        height: auto;
        margin: 0 auto;
        margin-right: 0;
      }

      .content {
        display: flex;
        align-items: center;

        .gamepanel-time-box {
          width: 240px;
          margin: 10px auto;
        }
        .prev_result {
          padding: 6px;
        }
      }
    }

    @media (max-width: 1000px) {
      width: 100%;
      flex-direction: row;
    }
  }
</style>