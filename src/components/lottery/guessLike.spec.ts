import { mount, VueWrapper } from '@vue/test-utils'
import { describe, it, expect, vi } from 'vitest'
import Guess<PERSON><PERSON> from './guessLike.vue'

vi.mock('vue-i18n', () => ({
  useI18n: () => ({
    t: vi.fn((key) => key),
  }),
}))

const mockGetGameLink = vi.fn((url) => `mocked_game_link_for_${url}`)
vi.mock('@/composables/useLotteryGameLink', () => ({
  useLotteryGameLink: () => ({
    getGameLink: mockGetGameLink,
  }),
}))

const mockWindowFocus = vi.fn()
const mockWindowOpen = vi.fn().mockReturnValue({ focus: mockWindowFocus })
global.open = mockWindowOpen

const defaultProps = {
  guessLikeList: [],
  cdnUrl: 'https://test-cdn.com',
  isLogin: false,
  unixTime: 1234567890,
}

const gameListMock = [
  { name: 'Awesome Game 1', platformName: 'Platform X', link: 'game/link1', gameId: 'g1' },
  { name: 'Super Game 2', platformName: 'Platform Y', link: 'game/link2', gameId: 'g2' },
]

const createWrapper = (props?: object) => {
  return mount(GuessLike, {
    props: { ...defaultProps, ...props },
  })
}

describe('GuessLike', () => {
  let wrapper: VueWrapper<any>

  it('renders the title correctly', () => {
    wrapper = createWrapper()
    const title = wrapper.find('.title')

    expect(title.text()).toBe('S_GUESS_LIKE')
  })

  it('displays "no data" message when guessLikeList is empty', () => {
    wrapper = createWrapper({ guessLikeList: [] })
    expect(wrapper.find('.content').exists()).toBe(false)

    const noDataBox = wrapper.find('.gamebox.no-data')

    expect(noDataBox.exists()).toBe(true)
    expect(noDataBox.text()).toBe('S_NO_DATA_CL')
  })

  it('renders game items when guessLikeList is provided', () => {
    wrapper = createWrapper({ guessLikeList: gameListMock })

    expect(wrapper.find('.content').exists()).toBe(true)
    expect(wrapper.find('.gamebox.no-data').exists()).toBe(false)

    const gameBoxes = wrapper.findAll('.content .gamebox')

    expect(gameBoxes.length).toBe(2)
    expect(gameBoxes[0].find('.gamekind').text()).toBe('Platform X')
    expect(gameBoxes[0].find('.gamename').text()).toBe('Awesome Game 1')
    expect(gameBoxes[1].find('.gamekind').text()).toBe('Platform Y')
    expect(gameBoxes[1].find('.gamename').text()).toBe('Super Game 2')
  })

  it('not open link if not logged in', async () => {
    wrapper = createWrapper({ guessLikeList: [gameListMock[0]], isLogin: false })
    const gameBox = wrapper.find('.content .gamebox')

    await gameBox.trigger('click')

    expect(mockGetGameLink).not.toHaveBeenCalled()
    expect(mockWindowOpen).not.toHaveBeenCalled()
  })

  it('opens the game link and focuses the new window if logged in', async () => {
    const game = gameListMock[0]
    wrapper = createWrapper({ guessLikeList: [game], isLogin: true })
    const gameBox = wrapper.find('.content .gamebox')

    await gameBox.trigger('click')

    expect(mockGetGameLink).toHaveBeenCalledWith('game/link1')
    expect(mockWindowOpen).toHaveBeenCalledWith('mocked_game_link_for_game/link1')
    expect(mockWindowFocus).toHaveBeenCalledTimes(1)
  })
})