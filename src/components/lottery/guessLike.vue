<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { ElMessageBox } from 'element-plus'
import { ILotteryLikeGuess } from '@/types/lottery'
import { useLotteryGameLink } from '@/composables/useLotteryGameLink'

const props = defineProps({
  guessLikeList: {
    type: Array as PropType<ILotteryLikeGuess[]>,
    default: () => [],
    required: true
  },
  cdnUrl: {
    type: String,
    default: '',
    required: true
  },
  isLogin: {
    type: Boolean,
    default: false,
    required: true
  },
  unixTime: {
    type: Number,
    default: 0,
    required: false
  }
})

const { t } = useI18n()
const { getGameLink } = useLotteryGameLink()

const hoverLogo = computed(() => {
  return `url(${props.cdnUrl}/client/static/image/hall/lottery/icon_bb_h.png?v=${props.unixTime})`
})

const openLink = (url: string) => {
  if (props.isLogin === false) {
    ElMessageBox.alert(
      t('S_LOGIN_TIPS'), 
      '', 
      { center: true, showClose: false}
    )
    return
  }
  window.open(
    getGameLink(url),
  )?.focus()
}

</script>

<template>
    <div class="guess-you-like">
        <div class="title">{{ t('S_GUESS_LIKE') }}</div>
        <div class="content" v-if="props.guessLikeList.length >0">
            <template v-for="guess_game in props.guessLikeList" :key="guess_game.name">
                <div class="gamebox" @click="openLink(guess_game.link)">
                <i
                    class="logo"
                    :style="`background: url(${props.cdnUrl}/client/static/image/hall/lottery/icon_bb_n.png?v=${props.unixTime}) 50% no-repeat`"
                >
                </i>
                <div class="right">
                    <h4 class="gamekind">{{ guess_game.platformName }}</h4>
                    <h3 class="gamename">{{ guess_game.name }}</h3>
                </div>
                </div>
            </template>
        </div>
        <div v-else class="gamebox no-data">{{ t('S_NO_DATA_CL') }}</div>
    </div>
</template>

<style lang="scss" scoped>
  .guess-you-like {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    border-top: 1px solid #777;
    
    .title {
      width: 10%;
      height: 55px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #555;
      color: #19cdad;
      font-size: 1.2rem;
      font-weight: bold;
      text-align: center;
    }

    .content {
      width: 90%;
      display: flex;
      background: #666;
    }

    .gamebox {
      width: 200px;
      height: 55px;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      border-left: 1px solid #777;
      background: #666;
      cursor: pointer;

      .logo {
        flex: 3;
        width: 10vw;
        height: 5vw;
      }

      .right {
        flex: 7;
        text-align: left;
        margin-left: 5px;

        .gamekind {
          color: #19cdad;
          font-size: 1rem;
          margin-bottom: 3px;
        }

        .gamename {
          color: #eee;
          font-size: 1rem;
        }
      }

      &:hover {
        background: linear-gradient(90deg, #0295ec, #1aceac 44%, #85efdd);
        .logo {
          background-image: v-bind(hoverLogo) !important;
        }

        .right {
          .gamekind {
            color: #fff;
          }
        }
      }

      &.no-data {
        max-width: 100%;
        width: 100%;
        color: #ddd;
      }
    }
  }
</style>