import { mount, VueWrapper } from '@vue/test-utils'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import HeroBoard from './heroBoard.vue'
import { ILotteryLeaderBoard } from '@/types/lottery' 

const mockLocaleRef = ref('en')
const mockT = vi.fn((key) => {
  if (key === 'S_LOTTERY_BET_NOW') {
    return mockLocaleRef.value === 'en' ? 'Become a legend.Bet Now!' : '成为传奇，立即投注！'
  }
  if (key === 'S_LOGIN_TIPS') return 'Please log in first.'
  return key
})

vi.mock('vue-i18n', () => ({
  useI18n: () => ({
    t: mockT,
    locale: mockLocaleRef
  }),
}))

const mockGetGameLink = vi.fn((url: string) => `processed-${url}`)
vi.mock('@/composables/useLotteryGameLink', () => ({
  useLotteryGameLink: () => ({
    getGameLink: mockGetGameLink,
  }),
}))

const mockWindowFocus = vi.fn()
const mockWindowOpen = vi.fn().mockReturnValue({ focus: mockWindowFocus })
global.open = mockWindowOpen

const mockLeaderboardDataItems: ILotteryLeaderBoard[] = [
  { userName: 'TestUser1', gameName: 'Awesome Game 1', originProfit: '1000', link: 'game/link1' },
  { userName: 'TestUser2', gameName: 'Super Game 2', originProfit: '2000', link: 'game/link2' },
]

const defaultProps = {
  leaderboardData: [] as ILotteryLeaderBoard[],
  traditionLink: 'default/tradition/link',
  cdnUrl: 'https://cdn.example.com',
  isLogin: false,
  unixTime: 1234567890,
}

const createWrapper = (props?: any) => {
  return mount(HeroBoard, {
    props: { ...defaultProps, ...props }
  })
}

describe('HeroBoard', () => {
  let wrapper: VueWrapper<any>

  describe('Rendering with leaderboard data', () => {
    it('should render hero blocks when leaderboardData is provided', () => {
      wrapper = createWrapper({ leaderboardData: mockLeaderboardDataItems })

      expect(wrapper.find('.hero-board-box').exists()).toBe(true)

      const heroBlocks = wrapper.findAll('.hero-block')

      expect(heroBlocks.length).toBe(2)
    })

    it('should render correct content inside hero blocks', () => {
      wrapper = createWrapper({ leaderboardData: [mockLeaderboardDataItems[0]] })
      const heroBlock = wrapper.find('.hero-block p')

      expect(heroBlock.exists()).toBe(true)      
      expect(heroBlock.html()).toContain('S_LOTTERY_HERO_BOARD')
    })
  })

  describe('Rendering without leaderboard data', () => {
    beforeEach(() => {
      wrapper = createWrapper({ leaderboardData: [] })
    })

    it('should render no-data container', () => {
      expect(wrapper.find('.noherodata-wrap').exists()).toBe(true)
      expect(wrapper.find('.hero-board-box').exists()).toBe(false)
    })

    it('should render correct "no data" text for English locale', () => {
      mockLocaleRef.value = 'en'
      wrapper = createWrapper({ leaderboardData: [] })

      const textParagraph = wrapper.find('.noherodata-wrap .txt')
      expect(textParagraph.text()).toContain('Become a legend.')
      const callToAction = wrapper.find('.noherodata-wrap .call-to-action')
      expect(callToAction.exists()).toBe(true)
      expect(callToAction.text()).toBe('Bet Now!')
    })

    it('should render correct "no data" text for Chinese locale', () => {
      mockLocaleRef.value = 'zh'
      wrapper = createWrapper({ leaderboardData: [] })

      const textParagraph = wrapper.find('.noherodata-wrap .txt')
      expect(textParagraph.text()).toContain('成为传奇，')
      const callToAction = wrapper.find('.noherodata-wrap .call-to-action')
      expect(callToAction.exists()).toBe(true)
      expect(callToAction.text()).toBe('立即投注！')
    })
  })

  describe('openLink functionality', () => {
    beforeEach(() => {
      mockGetGameLink.mockClear()
      mockWindowOpen.mockClear()
      mockWindowFocus.mockClear()
    })

    it('user is not logged in when clicking hero block', async () => {
      wrapper = createWrapper({ leaderboardData: [mockLeaderboardDataItems[0]], isLogin: false })
      await wrapper.find('.hero-block').trigger('click')
      
      expect(mockWindowOpen).not.toHaveBeenCalled()
    })

    it('user is logged in when clicking hero block', async () => {
      wrapper = createWrapper({ leaderboardData: [mockLeaderboardDataItems[0]], isLogin: true })
      await wrapper.find('.hero-block').trigger('click')

      expect(mockGetGameLink).toHaveBeenCalledWith('game/link1')
      expect(mockWindowOpen).toHaveBeenCalledWith('processed-game/link1')
      expect(mockWindowFocus).toHaveBeenCalled()
    })

    it('user is not logged in when clicking call-to-action block', async () => {
      wrapper = createWrapper({ leaderboardData: [], isLogin: false })
      await wrapper.find('.call-to-action').trigger('click')

      expect(mockWindowOpen).not.toHaveBeenCalled()
    })

    it('user is logged in when clicking call-to-action block', async () => {
      wrapper = createWrapper({ leaderboardData: [], isLogin: true, traditionLink: 'test/tradition/link' })
      await wrapper.find('.call-to-action').trigger('click')

      expect(mockGetGameLink).toHaveBeenCalledWith('test/tradition/link')
      expect(mockWindowOpen).toHaveBeenCalledWith('processed-test/tradition/link')
      expect(mockWindowFocus).toHaveBeenCalled()
    })
  })
})