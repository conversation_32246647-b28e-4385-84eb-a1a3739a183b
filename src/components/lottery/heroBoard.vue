<script setup lang="ts">
import { ILotteryLeaderBoard } from '@/types/lottery'
import { useI18n } from 'vue-i18n'
import { ElMessageBox } from 'element-plus'
import { useLotteryGameLink } from '@/composables/useLotteryGameLink'

const { getGameLink } = useLotteryGameLink()

const props = defineProps({
  leaderboardData: {
    type: Array as PropType<ILotteryLeaderBoard[]>,
    default: () => [],
    required: true
  },
  traditionLink: {
    type: String,
    default: '',
    required: true
  },
  cdnUrl: {
    type: String,
    default: '',
    required: true
  },
  isLogin: {
    type: Boolean,
    default: false,
    required: true
  },
  unixTime: {
    type: Number,
    default: 0,
    required: false
  }
})

const { t, locale } = useI18n()

const heroBlockContent = (item: ILotteryLeaderBoard) => {
  return t('S_LOTTERY_HERO_BOARD').replace('%s', `${item.userName}`)
          .replace('%x', `<span style='display: inline-block; margin: 0 5px; color: #52A5FD; font-weight: bold; cursor: pointer;'>${item.gameName}</span>`)
          .replace('%y', `${item.originProfit}`)
}

const openLink = (url: string) => {
  if (props.isLogin === false) {
    ElMessageBox.alert(
      t('S_LOGIN_TIPS'), 
      '', 
      { center: true, showClose: false}
    )
    return
  }
  window.open(
    getGameLink(url),
  )?.focus()
}

</script>

<template>
    <div v-if="props.leaderboardData.length > 0" class="hero-board-box">
      <div
        v-for="(item, index) in props.leaderboardData"
        :key="index"
        class="hero-block"
        @click="openLink(item.link)"
        >
        <p v-html="heroBlockContent(item)"
        ></p>
      </div>
    </div>
    <div v-else class="noherodata-wrap">
        <i class="icon">
          <img :src="`${props.cdnUrl}/client/static/image/hall/lottery/icon_crown.png?v=${props.unixTime}`">
        </i>
        <p class="txt">
          {{ locale == 'en' ? 
            t('S_LOTTERY_BET_NOW').split('.')[0]+'.' : 
            t('S_LOTTERY_BET_NOW').split('，')[0]+'，'
          }}
          <span
            class="call-to-action" 
            @click="openLink(props.traditionLink)">
            {{ locale == 'en' ?
              t('S_LOTTERY_BET_NOW').split('.')[1] : 
              t('S_LOTTERY_BET_NOW').split('，')[1] 
            }}
          </span>
        </p>
    </div>
</template>

<style lang="scss" scoped>
  .hero-block {
    display: flex;
    padding: 10px 20px;
    font-size: 1.1rem;
    background: #fff;

    p {
      display: inline-block;
      font-weight: bold;
    }
  }

  .noherodata-wrap {
    display: flex;
    align-items: center;
    padding: 5px;
    border-bottom: 1px solid #eee;
    background: #fff;

    .icon {
      margin-left: 20px;
      margin-right: 10px;
    }

    .txt {
      color: #bbb;
      font-size: 1.1rem;
    }

    .call-to-action {
      color: #0295ec;
      font-weight: bold;
      cursor: pointer;
    }
  }

</style>