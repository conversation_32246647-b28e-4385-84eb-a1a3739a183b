import { mount, VueWrapper } from '@vue/test-utils'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import OfficialBoard from './officialBoard.vue'
import { ILotteryOfficial } from '@/types/lottery'

vi.mock('vue-i18n', () => ({
  useI18n: () => ({
    t: vi.fn((key) => key),
  }),
}))

const mockGetGameLink = vi.fn((url: string) => `processed-${url}`)
vi.mock('@/composables/useLotteryGameLink', () => ({
  useLotteryGameLink: () => ({
    getGameLink: mockGetGameLink,
  }),
}))

const mockWindowFocus = vi.fn()
const mockWindowOpen = vi.fn().mockReturnValue({ focus: mockWindowFocus })
global.open = mockWindowOpen

const mockOfficialDataItems: ILotteryOfficial[] = [
  {
    groupName: 'Normal Lottery',
    groupKey: 'NORMAL',
    games: [
      { name: 'Game A1', link: 'link/a1', isRecommand: true, gameId: 'a1' },
      { name: 'Game A2', link: 'link/a2', isRecommand: false, gameId: 'a2' },
      { name: 'Game A3', link: 'link/a3', isRecommand: false, gameId: 'a3' },
      { name: 'Game A4', link: 'link/a4', isRecommand: false, gameId: 'a4' },
    ],
    gameName: 'Normal Lottery',
    groupLink: 'group/link/normal',
    link: 'all/link/normal',
  },
  {
    groupName: 'BB Lottery',
    groupKey: 'BBFO',
    games: [
      { name: 'Game B1', link: 'link/b1', isRecommand: false, gameId: 'b1' },
    ],
    gameName: 'BBFO Lottery',
    groupLink: 'group/link/bbfo',
    link: 'all/link/bbfo',
  },
  {
    groupName: 'KENO (No Games)',
    groupKey: 'KENO',
    games: [],
    gameName: 'KENO Lottery',
    groupLink: 'group/link/keno',
    link: 'all/link/keno',
  }
]

const defaultProps = {
  officialData: [] as ILotteryOfficial[],
  isLogin: false,
}

const createWrapper = (props?: object): VueWrapper<any> => {
  return mount(OfficialBoard, {
    props: { ...defaultProps, ...props }
  })
}

describe('OfficialBoard', () => {
  let wrapper: VueWrapper<any>

  describe('Rendering', () => {
    it('should not render any game-wrap when officialData is empty', () => {
      wrapper = createWrapper({ officialData: [] })
      expect(wrapper.findAll('.game-wrap').length).toBe(0)
    })

    it('should render the correct', () => {
      wrapper = createWrapper({ officialData: mockOfficialDataItems })
      const groups = wrapper.findAll('.game-wrap')

      expect(groups.length).toBe(mockOfficialDataItems.length)
      expect(groups[0].find('.icon').classes()).toContain('offical-lotto-icon')
      expect(groups[1].find('.icon').classes()).toContain('offical-fastbb-icon')
      expect(groups[2].find('.icon').classes()).toContain('offical-keno-icon')

      const titles = wrapper.findAll('.official-title')
      expect(titles[0].text()).toBe('Normal Lottery')
      expect(titles[1].text()).toBe('BB Lottery')
    })

    it('should initially display a maximum of 3 games per group', () => {
      wrapper = createWrapper({ officialData: [mockOfficialDataItems[0]] })
      const gameLinks = wrapper.findAll('.official-game-list a')
      
      expect(gameLinks.length).toBe(3)
      expect(gameLinks[0].text()).toBe('Game A1')
      expect(gameLinks[1].text()).toBe('Game A2')
      expect(gameLinks[2].text()).toBe('Game A3')
      expect(gameLinks[0].classes()).toContain('recommand')
      expect(gameLinks[1].classes()).not.toContain('recommand')
      expect(wrapper.find('.official-game-more').exists()).toBe(true)
      expect(wrapper.find('.official-game-more').text()).toBe('...')
            
      const allOpenButton = wrapper.find('.official-showall-btn')
      
      expect(allOpenButton.exists()).toBe(true)
      expect(allOpenButton.text()).toBe('S_FL_ALL_OPEM')
    })

    it('should not render the "All Open" button if the group has no games', () => {
      wrapper = createWrapper({ officialData: [mockOfficialDataItems[2]] })
      expect(wrapper.find('.official-showall-btn').exists()).toBe(false)
    })
  })

  describe('gameSeriesIcon method', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('should return the correct class for known keys', () => {
      expect(wrapper.vm.gameSeriesIcon('NORMAL')).toBe('offical-lotto-icon')
      expect(wrapper.vm.gameSeriesIcon('BBFO')).toBe('offical-fastbb-icon')
      expect(wrapper.vm.gameSeriesIcon('PKE5')).toBe('offical-pk115-icon')
      expect(wrapper.vm.gameSeriesIcon('TTQ3')).toBe('offical-fast3-icon')
      expect(wrapper.vm.gameSeriesIcon('KENO')).toBe('offical-keno-icon')
      expect(wrapper.vm.gameSeriesIcon('TF')).toBe('offical-happy10-icon')
    })

    it('should return an empty string for unknown keys', () => {
      expect(wrapper.vm.gameSeriesIcon('UNKNOWN_KEY')).toBe('')
      expect(wrapper.vm.gameSeriesIcon('')).toBe('')
    })
  })

  describe('Interactions and openLink method', () => {
    describe('User not logged in', () => {
      beforeEach(() => {
        wrapper = createWrapper({ officialData: [mockOfficialDataItems[0]], isLogin: false })
      })

      it('not open link when clicking a game link', async () => {
        const firstGameLink = wrapper.find('.official-game-list a')
        await firstGameLink.trigger('click')

        expect(mockGetGameLink).not.toHaveBeenCalled()
        expect(mockWindowOpen).not.toHaveBeenCalled()
      })

      it('not open link when clicking the "more" button', async () => {
        const moreButton = wrapper.find('.official-game-more')
        await moreButton.trigger('click')
        
        expect(mockGetGameLink).not.toHaveBeenCalled()
        expect(mockWindowOpen).not.toHaveBeenCalled()
      })

      it('not open link when clicking the "All Open" button', async () => {
        const allOpenButton = wrapper.find('.official-showall-btn')
        await allOpenButton.trigger('click')

        expect(mockGetGameLink).not.toHaveBeenCalled()
        expect(mockWindowOpen).not.toHaveBeenCalled()
      })
    })

    describe('User logged in', () => {
      beforeEach(() => {
        wrapper = createWrapper({ officialData: mockOfficialDataItems, isLogin: true })
      })

      it('open game link when clicking a game link', async () => {
        const firstGameLink = wrapper.findAll('.game-wrap').at(0)!.find('.official-game-list a')
        await firstGameLink.trigger('click')

        expect(mockGetGameLink).toHaveBeenCalledWith('link/a1')
        expect(mockWindowOpen).toHaveBeenCalledWith('processed-link/a1')
        expect(mockWindowFocus).toBeCalled()
      })

      it('open group link when clicking the "more" button', async () => {
        const moreButton = wrapper.findAll('.game-wrap').at(0)!.find('.official-game-more')
        await moreButton.trigger('click')

        expect(mockGetGameLink).toHaveBeenCalledWith('group/link/normal')
        expect(mockWindowOpen).toHaveBeenCalledWith('processed-group/link/normal')
        expect(mockWindowFocus).toBeCalled()
      })

      it('open "all open" link when clicking the "All Open" button', async () => {
        const allOpenButton = wrapper.findAll('.game-wrap').at(0)!.find('.official-showall-btn')
        await allOpenButton.trigger('click')

        expect(mockGetGameLink).toHaveBeenCalledWith('all/link/normal')
        expect(mockWindowOpen).toHaveBeenCalledWith(`processed-all/link/normal`)
        expect(mockWindowFocus).toBeCalled()
      })
    })
  })
})