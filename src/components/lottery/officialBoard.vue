<script setup lang="ts">
import { ILotteryOfficial } from '@/types/lottery'
import { useI18n } from 'vue-i18n'
import { ElMessageBox } from 'element-plus'
import { useLotteryGameLink } from '@/composables/useLotteryGameLink'

const { getGameLink } = useLotteryGameLink()

const props = defineProps({
  officialData: {
    type: Array as PropType<ILotteryOfficial[]>,
    default: () => [],
    required: true
  },
  isLogin: {
    type: Boolean,
    default: false,
    required: true
  }
})

const { t } = useI18n()

const gameSeriesIcon = (key: string) => {
    let seriesIcon;
    switch (key) {
        case 'NORMAL':
            seriesIcon = 'offical-lotto-icon';
            break;
        case 'BBFO':
            seriesIcon = 'offical-fastbb-icon';
            break;
        case 'PKE5':
            seriesIcon = 'offical-pk115-icon';
            break;
        case 'TTQ3':
            seriesIcon = 'offical-fast3-icon';
            break;
        case 'KENO':
            seriesIcon = 'offical-keno-icon';
            break;
        case 'TF':
            seriesIcon = 'offical-happy10-icon';
            break;
        default:
            seriesIcon = '';
            break;
    }
    return seriesIcon;
}

const openLink = (url: string) => {
  if (props.isLogin === false) {
    ElMessageBox.alert(
      t('S_LOGIN_TIPS'), 
      '', 
      { center: true, showClose: false}
    )
    return
  }
  window.open(
    getGameLink(url),
  )?.focus()
}

</script>

<template>
    <div class="official-wrap">
        <template v-for="o_game in props.officialData" :key="o_game.groupName">
        <div class="game-wrap">
        <div :class="['icon', gameSeriesIcon(o_game.groupKey)]"></div>
        <!-- 群組名稱 -->
        <div>
            <div class="official-title">{{ o_game.groupName }}</div>
            <div class="official-game-list">
            <template v-for="(g,index) in o_game.games" :key="g.name">
                <!-- 遊戲名稱 -->
                <a v-if="index < 3" :class="g.isRecommand ? 'recommand': ''" @click="openLink(g.link)">
                {{ g.name }}
                </a>
            </template>
            </div>
        </div>
        <!-- ...的按鈕 -->
        <a
            class="official-game-more"
            @click="openLink(props.isLogin ? o_game.groupLink : '')"
        >...
        </a>
        <!-- 全開按鈕 -->
        <a
            v-if="o_game.games.length > 0"
            :title="t('S_FL_ALL_OPEM')"
            class="official-showall-btn"
            @click="openLink(props.isLogin ? o_game.link : '')"
        >
            {{ t('S_FL_ALL_OPEM') }}
        </a>
        </div>
        </template>
    </div>
</template>

<style lang="scss" scoped>

.official-wrap {
  display: flex;
  flex-wrap: wrap;
    
  .game-wrap {
      position: relative;
      width: 50%;
      padding: 15px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      border: 1px solid #ddd;
      background-color: rgb(246, 247, 249);

      .icon {
          width: 55px;
          height: 55px;
          background-size: 100% auto;
          margin-right: 8px;
      }

      .offical-lotto-icon {
        background: url('/client/static/image/hall/lottery/icon_lotto.png') 0 0 no-repeat;
      }

      .offical-fastbb-icon {
        background: url('/client/static/image/hall/lottery/icon_fastbb.png') 0 0 no-repeat;
      }

      .offical-pk115-icon {
        background: url('/client/static/image/hall/lottery/icon_pk&115.png') 0 0 no-repeat;
      }

      .offical-fast3-icon {
        background: url('/client/static/image/hall/lottery/icon_1010lotto&fast3.png') 0 0 no-repeat;
      }

      .offical-keno-icon {
        background: url('/client/static/image/hall/lottery/icon_keno.png') 0 0 no-repeat;
      }

      .offical-happy10-icon {
        background: url('/client/static/image/hall/lottery/icon_happy10.png') 0 0 no-repeat;
      }
      .official-title {
        margin: 5px 0;
        font-size: 1.3rem;
        font-weight: bold;
      }

      .official-game-list {
          margin-left: auto;

          a {
              margin-right: 8px;
              color: #999;
              font-size: 1rem;
              font-weight: bold;
              cursor: pointer;

              &.recommand {
                color: orange;
              }

              &::after {
                content: '|';
                margin: 0 5px;
              }

              &:last-child {
                &::after {
                  content: '';
                  display: none;
                }
              }

              &:hover {
                  text-decoration: underline;
              }
            }
        }

        .official-game-more {
          margin-top: auto;
          color: #999;
          cursor: pointer;
        }

        .official-showall-btn {
          margin-left: auto;
          padding: 8px 10px;
          color: #999;
          border: 1px solid #ddd;
          border-radius: 10px;
          cursor: pointer;
        }

      }
}

</style>