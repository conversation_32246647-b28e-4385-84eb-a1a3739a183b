import { mount, VueWrapper } from '@vue/test-utils'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import TraditionBoard from './traditionBoard.vue'
import { ILotteryTradition, ILotteryTimer } from '@/types/lottery'

vi.mock('vue-i18n', () => ({
  useI18n: () => ({
    t: vi.fn((key: string) => key),
  }),
}))

const mockGetGameLink = vi.fn((url: string) => `mocked_game_link_for_${url}`)
vi.mock('@/composables/useLotteryGameLink', () => ({
  useLotteryGameLink: () => ({
    getGameLink: mockGetGameLink,
  }),
}))

const mockWindowFocus = vi.fn()
const mockWindowOpen = vi.fn().mockReturnValue({ focus: mockWindowFocus })
global.open = mockWindowOpen

const defaultProps = {
  traditionList: [] as ILotteryTradition[],
  tranditionTimer: [] as ILotteryTimer[],
  serverTime: 1678886400,
  isLogin: false,
  unixTime: 1234567890,
}

const mockTraditionListItems: ILotteryTradition[] = [
  { name: 'Super Lotto', link: 'lotto/super', tag: 'hot', num: '100', ruleLink: 'rules/super_lotto', gameId: "123", openTimestamp: 1678886400, closeTimestamp: 1678886400, group: "lotto" },
  { name: 'Fast Keno', link: 'keno/fast', tag: 'new', num: '200', gameId: "456", openTimestamp: 1678886400, closeTimestamp: 1678886400, group: "lotto" },
  { name: 'Speedy SSC', link: 'ssc/speedy', tag: '', num: '300', ruleLink: 'rules/ssc_speedy', gameId: "789", openTimestamp: 1678886400, closeTimestamp: 1678886400, group: "lotto" }
]

const mockTranditionTimerItems: ILotteryTimer[] = [
  { time: '01:23:45', status: true },
  { time: '00:05:30', status: false },
  { time: '00:00:10', status: true },
]

const createWrapper = (props?: object): VueWrapper<any> => {
  return mount(TraditionBoard, {
    props: { ...defaultProps, ...props },
  })
}

describe('TraditionBoard', () => {
  let wrapper: VueWrapper<any>

  describe('Rendering', () => {
    it('should render an empty panel when traditionList is empty', () => {
      wrapper = createWrapper()

      expect(wrapper.find('.tradition-panel').exists()).toBe(true)
      expect(wrapper.findAll('.game-wrap').length).toBe(0)
    })

    it('should render game items when traditionList is provided', () => {
      wrapper = createWrapper({ traditionList: mockTraditionListItems })
      const gameWraps = wrapper.findAll('.game-wrap')

      expect(gameWraps.length).toBe(3)
      expect(gameWraps[0].find('.title').text()).toContain('Super Lotto')
      expect(gameWraps[1].find('.title').text()).toContain('Fast Keno')
      expect(gameWraps[2].find('.title').text()).toContain('Speedy SSC')
    })

    it('should display game number when isLogin is true', () => {
      wrapper = createWrapper({ traditionList: [mockTraditionListItems[0]], isLogin: true })
      
      expect(wrapper.find('.game-wrap .title .num').exists()).toBe(true)
      expect(wrapper.find('.game-wrap .title .num').text()).toBe('100')

      wrapper = createWrapper({ traditionList: [mockTraditionListItems[0]], isLogin: false })
      
      expect(wrapper.find('.game-wrap .title .num').exists()).toBe(false)
    })

    it('should display countdown timer when isLogin is true', () => {
      wrapper = createWrapper({ traditionList: [mockTraditionListItems[0]], tranditionTimer: [mockTranditionTimerItems[0]], isLogin: true })
      
      expect(wrapper.find('.game-wrap .countdown-time').exists()).toBe(true)
      expect(wrapper.find('.game-wrap .countdown-time span').text()).toBe('01:23:45')

      wrapper = createWrapper({ traditionList: [mockTraditionListItems[0]], isLogin: false })
      
      expect(wrapper.find('.game-wrap .countdown-time').exists()).toBe(false)
    })

    it('should display rule link icon if game.ruleLink is present', () => {
      wrapper = createWrapper({ traditionList: [mockTraditionListItems[0]] })
      
      expect(wrapper.find('.game-wrap .info').exists()).toBe(true)

      wrapper = createWrapper({ traditionList: [mockTraditionListItems[1]] })
      
      expect(wrapper.find('.game-wrap .info').exists()).toBe(false)
    })

    it('should apply correct class to countdown timer based on status', () => {
      wrapper = createWrapper({
        traditionList: [mockTraditionListItems[0], mockTraditionListItems[1]],
        tranditionTimer: [mockTranditionTimerItems[0], mockTranditionTimerItems[1]],
        isLogin: true,
      })
      
      const countdowns = wrapper.findAll('.countdown-time p')
      
      expect(countdowns[0].classes()).toContain('time-start')
      expect(countdowns[1].classes()).toContain('time-stop')
    })

     it('should render empty string for countdown time if tranditionTimer is empty', () => {
      wrapper = createWrapper({
        traditionList: [mockTraditionListItems[0]],
        tranditionTimer: [],
        isLogin: true,
      })
     
      const countdownSpan = wrapper.find('.countdown-time span')
     
      expect(countdownSpan.text()).toBe('')
    })
  })

  describe('tranditionGameStatus method', () => {
    it('should return empty string if tranditionTimer is empty', () => {
      wrapper = createWrapper({ traditionList: [mockTraditionListItems[0]], tranditionTimer: [] })
      
      expect(wrapper.vm.tranditionGameStatus(0)).toBe('')
    })

    it('should return "time-start" if timer status is true', () => {
      wrapper = createWrapper({ tranditionTimer: [{ time: '00:10', status: true }] })
      
      expect(wrapper.vm.tranditionGameStatus(0)).toBe('time-start')
    })

    it('should return "time-stop" if timer status is false', () => {
      wrapper = createWrapper({ tranditionTimer: [{ time: '00:00', status: false }] })
      
      expect(wrapper.vm.tranditionGameStatus(0)).toBe('time-stop')
    })
  })

  describe('Interactions', () => {
    describe('openGameLink method', () => {
      const gameToClick = mockTraditionListItems[0]

      it('not open link if not logged in', async () => {
        wrapper = createWrapper({ traditionList: [gameToClick], isLogin: false })
        const gameWrap = wrapper.find('.game-wrap')
        await gameWrap.trigger('click')

        expect(mockGetGameLink).not.toHaveBeenCalled()
        expect(mockWindowOpen).not.toHaveBeenCalled()
      })

      it('open game link and focus new window if logged in', async () => {
        wrapper = createWrapper({ traditionList: [gameToClick], isLogin: true })
        const gameWrap = wrapper.find('.game-wrap')
        await gameWrap.trigger('click')

        expect(mockGetGameLink).toHaveBeenCalledWith('lotto/super')
        expect(mockWindowOpen).toHaveBeenCalledWith('mocked_game_link_for_lotto/super')
        expect(mockWindowFocus).toHaveBeenCalled()
      })
    })

    describe('openRuleLink method', () => {
      const gameWithRuleLink = mockTraditionListItems[0]

      beforeEach(() =>  {
        mockGetGameLink.mockClear()
        mockWindowOpen.mockClear()
        mockWindowFocus.mockClear()
      })

      it('should open rule link', async () => {
        wrapper = createWrapper({ traditionList: [gameWithRuleLink] })
        const ruleIcon = wrapper.find('.game-wrap .info')
        await ruleIcon.trigger('click')

        expect(mockWindowOpen).toHaveBeenCalledWith(
          'rules/super_lotto',
          '',
          'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no'
        )
        expect(mockWindowFocus).toHaveBeenCalled()
        expect(mockGetGameLink).not.toHaveBeenCalled()
      })

      it('should stop propagation of click event to prevent openGameLink', async () => {
        wrapper = createWrapper({ traditionList: [gameWithRuleLink], isLogin: true })
        const ruleIcon = wrapper.find('.game-wrap .info')
        await ruleIcon.trigger('click')

        expect(mockGetGameLink).not.toHaveBeenCalled()
        expect(mockWindowOpen).toHaveBeenCalled()
      })
    })
  })
})