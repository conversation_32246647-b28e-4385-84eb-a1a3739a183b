<script setup lang="ts">
import { ILotteryTradition, ILotteryTimer} from '@/types/lottery'
import { useI18n } from 'vue-i18n'
import { ElMessageBox } from 'element-plus'
import { useLotteryGameLink } from '@/composables/useLotteryGameLink'

const { getGameLink } = useLotteryGameLink()

const props = defineProps({
  traditionList: {
    type: Array as PropType<ILotteryTradition[]>,
    default: () => [],
    required: true
  },
  tranditionTimer: {
    type: Array as PropType<ILotteryTimer[]>,
    default: () => [],
    required: true
  },
  serverTime: {
    type: Number,
    default: 0,
    required: true
  },
  isLogin: {
    type: Boolean,
    default: false,
    required: true
  },
  unixTime: {
    type: Number,
    default: 0,
    required: false
  }
})

const { t } = useI18n()

const tranditionGameStatus = (index: number) => {
  if (props.tranditionTimer.length === 0) {
      return ''
  }
  return props.tranditionTimer[index].status
      ? 'time-start'
      : 'time-stop'
}

const openGameLink = (url: string) => {
  if (props.isLogin === false) {
    ElMessageBox.alert(
      t('S_LOGIN_TIPS'), 
      '', 
      { center: true, showClose: false}
    )
    return
  }
  window.open(
    getGameLink(url)
  )?.focus()
}

const openRuleLink = (url: string) => {
  window.open(
    url,
    '',
    'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no'
  )?.focus()
}
</script>

<template>
    <div class="tradition-panel">
      <template v-for="(game,index) in props.traditionList" :key="game.gameName">
        <div class="game-wrap" @click="openGameLink(game.link)">
          <span
            class="tag"
            :style="game.tag ? `background:url(/client/static/image/hall/lottery/icon_${game.tag}.png?v=${props.unixTime}) 0 1px no-repeat` : ''"
          >
          </span>
          <div class="title">
            {{ game.name }}
            <p v-if="props.isLogin" class="num">{{ game.num }}</p>
          </div>
          <div v-if="props.isLogin" class="countdown-time">
            <p :class="tranditionGameStatus(index)">
              <span>{{ props.tranditionTimer.length === 0 ? '' : props.tranditionTimer[index].time }}</span>
            </p>
          </div>
          <div
            v-if="game.ruleLink"
            class="info"
            @click.stop="openRuleLink(game.ruleLink)"
          ></div>
        </div>
      </template>
    </div>
</template>

<style lang="scss" scoped>
  .tradition-panel {
    flex: 7;
    height: 40vh;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    
    .game-wrap {
      max-width: 50%;
      position: relative;
      height: 8vh;
      padding: 1.5vw;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px solid #eee;
      background: #fff;
      cursor: pointer;

      .tag {
        position: absolute;
        left: 0;
        top: 0;
        width: 30px;
        height: 30px;
      }

      .title {
        color: #333;
        font-size: 1.3rem;
        font-weight: bold;
      }

      .num {
        color: #999;
        margin-top: 5px;
        font-size: 1rem;
      }
      
      .info {
        margin-left: auto;
        &::after {
          content: '?';
          cursor: pointer;
          padding: 2px 8px;
          color: #777;
          border: 1px solid #777;
          border-radius: 50%;
        }
        &:hover {
          &::after {
            color: #69eece;
            border: 1px solid #69eece;
          }
        }
      }

      .countdown-time {
        margin-left: auto;
        .time-start {
          display: flex;
          align-items: center;
          width: 100px;
          height: 30px;
          background: url('/client/static/image/hall/lottery/icon_play.png') 0 0 no-repeat;
          background-size: 30% auto;
          span {
            margin-left: auto;
          }
        }

        .time-stop {
          display: flex;
          align-items: center;
          width: 100px;
          height: 30px;
          background: url('/client/static/image/hall/lottery/icon_stop.png') 0 0 no-repeat;
          background-size: 30% auto;
          color: #BABAC9;
          span {
            margin-left: auto;
          }
        }
      }

      &:hover {
        background: #f5f7f8;
      }
    }
  }
</style>