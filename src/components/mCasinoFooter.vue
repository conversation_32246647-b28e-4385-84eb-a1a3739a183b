<script setup lang="ts">
import { Up } from '@icon-park/vue-next'
import { useEjpStore } from '@/stores/ejpremote'
import { storeToRefs } from 'pinia'

const ejpStore = useEjpStore()
const { jpAmount, topList, isShowJackpot } = storeToRefs(ejpStore)
const showRanklist = ref(false)
const scollTop = () => {
  document.querySelector('#mcasino')!.scrollTo({ top: 0, behavior: 'smooth' })
}
</script>

<template>
  <div v-if="isShowJackpot" class="m-casino-footer">
    <div class="backtop-btn" @click="scollTop()">
      <up theme="outline" size="20" fill="#ffe599" :stroke-width="8"/>
      <span>Back TO TOP</span>
    </div>
    <div class="jpamount" @click="showRanklist = true">
      <div class="grand"></div>
      <span>{{ jpAmount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
    </div>
    <RankDialog v-model="showRanklist" :top-list="topList" />
  </div>
</template>

<style lang="scss" scoped>
  .m-casino-footer {
    width: 100%;
    height: 40px;
    position: fixed;
    bottom: 0;
    left: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 5px solid var(--main);
    color: #ffe599;
    font-weight: bold;
    z-index: 1;

    .backtop-btn, 
    .jpamount {
      width: 50%;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .backtop-btn {
      background: linear-gradient(45deg, var(--text-active), var(--main));
      cursor: pointer;
    }

    .jpamount {
      position: relative;
      background: #9B3234;
      border-top: 1px solid #555;
      box-shadow: 0px 3px 5px -5px #222 inset;  
      cursor: pointer;

      .grand {
        width: 60px;
        height: 16px;
        background: url('@/assets/jp_grand.png') no-repeat 0 0;
        background-size: 100%;
        z-index: 1;
      }

      span {
        background: #333;
        padding: 5px;
        margin-left: 5px;
      }

      &:before {
        content: '';
        width: 3.5vw;
        height: 40px;
        position: absolute;
        left: -8px;
        top: 0;
        transform: skew(-20deg, 0deg );
        background: #9B3234;
        border-left: 1px solid #555;
        box-shadow: 3px 0px 6px -5px #222 inset;
      }
    }
  }
</style>