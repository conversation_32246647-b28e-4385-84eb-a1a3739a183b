import { describe, expect, it } from 'vitest'
import { mount } from '@vue/test-utils'
import { createI18n } from 'vue-i18n'
import GameMaintainTooltip from './GameMaintainTooltip.vue'

const i18n = createI18n({
  locale: 'en',
  messages: {
    en: {
      S_GAME_MAINTAIN: 'Game is under maintenance'
    }
  }
})

describe('GameMaintainTooltip', () => {
  it('renders the maintain message correctly', async () => {
    const maintainMessage = '<p>Maintenance in progress</p>'
    const wrapper = mount(GameMaintainTooltip, {
      global: {
        plugins: [i18n]
      },
      props: {
        maintainMessage,
      }
    })
    
    expect(wrapper.find('.maintain-text').text()).toBe('Game is under maintenance')
    expect(wrapper.find('span').exists()).toBe(true)

    const tooltip = wrapper.findComponent({name: 'el-tooltip'})
    expect(tooltip.props('content')).toContain(maintainMessage)
  })

  it('renders without maintainMessage prop', () => {
    const wrapper = mount(GameMaintainTooltip, {
      global: {
        plugins: [i18n]
      },
      props: {
        maintainMessage: undefined
      }
    })

    expect(wrapper.find('.maintain-text').text()).toBe('Game is under maintenance')
    expect(wrapper.find('span').exists()).toBe(true)

    const tooltip = wrapper.findComponent({name: 'el-tooltip'})
    expect(tooltip.props('content')).toBe('')
  })
})