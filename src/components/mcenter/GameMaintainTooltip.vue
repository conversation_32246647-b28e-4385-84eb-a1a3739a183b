<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
  maintainMessage: {
    type: String,
    required: true,
    default: '',
  }
})

</script>

<template>
  <el-tooltip
    :content="props.maintainMessage"
    placement="top"
    raw-content
  >
    <span class="maintain-text">{{ t('S_GAME_MAINTAIN') }}</span>
  </el-tooltip>
</template>

<style lang="scss" scoped>
.maintain-text {
  color: #ff5656;
  &:before {
    content: '!';
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background: #ff5656;
    color: #fff;
    display: inline-block;
    text-align: center;
    line-height: 15px;
    margin-right: 5px;
  }
}
</style>