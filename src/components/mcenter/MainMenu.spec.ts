import { mount } from '@vue/test-utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import MainMenu from '@/components/mcenter/MainMenu.vue'
import { createI18n } from 'vue-i18n'

const i18n = createI18n({
  locale: 'en',
  messages: {
    en: {
      S_DAW_ACC: 'Account',
      S_BALANCE: 'Balance',
      'Menu 1': 'Menu 1',
      'Menu 2': 'Menu 2',
      'Menu 3': 'Menu 3',
    },
  },
})

vi.mock('@/stores/account', () => ({
  useAccountInfoStore: () => ({
    accountInfo: ref({
      username: 'test_account',
      balance: 100,
      currency: 'USD'
    })
  }),
}))

vi.mock('@/config/mcenterMenu', () => ({
  getMenuList: () => [
    { name: 'menu1', label: 'Menu 1', icon: 'el-icon-menu' },
    { name: 'menu2', label: 'Menu 2', icon: 'el-icon-menu' },
    { name: 'menu3', label: 'Menu 3', icon: 'el-icon-menu' },
  ],
}))

vi.mock('@/composables/useImportTheme', () => ({
  useImportTheme: () => ({
    importStyle: vi.fn(),
  }),
}))

describe('MenuComponent', () => {
  let wrapper: any

  beforeEach(async () => {
    wrapper = mount(MainMenu, {
      global: {
        plugins: [i18n],
      },
      props: {
        menuItemSelected: 'menu1',
        menuItemHover: 'menu1',
      }
    })
    await wrapper.vm.$nextTick()
  })

  it('should render menu items correctly', () => {
    const menuItems = wrapper.findAll('li')
    expect(menuItems).toHaveLength(3)
    expect(menuItems[0].text()).toContain('Menu 1')
    expect(menuItems[1].text()).toContain('Menu 2')
    expect(menuItems[2].text()).toContain('Menu 3')

    expect(menuItems[0].classes()).toContain('active')
    expect(menuItems[1].classes()).not.toContain('active')
    expect(menuItems[2].classes()).not.toContain('active')
  })

  it('should highlight the selected menu item on click', async () => {
    const menuItems = wrapper.findAll('li')
    await menuItems[1].trigger('click')
    expect(menuItems[1].classes()).toContain('active')
    expect(menuItems[0].classes()).not.toContain('active')
    expect(menuItems[2].classes()).not.toContain('active')
    expect(wrapper.vm.menuItemSelected).toBe('menu2')
  })

  it('hovered and actived menu item shoule be highlighted', async () => {
    const menuItems = wrapper.findAll('li')
    await menuItems[1].trigger('mouseover')
    expect(menuItems[1].classes()).toContain('active')
    expect(menuItems[0].classes()).toContain('active')
    expect(menuItems[2].classes()).not.toContain('active')
    expect(wrapper.vm.menuItemHover).toBe('menu2')
  })

  it('should display user information correctly', () => {
    const userinfo = wrapper.find('.userinfo')
    expect(userinfo.text()).toContain('test_account')
    expect(userinfo.text()).toContain('100 USD')
  })
})

