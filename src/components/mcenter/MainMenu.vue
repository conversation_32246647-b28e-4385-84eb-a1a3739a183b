<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useAccountInfoStore } from '@/stores/account'
import { getMenuList } from '@/config/mcenterMenu'
import { useImportTheme } from '@/composables/useImportTheme'
import { storeToRefs } from 'pinia'

const menuItemSelected = defineModel('menuItemSelected', {
  required: true,
  type: String,
  default: '',
})

const menuItemHover = defineModel('menuItemHover', {
  required: true,
  type: String,
  default: '',
})

const { t } = useI18n()
const accountInfoStore = useAccountInfoStore()
const { accountInfo } = storeToRefs(accountInfoStore)
const { importStyle } = useImportTheme()

const menuList = getMenuList()

const onMenuHover = (menu: string) => {
  menuItemHover.value = menu
}

const onMenuClick = (menu: string) => {
  menuItemSelected.value = menu
  menuItemHover.value = menu
}

importStyle('mcenter/mainMenu')
</script>

<template>
  <div class="menu">
    <ul>
      <li
        v-for="item in menuList"
        :key="item.name"
        :class="menuItemHover === item.name || menuItemSelected === item.name ? 'active' : ''"
        @click="onMenuClick(item.name)"
        @mouseover="onMenuHover(item.name)"
      >
        <component :is="item.icon" theme="outline" size="24" fill="#ddd" />{{ t(item.label) }}
      </li>
    </ul>
    <div class="userinfo">
      <div>
        <span>{{ `${t('S_DAW_ACC')} : ` }}</span>
        <span class="content">{{ accountInfo.username || '' }}</span>
      </div>
      <div>
        <span>{{ `${t('S_BALANCE')} : ` }}</span>
        <span class="content">{{ `${accountInfo.balance?.toLocaleString() || 0} ${accountInfo.currency || ''}` }}</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.menu > ul {
  display: flex;
  justify-content: center;
  color: var(--mcenter-mainMenu-userinfo-menu-ul-text);

  li {
    width: 130px;
    padding: 15px;
    display: flex;
    text-align: center;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    &.active, &:hover {
      color: var(--mcenter-mainMenu-userinfo-menu-active);
      font-weight: 600;

      .i-icon {
        :global(path), :global(circle) {
          stroke: var(--mcenter-mainMenu-userinfo-menu-active);
        }
      }
    }
  }
}

.menu {
  font-size: 16px;
  background: var(--mcenter-mainMenu-userinfo-menu-bg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #353535;
  .userinfo {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-right: 15px;
    color: var(--mcenter-text);

    .content {
      color: var(--mcenter-mainMenu-userinfo-content-text);
    }
  }
  .i-icon {
    margin-right: 5px;
  }
}
</style>