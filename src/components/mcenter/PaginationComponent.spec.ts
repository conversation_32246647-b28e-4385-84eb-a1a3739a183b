import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import PaginationComponent from '@/components/mcenter/PaginationComponent.vue'

describe('PaginationComponent', () => {
  it('should render correctly with given pageCount', () => {
    const wrapper = mount(PaginationComponent, {
      props: {
        pageCount: 10,
      },
    })

    const pagination = wrapper.findComponent({ name: 'ElPagination' })
    expect(pagination.exists()).toBe(true)
    expect(pagination.props('pageCount')).toBe(10)
  })

  it('should emit currentChange event when page is changed', async () => {
    const wrapper = mount(PaginationComponent, {
      props: {
        pageCount: 10,
      },
    })

    const pagination = wrapper.findComponent({ name: 'ElPagination' })
    await pagination.findAll('li').at(1)?.trigger('click')

    expect(wrapper.emitted('currentChange')).toBeDefined()
    expect(wrapper.emitted('currentChange')?.[0]).toEqual([2])
  })
})
