<script setup lang="ts">
const emit = defineEmits(['currentChange'])

const currentPage = defineModel('currentPage', {
  type: [Number, undefined] as PropType<number | undefined>,
  default: 1
})

const props = defineProps({
  pageCount: {
    type: Number,
    required: true,
    default: 0
  }
})
</script>

<template>
  <div class="pagination">
    <el-pagination
      v-model:current-page="currentPage"
      :page-count="props.pageCount"
      background
      size="small"
      layout="prev, pager, next"
      class="mt-4"
      @current-change="emit('currentChange', $event)"
    />
  </div>
</template>

<style lang="scss" scoped>
.pagination {
  display: flex;
  justify-content: center;
  margin: 5px 0;
}
</style>
