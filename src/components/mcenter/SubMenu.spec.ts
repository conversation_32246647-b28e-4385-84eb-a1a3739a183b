import { mount } from '@vue/test-utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import SubMenu from './SubMenu.vue'
import { createI18n } from 'vue-i18n'
import { createRouter, createWebHistory } from 'vue-router'
import { getMainMenuBySubMenu } from '@/config/mcenterMenu'

const i18n = createI18n({
  locale: 'en',
  messages: {
    en: {
      'Sub Menu 1': 'Sub Menu 1',
      'Sub Menu 2': 'Sub Menu 2',
    },
  },
})

const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', name: 'home', component: { template: '<div>Home</div>' } },
    { path: '/subMenu1', name: 'subMenu1', component: { template: '<div>Sub Menu 1</div>' } },
    { path: '/subMenu2', name: 'subMenu2', component: { template: '<div>Sub Menu 2</div>' } }
  ]
})

vi.mock('@/config/mcenterMenu', () => ({
  getMenu: vi.fn().mockReturnValue({
    subMenu: [
      { name: 'subMenu1', label: 'Sub Menu 1' },
      { name: 'subMenu2', label: 'Sub Menu 2' },
    ],
  }),
  getMainMenuBySubMenu: vi.fn().mockReturnValue({ name: 'mainMenu' }),
}))

vi.mock('@/composables/useImportTheme', () => ({
  useImportTheme: () => ({
    importStyle: vi.fn(),
  }),
}))

describe('SubMenuComponent', () => {
  let wrapper: any
  const mainMenuHover = 'mainMenu'

  beforeEach(() => {
    wrapper = mount(SubMenu, {
      global: {
        plugins: [router, i18n],
      },
      props: {
        mainMenuHover,
        mainMenuItemSelected: 'mainMenu',
        subMenuItemSelected: 'subMenu1',
      },
    })
  })

  it('should render sub menu items correctly', () => {
    const subMenuItems = wrapper.findAll('li')
    expect(subMenuItems).toHaveLength(2)
    expect(subMenuItems[0].text()).toContain('Sub Menu 1')
    expect(subMenuItems[1].text()).toContain('Sub Menu 2')

    expect(subMenuItems[0].classes()).toContain('active')
    expect(subMenuItems[1].classes()).not.toContain('active')
  })

  it('should highlight the selected sub menu item on click', async () => {
    const subMenuItems = wrapper.findAll('li')
    await subMenuItems[1].trigger('click')
    expect(subMenuItems[1].classes()).toContain('active')
    expect(wrapper.vm.subMenuItemSelected).toBe('subMenu2')
    expect(wrapper.vm.mainMenuItemSelected).toBe('mainMenu')
  })

  it('should navigate to the correct route on menu click', async () => {
    vi.spyOn(router, 'push').mockImplementation(vi.fn())
    
    const subMenuItems = wrapper.findAll('li')
    await subMenuItems[1].trigger('click')
    expect(router.push).toHaveBeenCalledWith({ name: 'subMenu2' })
  })

  it('should import the correct style', () => {
    expect(wrapper.vm.importStyle).toHaveBeenCalledWith('mcenter/subMenu')
  })
  
  it('should get default main menu item', async () => {
    vi.mocked(getMainMenuBySubMenu).mockReturnValue(undefined)

    const subMenuItems = wrapper.findAll('li')
    await subMenuItems[1].trigger('click')
    expect(router.push).toHaveBeenCalledWith({ name: 'subMenu2' })
    expect(wrapper.vm.mainMenuItemSelected).toBe('record')
  })
})
