<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { getMenu, getMainMenuBySubMenu } from '@/config/mcenterMenu'
import { useImportTheme } from '@/composables/useImportTheme'

const { importStyle } = useImportTheme()
const { t } = useI18n()
const router = useRouter()

const props = defineProps({
  mainMenuHover: {
    type: String,
    required: true,
    default: '',
  },
})

const mainMenuItemSelected = defineModel('mainMenuItemSelected', {
  required: true,
  type: String,
  default: '',
})

const subMenuItemSelected = defineModel('subMenuItemSelected', {
  required: false,
  type: String,
  default: '',
})

const onMenuClick = (menu: string) => {
  subMenuItemSelected.value = menu
  mainMenuItemSelected.value = getMainMenuBySubMenu(menu)?.name || 'record'

  router.push({ name:menu })
}
importStyle('mcenter/subMenu')
</script>

<template>
  <div class="sub-menu">
    <ul>
      <li
        v-for="item in getMenu(props.mainMenuHover)?.subMenu"
        :key="item.name"
        :class="mainMenuItemSelected === props.mainMenuHover && subMenuItemSelected === item.name ? 'active' : ''"
        @click="onMenuClick(item.name)"
      >
        {{ t(item.label) }}
      </li>
    </ul>
  </div>
</template>

<style lang="scss" scoped>
.sub-menu > ul {
  font-size: 15px;
  display: flex;
  justify-content: center;
  background: var(--mcenter-subMenu-userinfo-sub-menu-bg);
  color: var(--mcenter-subMenu-userinfo-sub-menu-text);

  li {
    width: 130px;
    padding: 15px;
    display: flex;
    text-align: center;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    &.active {
      color: var(--mcenter-subMenu-userinfo-sub-menu-active-text);
      font-weight: 600;
    }
    &:hover {
      color: var(--mcenter-subMenu-userinfo-sub-menu-active-text);
      font-weight: 600;
    }
  }
}
</style>

