import { mount } from '@vue/test-utils';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import TopHeader from './TopHeader.vue';
import { createI18n } from 'vue-i18n';

const messages = {
  en: {
    S_MEM_CENTER: 'Member Center',
    S_TIME_OF_EAST_US: 'New York Time'
  },
};

const i18n = createI18n({
  locale: 'en',
  messages,
});

const mockDate = '2024/12/11 - 10:00:00';

vi.mock('dayjs', async () => {
  return {
    default: {
      tz: vi.fn().mockImplementation(() => ({
        format: vi.fn().mockReturnValue(mockDate),
      })),
    },
  }
})

describe('TopHeader', () => {
  let wrapper: any;

  beforeEach(() => {
    wrapper = mount(TopHeader, {
      global: {
        plugins: [i18n],
      },
    });
  });

  it('test_title', () => {
    expect(wrapper.find('.pagetitle').text()).toBe('Member Center');
  });

  it('test_datetime', async () => {
    expect(wrapper.find('.datetime').text()).toBe(`New York Time：${mockDate}`);
  });
});
