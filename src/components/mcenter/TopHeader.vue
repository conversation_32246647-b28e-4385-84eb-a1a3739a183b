<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { getTodayWithFormat } from '@/utils/date'

const { t } = useI18n()

const currentNyDate = ref(getTodayWithFormat('YYYY/MM/DD - HH:mm:ss'))

const timeCounter = setInterval(() => {
  currentNyDate.value = getTodayWithFormat('YYYY/MM/DD - HH:mm:ss')
}, 1000)

onBeforeUnmount(() => {
  clearInterval(timeCounter)
})
</script>

<template>
  <div class="top">
    <span class="pagetitle">{{ t('S_MEM_CENTER') }}</span>
    <div class="datetime">{{ t('S_TIME_OF_EAST_US')+'：' + currentNyDate }}</div>
  </div>
</template>

<style scoped>
.top {
  display: flex;
  background: #000;
  justify-content: space-between;
  .pagetitle {
    padding: 10px 35px;
    color: var(--mcenter-text);
  }
  .datetime {
    padding: 10px 35px;
    color: var(--mcenter-text);
  }
}
</style>