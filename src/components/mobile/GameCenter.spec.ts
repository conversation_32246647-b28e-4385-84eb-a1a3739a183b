import { mount } from '@vue/test-utils'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { ref } from 'vue'
import { setActivePinia, createPinia } from 'pinia'
import GameCenter from './GameCenter.vue'
import { GameType } from '@/types/game'

const mockGetImageUrl = vi.fn((name) => `mock-url/${name}`)
const mockAddRecentData = vi.fn()
const mockImportStyle = vi.fn()
const mockAccountInfo = ref({ isLogin: false, username: '' })
const mockGameList = [
  { name: 'Game 1', link: 'url1', gameKind: GameType.Casino, gameId: 501, icon: 'icon1' },
  { name: 'Game 2', link: 'url2', gameKind: GameType.Battle, gameId: 6601, icon: 'icon2' },
  { name: 'Game 3', link: 'url3', gameKind: GameType.Fishmaster, gameId: 3801, icon: 'icon3' },
]

vi.mock('vue-i18n', () => ({
  useI18n: () => ({
    t: vi.fn((key) => key)
  })
}))

vi.mock('@/composables/useGetImgUrl', () => ({
  useGetImgUrl: () => ({
    getImageUrl: mockGetImageUrl
  })
}))

vi.mock('@/stores/mobilebasic', () => ({
  useMobileBasicInfoStore: () => ({
    siteInfo: ref({ unitTime: '12345' })
  })
}))

vi.mock('@/stores/config', () => ({
  useConfigStore: () => ({
    cdnUrl: ref('https://mock-cdn.com')
  })
}))

vi.mock('@/stores/account', () => ({
  useAccountInfoStore: () => ({
    accountInfo: mockAccountInfo,
  }),
}))

vi.mock('@/stores/recentPlay', () => ({
  useRecentlyPlay: () => ({
    addRecentData: mockAddRecentData,
  }),
}))

vi.mock('@/composables/useImportTheme', () => ({
  useImportTheme: () => ({
    importStyle: mockImportStyle,
  }),
}))

vi.mock('@/components/GamePic.vue', () => ({
  default: {
    name: 'GamePic',
    template: '<div class="stub-game-pic"></div>',
  }
}))

vi.mock('@/components/mobile/historyBoard.vue', () => ({
  default: {
    name: 'HistoryBoard',
    template: '<div class="stub-history-board"></div>',
  }
}))

vi.mock('@/components/mCasinoFooter.vue', () => ({
  default: {
    name: 'MCasinoFooter',
    template: '<div class="stub-mcasino-footer"></div>',
  }
}))

const mockWindowOpen = vi.fn()
global.open = mockWindowOpen

describe('GameCenter', () => {
  let wrapper

  const mountComponent = (props = {}, provideOptions = { nowPage: 'casino' }) => {
    return mount(GameCenter, {
      props: {
        gameList: mockGameList,
        ...props,
      },
      global: {
        provide: {
          nowPage: provideOptions.nowPage,
        },
      },
    })
  }

  beforeEach(() => {
    setActivePinia(createPinia())
    mockAccountInfo.value = { isLogin: false, username: '' }
  })

  describe('Rendering', () => {
    it('renders game list correctly', () => {
      wrapper = mountComponent()
      const gameBoxes = wrapper.findAll('.game-box')
      const gamePic = wrapper.findAllComponents({ name: 'GamePic' })
      
      expect(gameBoxes.length).toBe(3)
      expect(gamePic.length).toBe(3)
      expect(gameBoxes[0].find('.game-name').text()).toBe('Game 1')
      expect(gameBoxes[1].find('.game-name').text()).toBe('Game 2')
      expect(gameBoxes[2].find('.game-name').text()).toBe('Game 3')
    })

    it('applies "casino" class to m-wrap if currentPage is "casino"', () => {
      wrapper = mountComponent({}, { nowPage: 'casino' })
      expect(wrapper.find('.m-wrap').classes()).toContain('casino')
      expect(wrapper.find('.stub-mcasino-footer').exists()).toBe(true)
    })

    it('does not apply "casino" class to m-wrap if currentPage is not "casino"', () => {
      wrapper = mountComponent({}, { nowPage: 'fishing' })
      expect(wrapper.find('.m-wrap').classes()).not.toContain('casino')
      expect(wrapper.find('.stub-mcasino-footer').exists()).toBe(false)
    })

    it('calls importStyle on setup', () => {
      mountComponent()
      expect(mockImportStyle).toHaveBeenCalledWith('mobile/mobileGames')
    })
  })

  describe('openGame method', () => {
    beforeEach(() => {
      mockWindowOpen.mockClear()
      mockAddRecentData.mockClear()
    })

    it('user is not logged in', async () => {
      wrapper = mountComponent()
      mockAccountInfo.value.isLogin = false
      await wrapper.vm.$nextTick()

      const firstGameBox = wrapper.find('.game-box')
      await firstGameBox.trigger('click')

      expect(mockWindowOpen).not.toBeCalled()
      expect(mockAddRecentData).not.toBeCalled()
    })

    it('opens game and adds to recent', async () => {
      wrapper = mountComponent()
      mockAccountInfo.value.isLogin = true
      await wrapper.vm.$nextTick()

      const firstGameBox = wrapper.findAll('.game-box')[0]
      await firstGameBox.trigger('click')

      expect(mockWindowOpen).toHaveBeenCalledWith('url1', '', 'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no')
      expect(mockAddRecentData).toBeCalled()
    })

    it('opens game and does not add to recent', async () => {
      wrapper = mountComponent()
      mockAccountInfo.value.isLogin = true
      await wrapper.vm.$nextTick()

      const secondGameBox = wrapper.findAll('.game-box')[1]
      await secondGameBox.trigger('click')

      expect(mockWindowOpen).toHaveBeenCalledWith('url2', '', 'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no')
      expect(mockAddRecentData).not.toBeCalled()
    })
  })

  describe('historyBoard rendering', () => {
    it('shows historyBoard if logged in and currentPage is "casino"', async () => {
      mockAccountInfo.value.isLogin = true
      wrapper = mountComponent({}, { nowPage: 'casino' })
      await wrapper.vm.$nextTick()
      expect(wrapper.find('.stub-history-board').exists()).toBe(true)
    })

    it('shows historyBoard if logged in and currentPage is "fishing"', async () => {
      mockAccountInfo.value.isLogin = true
      wrapper = mountComponent({}, { nowPage: 'fishing' })
      await wrapper.vm.$nextTick()
      expect(wrapper.find('.stub-history-board').exists()).toBe(true)
    })

    it('hides historyBoard if not logged in', async () => {
      mockAccountInfo.value.isLogin = false
      wrapper = mountComponent({}, { nowPage: 'casino' })
      await wrapper.vm.$nextTick()
      expect(wrapper.find('.stub-history-board').exists()).toBe(false)
    })

    it('hides historyBoard if currentPage is not "casino" or "fishing"', async () => {
      mockAccountInfo.value.isLogin = true
      wrapper = mountComponent({}, { nowPage: 'homepage' })
      await wrapper.vm.$nextTick()
      expect(wrapper.find('.stub-history-board').exists()).toBe(false)
    })
  })
})
