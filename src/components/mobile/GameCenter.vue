<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useGetImgUrl } from '@/composables/useGetImgUrl'
import { useMobileBasicInfoStore } from "@/stores/mobilebasic"
import { useConfigStore } from '@/stores/config'
import type { IGameInfo } from '@/types/game'
import { useAccountInfoStore } from '@/stores/account'
import { GameType } from '@/types/game'
import { useRecentlyPlay } from "@/stores/recentPlay"
import { useImportTheme } from '@/composables/useImportTheme'
import GamePic from '@/components/GamePic.vue'
import { ElMessageBox } from 'element-plus'
import { storeToRefs } from 'pinia'
import historyBoard from '@/components/mobile/historyBoard.vue'

const props = defineProps({
  gameList: {
    type: Array as PropType<IGameInfo[]>,
    default: () => [],
    required: true
  }
})

const { t } = useI18n()
const recentPlayStore = useRecentlyPlay()
const { getImageUrl } = useGetImgUrl()
const mobileBasicStore = useMobileBasicInfoStore()
const accountInfoStore = useAccountInfoStore()
const configStore = useConfigStore()
const { siteInfo } = storeToRefs(mobileBasicStore)
const { accountInfo } = storeToRefs(accountInfoStore)
const { cdnUrl } = storeToRefs(configStore)
const currentPage = inject('nowPage') as string
const { importStyle } = useImportTheme()

const isShowHistoryBoard = computed(() => {
  const result = accountInfo.value.isLogin && ['casino', 'fishing'].includes(currentPage)
  return result
})

const openGame = (url: string, game: IGameInfo) => {
  if (!accountInfo.value.isLogin) {
    ElMessageBox.alert(
      t('S_LOGIN_TIPS'),
      '',
      { center: true, showClose: false}
    )
    return
  }

  window.open(url, '', 'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no')

  if (game.gameKind !== GameType.Battle) {
    recentPlayStore.addRecentData(game)
  }
}

importStyle('mobile/mobileGames')
</script>

<template>
  <div id="mcasino" class="m-wrap" :class="[ currentPage === 'casino' ? 'casino' : '' ]">
    <div class="current-games">
      <template v-for="game in props.gameList" :key="game.name">
        <div class="game-box" @click="openGame(game.link, game)">
          <GamePic
            :game-pic="`${cdnUrl}/client/static/image/gamePicture/mobile/` + `${game.gameKind}/${game.gameId}.png?v=${siteInfo.unitTime}`"
            :failed-pic="getImageUrl('mobile_failed.png')"
            class="game-image"
          />
          <span class="game-name">{{ game.name }}</span>
        </div>
      </template>
    </div>
    <historyBoard v-if="isShowHistoryBoard" />
    <MCasinoFooter v-if="currentPage === 'casino'"/>
  </div>
</template>

<style lang="scss" scoped>
  .m-wrap {
    width: 100%;
    overflow-y: scroll;
    padding-bottom: 70px;
    display: flex;
    align-items: start;
    flex-wrap: wrap;
    &.casino {
      height: 70vh;
    }

    .menu-bar {
      width: 100%;
      .main {
        display: flex;
        .main-menu-item {
          margin: 10px;
          &:hover {
            background: var(--main-5);
          }
          &.active {
            background: var(--main-5);
          }
        }
      }
    }
    .menu-sub {
      width: 100%;
      display: flex;
      .sub-menu-item {
        margin: 10px;
        &:hover {
          color: var(--text-active);
        }
        &.active {
          color: #fff;
          background: var(--text-active);
          border: 1px solid var(--text-active);
          &:hover {
            color: #fff;
          }
        }
      }
    }
    .current-games {
      width: 100%;
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
      place-items: baseline;
      gap: 10px;
      padding: 10px;
    }

    .game-box {
      cursor: pointer;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 5px;

      .game-image {
        width: 60px;
        height: auto;
      }

      .game-name {
        width: 60px;
        color: var(--mobile-mobileGames-game-name-text);
        display: block;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
</style>