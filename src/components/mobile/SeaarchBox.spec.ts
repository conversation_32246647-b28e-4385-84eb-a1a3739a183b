import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import SearchBox from './SearchBox.vue'
import { createI18n } from 'vue-i18n'
import { Search } from '@icon-park/vue-next'

const i18n = createI18n({
  locale: 'en',
  messages: {
    en: {
      S_ENTER_GAME_NAME: 'Enter game name',
    },
  },
})

describe('SearchBox', () => {
  it('renders correctly', () => {
    const wrapper = mount(SearchBox, {
      global: {
        plugins: [i18n],
      },
      props: {
        modelValue: '',
      },
    })

    expect(wrapper.exists()).toBe(true)

    const input = wrapper.find('input')
    expect(input.exists()).toBe(true)
    expect(input.attributes('placeholder')).toBe('Enter game name')

    const search = wrapper.findComponent(Search)
    expect(search.exists()).toBe(true)
  })

  it('emits the correct data when input changes', async () => {
    const wrapper = mount(SearchBox, {
      global: {
        plugins: [i18n],
      },
      props: {
        modelValue: '',
      },
    })

    const input = wrapper.find('input')
    await input.setValue('test')
    expect(wrapper.emitted('update:modelValue')).toEqual([['test']]);
  })
})
