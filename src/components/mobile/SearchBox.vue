<script setup lang="ts">
import { Search } from '@icon-park/vue-next'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const data = defineModel({
  type: String,
  required: true,
  default: ''
});
</script>

<template>
  <div>
    <el-input
      v-model.trim="data"
      class="search-input"
      :placeholder="t('S_ENTER_GAME_NAME')"
      clearable
    >
      <template #prefix>
        <Search
          theme="outline"
          size="12"
          :fill="'var(--icon-bg)'"
        />
      </template>
    </el-input>
  </div>
</template>