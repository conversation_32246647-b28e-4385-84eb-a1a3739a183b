import { mount } from '@vue/test-utils'
import { setActive<PERSON><PERSON>, createPinia } from 'pinia'
import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest'
import { nextTick } from 'vue'
import { useAccountInfoStore } from '@/stores/account'
import UserInfo from "./UserInfo.vue"

vi.mock('@/stores/account', () => ({
  useAccountInfoStore: vi.fn()
}))

describe('UserInfo component', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  afterEach(() => {
    vi.resetAllMocks();
  })

  it('test_isLogin_true', async () => {
    vi.mocked(useAccountInfoStore).mockReturnValue({
      accountInfo: ref({
        alias: 'testUser',
        balance: 100,
        bankrupt: false,
        block: false,
        currency: 'CNY',
        enable: true,
        test: false,
        username: 'testUser',
        isLogin: true
      })
    } as any)

    const wrapper = mount(UserInfo)

    await nextTick()
    await wrapper.vm.$nextTick();

    expect(wrapper.find('.account-name').text()).toBe('testUser')
    expect(wrapper.find('.bbin-balance').text()).toBe('100')
  })

  it('test_isLogin_false', async () => {
    vi.mocked(useAccountInfoStore).mockReturnValue({
      accountInfo: ref({ username: 'ballguest', isLogin: false })
    } as any)

    const wrapper = mount(UserInfo);

    await nextTick()
    await wrapper.vm.$nextTick();

    expect(wrapper.find('.account-name').text()).toBe('ballguest')
    expect(wrapper.find('.bbin-balance').exists()).toBe(false)
  })
})