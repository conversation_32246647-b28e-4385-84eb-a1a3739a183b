<script setup lang="ts">
import { useAccountInfoStore } from "@/stores/account";
import { ElAvatar } from "element-plus"
import { storeToRefs } from 'pinia'

const accountInfoStore = useAccountInfoStore()
const { accountInfo } = storeToRefs(accountInfoStore)

</script>

<template>
  <div class="user-info">
    <span class="account-name">{{ accountInfo.username }}</span>
    <span v-if="accountInfo.isLogin" class="bbin-balance">{{ accountInfo.balance.toLocaleString() }}</span>
  </div>
  <ElAvatar
    class="avatar"
    src="/client/site/default/image/user.png"
  />
</template>

<style lang="scss" scoped>
.user-info {
  display: flex;
  flex-direction: column;
  position: relative;
  left: -50px;
  span {
    display: block;
  }
  .account-name {
    color: var(--text-active);
  }

  .bbin-balance {
    color: #fff;
  }
}

.avatar {
  margin-left: 10px;
  position: fixed;
  top: 5px;
  right: 10px;
  border: 2px solid var(--main);
  box-shadow: 1px 1px 5px 1px #0000001f;
}
</style>