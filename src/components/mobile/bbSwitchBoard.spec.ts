import { mount, VueWrapper } from '@vue/test-utils'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { ref } from 'vue'
import BbSwitchBoard from './bbSwitchBoard.vue'

const mockRouterPush = vi.fn()
const mockCurrentRouteName = ref('default')
vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: mockRouterPush,
  }),
  useRoute: () => ({
    name: mockCurrentRouteName.value,
  })
}))

const mockImportStyle = vi.fn()
vi.mock('@/composables/useImportTheme', () => ({
  useImportTheme: () => ({
    importStyle: mockImportStyle,
  }),
}))

const mockCasinoListItems = [
  { key: 'game', name: 'Casino Games' },
  { key: 'fisharea', name: 'Fishing Fun' }
]
const mockMMenuListData = [
  {
    category: 'casino',
    list: mockCasinoListItems,
    showSub: false,
    name: 'Casino Category',
    key: 'casino_cat_key'
  },
  {
    category: 'sports',
    list: [{ key: 'soccer', name: 'Soccer Bets' }],
    showSub: false,
    name: 'Sports Category',
    key: 'sports_cat_key'
  }
]

vi.mock('@/stores/lobbySwitch', () => ({
  useLobbySwitchStore: () => ({
    mMenuList: ref(mockMMenuListData)
  })
}))

vi.mock('@/stores/mobilebasic', () => ({
  useMobileBasicInfoStore: () => ({
    siteInfo: ref({ unitTime: 123456789 }),
  })
}))

vi.mock('@/stores/config', () => ({
  useConfigStore: () => ({
    cdnUrl: ref('https://cdn.example.com'),
  })
}))

describe('BbSwitchBoard', () => {
  let wrapper: VueWrapper<any>

  beforeEach(() => {
    setActivePinia(createPinia())
    mockCurrentRouteName.value = 'default'
  })

  describe('Rendering', () => {
    it('renders switch buttons correctly', () => {
      wrapper = mount(BbSwitchBoard)
      const buttons = wrapper.findAll('.bbswitch-button')
      
      expect(buttons.length).toBe(2)
      expect(buttons[0].find('span').text()).toBe('Casino Games')
      expect(buttons[0].find('.game-icon').exists()).toBe(true)
      expect(buttons[1].find('span').text()).toBe('Fishing Fun')
      expect(buttons[1].find('.fisharea-icon').exists()).toBe(true)
    })

    it('applies "active" class to "Casino Games" button when route name is "mcasino"', () => {
      mockCurrentRouteName.value = 'mcasino'
      wrapper = mount(BbSwitchBoard)

      const buttons = wrapper.findAll('.bbswitch-button')
      const casinoButton = buttons[0]
      const fishingButton = buttons[1]

      expect(casinoButton.find('span').classes()).toContain('active')
      expect(fishingButton.find('span').classes()).not.toContain('active')
    })

    it('applies "active" class to "Fishing Fun" button when route name is "mfishing"', () => {
      mockCurrentRouteName.value = 'mfishing'
      wrapper = mount(BbSwitchBoard)

      const buttons = wrapper.findAll('.bbswitch-button')
      const casinoButton = buttons[0]
      const fishingButton = buttons[1]

      expect(casinoButton.find('span').classes()).not.toContain('active')
      expect(fishingButton.find('span').classes()).toContain('active')
    })
  })

  describe('Interactions', () => {
    beforeEach(() => {
      wrapper = mount(BbSwitchBoard)
    })

    it('router push is called when casion button is clicked', async () => {
      const casinoButton = wrapper.findAll('.bbswitch-button')[0]
      await casinoButton.trigger('click')
      expect(mockRouterPush).toHaveBeenCalledWith({ name: 'mcasino' })
    })

    it('router push is called when fish button is clicked', async () => {
      const fishingButton = wrapper.findAll('.bbswitch-button')[1]
      await fishingButton!.trigger('click')
      expect(mockRouterPush).toHaveBeenCalledWith({ name: 'mfishing' })
    })
  })

  describe('Theme Import', () => {
    it('calls importStyle with "mobile/bbSwitchBoard" during component setup', () => {
      wrapper = mount(BbSwitchBoard)
      expect(mockImportStyle).toHaveBeenCalledWith('mobile/bbSwitchBoard')
      expect(mockImportStyle).toBeCalled()
    })
  })
})