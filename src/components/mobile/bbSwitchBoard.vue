<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router'
import { useMobileBasicInfoStore } from "@/stores/mobilebasic"
import { useConfigStore } from '@/stores/config'
import { useLobbySwitchStore } from '@/stores/lobbySwitch'
import { useImportTheme } from '@/composables/useImportTheme'
import { storeToRefs } from 'pinia'

const router = useRouter()
const route = useRoute()
const basicInfoStore = useMobileBasicInfoStore()
const configStore = useConfigStore()
const lobbySwitchStore = useLobbySwitchStore()
const { siteInfo } = storeToRefs(basicInfoStore)
const { cdnUrl } = storeToRefs(configStore)
const { mMenuList } = storeToRefs(lobbySwitchStore)
const { importStyle } = useImportTheme()

const activeSelect = computed(() => {
  if (route.name == 'mfishing') {
    return 'fisharea'
  }
  if (route.name == 'mcasino') {
    return 'game'
  }
  return ''
})

const selectList = computed(() => {
  const casinoMenu = mMenuList.value.filter(item => item.category === 'casino')
  return casinoMenu.map(item => item.list).flat()
})

const changeRoute = (location: string) => {
  switch (location) {
    case 'game':
      router.push({name: 'mcasino'})
      break;

    case 'fisharea':     
      router.push({name: 'mfishing'})
      break;
  }
}

importStyle('mobile/bbSwitchBoard')
</script>

<template>
  <div class="bbswitch-group">
    <template v-for="item in selectList" :key="item.key">
    <div
      class="bbswitch-button"
      @click="changeRoute(item.key)"
    >
      <div
        :class="`${item.key}-icon`"
        :style="{
          backgroundImage: `url(${cdnUrl}/client/static/image/hall/casino/category/nav_logo.png?v=${siteInfo.unitTime})`
        }"
      />
      <span :class="{ active: activeSelect === item.key }">{{ item.name }}</span>
    </div>
  </template>
  </div>
</template>

<style lang="scss" scoped>
.bbswitch-group {
  display: flex;
  background: var(--mobile-bbSwitchBoard-bbswitch-group-bg);

  .bbswitch-button {
    margin: 5px 8px;
    display: flex;
    align-items: center;
    cursor: pointer;

    span {
      font-size: 1.2rem;
      font-weight: 400;
      color: var(--text-nonactive);
      letter-spacing: 1px;
      margin: 5px;

      &:hover {
        color: var(--mobile-bbSwitchBoard-bbswitch-button-active);
      }

      &.active {
        color: var(--mobile-bbSwitchBoard-bbswitch-button-active);
        font-weight: bold;
      }
    }
  }

  .game-icon {
    width: 22px;
    height: 22px;
    background-position: -110px 0;
  }

  .fisharea-icon {
    width: 22px;
    height: 22px;
    background-position: -88px 0;
  }
}
</style>