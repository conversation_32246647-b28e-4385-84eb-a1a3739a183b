import { mount, VueWrapper } from '@vue/test-utils'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { ref, nextTick } from 'vue'
import HistoryBoard from './historyBoard.vue'
import type { IGameInfo } from '@/types/game'
import { GameType } from '@/types/game'

vi.mock('vue-i18n', () => ({
  useI18n: () => ({
    t: (key: string) => key
  })
}))

vi.mock('@/composables/useGetImgUrl', () => ({
  useGetImgUrl: () => ({
    getImageUrl: (name: string) => `mock-url/${name}`
  })
}))

const mockRecentPlayList = ref<IGameInfo[]>([])
const mockAddRecentData = vi.fn()

vi.mock('@/stores/mobilebasic', () => ({
  useMobileBasicInfoStore: () => ({
    siteInfo: ref({ unitTime: '12345' })
  })
}))

vi.mock('@/stores/config', () => ({
  useConfigStore: () => ({
    cdnUrl: ref('http://mock-cdn.com')
  }),
}))

vi.mock('@/stores/recentPlay', () => ({
  useRecentlyPlay: () => ({
    recentPlayList: mockRecentPlayList,
    addRecentData: mockAddRecentData,
  }),
}))

vi.mock('@icon-park/vue-next', () => ({
  History: { name: 'History', template: '<span class="mock-history-icon" />' },
  Right: { name: 'Right', template: '<span class="mock-right-icon" />' },
  Left: { name: 'Left', template: '<span class="mock-left-icon" />' },
  Up: { name: 'Up', template: '<span class="mock-up-icon" />' },
  Down: { name: 'Down', template: '<span class="mock-down-icon" />' }
}))

vi.mock('@/components/GamePic.vue', () => ({
  default: {
    name: 'GamePic',
    template: '<div class="stub-game-pic"></div>',
  }
}))

const game1: IGameInfo = { gameId: 101, name: 'Game One', link: 'http://game.one/play', gameKind: GameType.Casino, icon: 'game-one-icon.png' }
const game2: IGameInfo = { gameId: 102, name: 'Game Two', link: 'http://game.two/play', gameKind: GameType.BBlive, icon: 'game-two-icon.png' }
const game3: IGameInfo = { gameId: 103, name: 'Game Three', link: 'http://game.three/play', gameKind: GameType.Battle, icon: 'game-three-icon.png' }

const mockWindowOpen = vi.fn()
global.open = mockWindowOpen

describe('HistoryBoard', () => {
  let wrapper: VueWrapper<any>

  beforeEach(() => {
    setActivePinia(createPinia())
    mockAddRecentData.mockClear()
    mockWindowOpen.mockClear()

    wrapper = mount(HistoryBoard)
  })

  describe('Rendering', () => {
    it('content should be hidden and an up arrow displayed when initial render', () => {
      expect(wrapper.find('.slide-container').isVisible()).toBe(false)
      expect(wrapper.vm.containerVisible).toBe(false)
      expect(wrapper.find('.mock-up-icon').exists()).toBe(true)
      expect(wrapper.find('.mock-down-icon').exists()).toBe(false)
    })

    it('toggle content visibility when the header area is clicked', async () => {
      const header = wrapper.find('.recent-header')

      await header.trigger('click')

      expect(wrapper.vm.containerVisible).toBe(true)
      
      await nextTick()
      
      expect(wrapper.find('.mock-up-icon').exists()).toBe(false)
      expect(wrapper.find('.mock-down-icon').exists()).toBe(true)

      await header.trigger('click')
      
      expect(wrapper.vm.containerVisible).toBe(false)
      
      await nextTick()

      expect(wrapper.find('.mock-up-icon').exists()).toBe(true)
      expect(wrapper.find('.mock-down-icon').exists()).toBe(false)
    })

    it('shows no data info', async () => {
      mockRecentPlayList.value = []

      await wrapper.find('.recent-header').trigger('click')
      await nextTick()

      expect(wrapper.find('.no-data').exists()).toBe(true)
      expect(wrapper.find('.no-data').text()).toBe('S_NO_DATA_CL')
      expect(wrapper.find('.recent-game-list').exists()).toBe(false)
      expect(wrapper.vm.showRightBtn).toBe(false)
      expect(wrapper.vm.showLeftBtn).toBe(false)
    })
  })

  describe('recentPlayList rendering', () => {
    beforeEach(async () => {
      mockAddRecentData.mockClear()
      mockWindowOpen.mockClear()

      mockRecentPlayList.value = [game1, game2, game3]

      if (!wrapper.vm.containerVisible) {
        await wrapper.find('.recent-header').trigger('click')
      }

      await nextTick()
    })

    it('renders correctly', async () => {
      const gameItems = wrapper.findAll('.recent-game-list li')

      expect(wrapper.find('.recent-game-list').exists()).toBe(true)
      expect(wrapper.findAll('.recent-game-list li').length).toBe(3)
      expect(wrapper.find('.no-data').exists()).toBe(false) 
      expect(gameItems[0].find('.game-name').text()).toBe('Game One')
      expect(gameItems[1].find('.game-name').text()).toBe('Game Two')
      expect(gameItems[2].find('.game-name').text()).toBe('Game Three')
    })

    it('opens game and records recent play', async () => {
      const firstGameItem = wrapper.findAll('.recent-game-list li').at(0)
      await firstGameItem!.trigger('click')

      expect(global.open).toBeCalled()
      expect(global.open).toHaveBeenCalledWith('http://game.one/play', '', 'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no')
      expect(mockAddRecentData).toBeCalled()
    })

    describe('Scrolling Functionality', () => {
      let gameListElement: any
      let currentScrollLeft: number

      beforeEach(async () => {
        mockRecentPlayList.value = [game1, game2, game3, game1, game2]

        currentScrollLeft = 0
        gameListElement = wrapper.vm.gamecontainer as HTMLDivElement

        Object.defineProperty(gameListElement, 'scrollWidth', { configurable: true, writable: true, value: 0 })
        Object.defineProperty(gameListElement, 'clientWidth', { configurable: true, writable: true, value: 0 })
        Object.defineProperty(gameListElement, 'scrollLeft', {
          configurable: true,
          get: () => currentScrollLeft,
          set: (val: number) => {
            currentScrollLeft = val
          },
        })

        gameListElement.scrollLeft = 0
      })

      it('scroll right and update button visibility', async () => {
        gameListElement.scrollWidth = 800
        gameListElement.clientWidth = 300
        gameListElement.scrollLeft = 0
        wrapper.vm.detectScrollWidth()
        await nextTick()

        expect(wrapper.vm.showRightBtn).toBe(true)
        expect(wrapper.vm.showLeftBtn).toBe(false)

        const rightButton = wrapper.find('.mock-right-icon')
        await rightButton.trigger('click')

        expect(gameListElement.scrollLeft).toBe(200)
        expect(wrapper.vm.showLeftBtn).toBe(true)
        expect(wrapper.vm.showRightBtn).toBe(true)

        gameListElement.scrollLeft = 300
        await rightButton.trigger('click')

        expect(gameListElement.scrollLeft).toBe(500)
        expect(wrapper.vm.showLeftBtn).toBe(true)
        expect(wrapper.vm.showRightBtn).toBe(false)
      })

      it('scroll left and update button visibility', async () => {
        gameListElement.scrollWidth = 800
        gameListElement.clientWidth = 300
        gameListElement.scrollLeft = 500

        wrapper.vm.detectScrollWidth()
        wrapper.vm.showRightBtn = false
        wrapper.vm.showLeftBtn = true
        await nextTick()

        expect(wrapper.find('.mock-left-icon').exists()).toBe(true)

        const leftButton = wrapper.find('.mock-left-icon')

        await leftButton.trigger('click')

        expect(gameListElement.scrollLeft).toBe(300)
        expect(wrapper.vm.showLeftBtn).toBe(true)
        expect(wrapper.vm.showRightBtn).toBe(false) 

        gameListElement.scrollLeft = 200 
        wrapper.vm.showLeftBtn = true 

        await nextTick()

        expect(wrapper.find('.mock-left-icon').exists()).toBe(true)

        await leftButton.trigger('click')

        expect(gameListElement.scrollLeft).toBe(0)
        expect(wrapper.vm.showLeftBtn).toBe(false)
        expect(wrapper.vm.showRightBtn).toBe(true)
      })

       it('shows correctly when scrolled to less than 0', async () => {
        gameListElement.scrollWidth = 800
        gameListElement.clientWidth = 300
        wrapper.vm.detectScrollWidth()
        await nextTick()

        gameListElement.scrollLeft = 100
        wrapper.vm.showLeftBtn = true
        wrapper.vm.showRightBtn = true
        await nextTick()

        const leftButton = wrapper.find('.mock-left-icon')
        await leftButton.trigger('click')

        expect(gameListElement.scrollLeft).toBe(-100)
        expect(wrapper.vm.showLeftBtn).toBe(false)
        expect(wrapper.vm.showRightBtn).toBe(true)
      })
    })
  })
})