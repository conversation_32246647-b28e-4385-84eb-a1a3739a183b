<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { History, Right, Left, Up, Down } from '@icon-park/vue-next'
import { useGetImgUrl } from '@/composables/useGetImgUrl'
import { useMobileBasicInfoStore } from "@/stores/mobilebasic"
import { useConfigStore } from '@/stores/config'
import { useRecentlyPlay } from "@/stores/recentPlay"
import { storeToRefs } from 'pinia'
import type { IGameInfo } from '@/types/game'
import GamePic from '@/components/GamePic.vue'
import { nextTick } from 'vue'

const { t } = useI18n()
const gamecontainer: globalThis.Ref = ref(null)
const { getImageUrl } = useGetImgUrl()
const mobileBasicInfoStore = useMobileBasicInfoStore()
const configStore = useConfigStore()
const { siteInfo } = storeToRefs(mobileBasicInfoStore)
const { cdnUrl } = storeToRefs(configStore)
const recentPlayStore = useRecentlyPlay()
const { recentPlayList } = storeToRefs(recentPlayStore)
const showLeftBtn = ref(false)
const showRightBtn = ref(false)
const maxScrollLeft = ref(0)
const containerVisible = ref(false)

const openGame = (url: string, game: IGameInfo) => {
  window.open(url, '', 'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no')
  recentPlayStore.addRecentData(game)
}

const onToggleClick = async () => {
  containerVisible.value = !containerVisible.value
  
  if (containerVisible.value) {
    await nextTick()
    detectScrollWidth()
  }
};

const scrollRight = () => {
  gamecontainer.value.scrollLeft += 200

  if (gamecontainer.value.scrollLeft == 0) {
    showLeftBtn.value = false
  } else if ( Math.round(gamecontainer.value.scrollLeft) == maxScrollLeft.value) {
      showRightBtn.value = false
      showLeftBtn.value = true
  } else {
    showLeftBtn.value = true
  }
}

const scrollLeft = () => {
  gamecontainer.value.scrollLeft -= 200

  if (gamecontainer.value.scrollLeft > 0 && gamecontainer.value.scrollLeft < maxScrollLeft.value) {
    showLeftBtn.value = true
  } else if (gamecontainer.value.scrollLeft == 0) {
    showLeftBtn.value = false
    showRightBtn.value = true
  } else {
    showLeftBtn.value = false
  }
}

const detectScrollWidth = () => {
  if( recentPlayList.value.length > 0 && gamecontainer.value.scrollWidth ) {
      maxScrollLeft.value = gamecontainer.value.scrollWidth - gamecontainer.value.clientWidth
  }
  if(maxScrollLeft.value > 0) {
    showRightBtn.value = true
  }
}

watch( recentPlayList, async () => {
  await nextTick()
  detectScrollWidth()
})
</script>

<template>
  <div class="history-board">
    <div class="recent-header" @click="onToggleClick">
      <span class="toggle">
        <up
          v-if="!containerVisible"
          class="right-btn"
          theme="filled"
          size="30"
          fill="#000"
        />
        <down
          v-else
          class="right-btn"
          theme="filled"
          size="30"
          fill="#000"
        />
      </span>
      <div class="divider"></div>
    </div>
    <transition name="history-board-slide">
      <div v-show="containerVisible" class="slide-container">
        <div class="recent-container">
          <div class="recent-title">
            <history 
              theme="outline"
              size="40"
              fill="#ffe599"
            />
            <span>{{ t('S_RECENTLY_PLAYED') }}</span>
          </div>
          <div v-if="recentPlayList.length > 0" ref="gamecontainer" class="recent-game-list">
            <template v-for="game in recentPlayList" :key="game.gameId">
              <li @click="openGame(game.link, game)">
                <GamePic
                  :game-pic="`${cdnUrl}/client/static/image/gamePicture/mobile/${game.gameKind}/${game.gameId}.png?v=${siteInfo.unitTime}`"
                  :failed-pic="getImageUrl('mobile_failed.png')"
                />
                <a class="game-name">{{ game.name }}</a>
              </li>
            </template>
          </div>
          <div v-else class="no-data">
            {{ t('S_NO_DATA_CL') }}
          </div>
          <div class="control-btn">
            <right
              v-if="showRightBtn"
              class="right-btn"
              theme="filled"
              size="50"
              fill="#fff"
              @click="scrollRight()"
            />
            <left
              v-if="showLeftBtn"
              class="right-btn"
              theme="filled"
              size="50"
              fill="#fff"
              @click="scrollLeft()"
            />
          </div>
        </div>
      </div>
    </transition>
  </div>

</template>

<style lang="scss" scoped>
.history-board {
  display: flex;
  flex-direction: column;
  position: fixed;
  bottom: 40px;
  width: 100%;

  .recent-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0;

    .divider {
      width: 100%;
      height: 4px;
      background: #f2c926;
    }

    .toggle {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 55px;
      height: 30px;
      border-radius: 35px 35px 0 0;
      background: #f2c926;
      position: relative;
      top: 1px;
    }
  }

  .slide-container {
    height: 120px;
    background: #333;
  }

  .recent-container {
    background: #333;
    height: 120px;
    display: flex;
    justify-content: space-around;
    align-items: center;

    .recent-title {
      width: 10%;
      padding: 0 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      font-size: 1.1rem;
      color: #ffe599;
      span {
        margin: 2px;
      }
      .i-icon-history {
        display: none;
      }
    }

    .please-login {
      width: 90%;
      text-align: center;
      color: var(--text-nonactive);
    }

    .recent-game-list {
      overflow-x: scroll; 
      display: flex;
      flex: 1;

      &::-webkit-scrollbar {
        display: none;
      }

      li {
        list-style: none;
        margin: 5px;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        color: #fff;
        cursor: pointer;

        &:hover {
          color: var(--main);
        }

        .game-name {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          width: 60px;
        }
      }
      .el-image {
        width: 55px;
        height: 55px;
        overflow: hidden;
        border-radius: 5px;
        margin: 5px;
      }

      :deep(.el-image) {
        img {
          object-fit: cover;
          object-position: center;
        }
      }
    }

    .no-data {
      width: 100%;
      color: var(--text-nonactive);
      text-align: center;
      flex: 1;
    }

    .control-btn {
      margin: auto;
      display: flex;
      flex-direction: column;
    }

    .right-btn, .left-btn {
      cursor: pointer;
    }
  }
}

// bottomInfo slid
.history-board-slide-enter-from,
.history-board-slide-leave-to {
    height: 0 !important;
}
.history-board-slide-enter-active,
.history-board-slide-leave-active {
  transition: all 0.3s ease-out;
}
</style>
