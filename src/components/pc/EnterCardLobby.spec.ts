import { mount } from '@vue/test-utils'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import EnterCardLobby from '@/components/pc/EnterCardLobby.vue'
import { createI18n } from 'vue-i18n'
import { ElMessageBox } from 'element-plus'

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      S_ENTER_GAME_HALL: 'Enter Game Hall',
      S_LOGIN_TIPS: 'Please Login!'
    }
  }
})

vi.mock('element-plus', () => ({
  ElMessageBox: {
    alert: vi.fn()
  }
}))

describe('EnterCardLobby', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(EnterCardLobby, {
      global: {
        plugins: [i18n],
      },
      props: {
        isLogin: true,
        hallId: 1,
        lobbyUrl: '/cardlobby'
      },
    })
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  it('should display the button with correct text', () => {
    expect(wrapper.find('.cardlobby-btn').text()).toBe('Enter Game Hall')
  })

  it('should alert when user is not logged in', async () => {
    await wrapper.setProps({ isLogin: false })
    vi.spyOn(ElMessageBox, 'alert').mockImplementation(vi.fn())

    await wrapper.find('.cardlobby-btn').trigger('click')

    expect(ElMessageBox.alert).toHaveBeenCalledWith('Please Login!', '', { center: true, showClose: false })
  })


  it('should open window to cardlobby when user is logged in', async () => {
    await wrapper.setProps({ isLogin: true })
    await wrapper.setProps({ lobbyUrl: '/cardlobby' })
    vi.spyOn(window, 'open').mockImplementation(vi.fn())
    await wrapper.find('.cardlobby-btn').trigger('click')
    expect(window.open).toHaveBeenCalledWith('/cardlobby', '', 'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no')
  })

})
