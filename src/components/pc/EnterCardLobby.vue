<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { ElMessageBox } from 'element-plus'

const { t } = useI18n()

const props = defineProps({
  isLogin: {
    type: Boolean,
    required: true,
    default: false
  },
  hallId: {
    type: Number,
    required: true,
    default: 0
  },
  lobbyUrl: {
    type: String,
    required: true,
    default: ''
  }
})

const openWindow = (link: string) => {
  if (!props.isLogin) {
    ElMessageBox.alert(
      t('S_LOGIN_TIPS'),
      '',
      { center: true, showClose: false}
    )
    return
  }
  window.open(
    link,
    '',
    'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no'
  )
}

</script>

<template>
  <div class="togamehall">
    <span class="cardlobby-btn" @click="openWindow(props.lobbyUrl)">
      {{ t('S_ENTER_GAME_HALL') }}
    </span>
  </div>
</template>

<style lang="scss" scoped>
.togamehall {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--pc-card-togamehall-bg);

  &.hide {
    display: none;
  }

  .cardlobby-btn {
    margin: 0 10px;
    margin-left: auto;
    color: #fff;
    border: 1px solid #fff;
    border-radius: 25px;
    padding: 6px 10px;
    cursor: pointer;
    &:hover {
      background-color: var(--pc-card-cardlobby-btn-hover-bg);
      color: var(--pc-card-cardlobby-btn-hover-text);
    }
  }
}
</style>