import { mount } from '@vue/test-utils'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import GameCard from '@/components/pc/GameCard.vue'
import type { IGameInfo } from '@/types/game'
import { createPinia, setActivePinia } from 'pinia'

vi.mock('@/composables/useGetImgUrl', () => ({
  useGetImgUrl: () => ({
    getImageUrl: vi.fn((name: string) => `mock-img-url/${name}`)
  })
}))

vi.mock('vue-i18n', () => ({
  useI18n: () => ({
    t: vi.fn((key: string) => key)
  })
}))

vi.mock('@/composables/useImportTheme', () => ({
  useImportTheme: () => ({
    importStyle: vi.fn()
  })
}))

const mockAddFavoriteGame = vi.fn()
const mockRemoveFavoriteGame = vi.fn()
vi.mock('@/stores/casinoGame', () => ({
  useCasinoGameStore: () => ({
    addFavoriteGame: mockAddFavoriteGame,
    removeFavoriteGame: mockRemoveFavoriteGame
  })
}))

const mockAddRecentData = vi.fn()
vi.mock('@/stores/recentPlay', () => ({
  useRecentlyPlay: () => ({
    addRecentData: mockAddRecentData
  })
}))

vi.mock('@icon-park/vue-next', () => ({
  Star: { name: 'Star', template: '<span class="mock-star-icon" />' },

}))

vi.mock('@/components/GamePic.vue', () => ({
  default: {
    name: 'GamePic',
    template: '<div class="stub-game-pic"></div>',
  }
}))

vi.mock('@/components/buttons/FreeTrial.vue', () => ({
  default: {
    name: 'FreeTrial',
    template: '<div class="stub-free-trial"></div>',
  }
}))

vi.mock('@/components/buttons/GameEnter.vue', () => ({
  default: {
    name: 'GameEnter',
    template: '<div class="stub-game-enter"></div>',
  }
}))

vi.mock('@/components/buttons/GameIntro.vue', () => ({
  default: {
    name: 'GameIntro',
    template: '<div class="stub-game-intro"></div>',
  }
}))

const defaultGameInfo: IGameInfo = {
  gameKind: 5,
  gameId: 101,
  name: 'Awesome Game',
  nickname: 'Awesome',
  icon: 'new',
  link: 'http://example.com/play/awesome-game',
  demoLink: 'http://example.com/demo/awesome-game',
  jpAmount: 12345.67,
  jpImg: 'major'
}

const defaultProps = {
  gameinfo: defaultGameInfo,
  gamepic: 'game-cover.png',
  isfav: false,
  cdnUrl: 'https://mycdn.com',
  isLogin: false,
  unixTime: 1678886400
}

const mockWindowOpen = vi.fn()
global.open = mockWindowOpen

describe('GameCard', () => {
  const createWrapper = (props = {}, provideOptions = { nowPage: 'casino' }) => {
    return mount(GameCard, {
      props: { ...defaultProps, ...props },
      global: {
        provide: provideOptions
      }
    })
  }

  beforeEach(() => {
    setActivePinia(createPinia())
  })

  describe('Rendering', () => {
    it('renders game name', () => {
      const wrapper = createWrapper()

      expect(wrapper.find('.game-name').text()).toBe('Awesome Game')
    })

    it('renders game icon text', () => {
      const wrapper = createWrapper()

      expect(wrapper.find('.icon').text()).toBe('icon.new')
    })

    it('renders nickname when currentPage is "fishing"', () => {
      const wrapper = createWrapper({}, { nowPage: 'fishing' })
      const nicknameEl = wrapper.find('.nickname.fishing')

      expect(nicknameEl.exists()).toBe(true)
      expect(nicknameEl.isVisible()).toBe(true)
      expect(nicknameEl.text()).toBe('Awesome')
    })

    it('renders GamePic with correct props and class', () => {
      const wrapper = createWrapper({}, { nowPage: 'testPage' })
      const gamePicComponent = wrapper.findComponent({ name: 'GamePic' })
      const gamePicAttrs = gamePicComponent.attributes()
      
      expect(gamePicComponent.exists()).toBe(true)
      expect(gamePicAttrs['game-pic']).toBe('game-cover.png')
      expect(gamePicAttrs['failed-pic']).toBe('mock-img-url/failed.png')
      expect(gamePicComponent.classes()).toContain('testPage')
    })

    it('renders jackpot information if jpAmount exists', () => {
      const wrapper = createWrapper()
      const jackpotWrap = wrapper.find('.ele-casino-game-jackpot-wrap')

      expect(jackpotWrap.exists()).toBe(true)
      expect(jackpotWrap.text()).toContain('12,345.67')

      const jackpotImg = jackpotWrap.find('img')

      expect(jackpotImg.attributes('src')).toBe(
        `https://mycdn.com/client/static/image/hall/casino/game_major.png?v=1678886400`
      )
    })

    it('does not render jackpot information if jpAmount is null', () => {
      const wrapper = createWrapper({
        gameinfo: { ...defaultGameInfo, jpAmount: null }
      })

      expect(wrapper.find('.ele-casino-game-jackpot-wrap').exists()).toBe(false)
    })

    it('renders GameIntro with correct props', () => {
      const wrapper = createWrapper({}, { nowPage: 'introPage' })
      const gameIntroComponent = wrapper.findComponent({ name: 'GameIntro' })
      const gameIntroAttrs = gameIntroComponent.attributes()

      expect(gameIntroComponent.exists()).toBe(true)
      expect(gameIntroAttrs['game-id']).toBe('101')
      expect(gameIntroAttrs['current-page']).toBe('introPage')
      expect(gameIntroAttrs['is-list']).toBe('false')
    })

    it('applies currentPage class to relevant elements', () => {
      const wrapper = createWrapper(
        { gameinfo: { ...defaultGameInfo, nickname: 'Nick' } },
        { nowPage: 'customPage' }
      )

      expect(wrapper.find('.gamecard-wrap').classes()).toContain('customPage')
      expect(wrapper.find('.game-name').classes()).toContain('customPage')
    })
  })

  describe('Login Status and Button Rendering', () => {
    it('shows FreeTrial button if not logged in and demoLink exists', () => {
      const wrapper = createWrapper({
        isLogin: false,
        gameinfo: { ...defaultGameInfo, demoLink: 'a-demo-link' }
      })

      expect(wrapper.findComponent({ name: 'FreeTrial' }).exists()).toBe(true)
      expect(wrapper.findComponent({ name: 'GameEnter' }).exists()).toBe(false)
      expect(wrapper.findComponent({ name: 'FreeTrial' }).attributes('demo-link')).toBe('a-demo-link')
    })

    it('shows GameEnter button if not logged in and no demoLink', () => {
      const wrapper = createWrapper({
        isLogin: false,
        gameinfo: { ...defaultGameInfo, demoLink: undefined }
      })

      expect(wrapper.findComponent({ name: 'FreeTrial' }).exists()).toBe(false)
      expect(wrapper.findComponent({ name: 'GameEnter' }).exists()).toBe(true)
    })

    it('shows GameEnter button if logged in', () => {
      const wrapper = createWrapper({ isLogin: true })

      expect(wrapper.findComponent({ name: 'FreeTrial' }).exists()).toBe(false)
      expect(wrapper.findComponent({ name: 'GameEnter' }).exists()).toBe(true)
    })

    it('shows favorite icon if logged in', () => {
      const wrapper = createWrapper({ isLogin: true })

      expect(wrapper.find('.fav-icon').exists()).toBe(true)
    })

    it('does not show favorite icon if not logged in', () => {
      const wrapper = createWrapper({ isLogin: false })

      expect(wrapper.find('.fav-icon').exists()).toBe(false)
    })
  })

  describe('Favorite Icon State and Interaction', () => {
    beforeEach(() => {
      mockAddFavoriteGame.mockClear()
      mockRemoveFavoriteGame.mockClear()
    })

    it('renders filled star if isfav is true and logged in', () => {
      const wrapper = createWrapper({ isLogin: true, isfav: true })
      const starComponent = wrapper.findComponent({ name: 'Star' })
      const starAttrs = starComponent.attributes()

      expect(starAttrs['theme']).toBe('filled')
      expect(starAttrs['fill']).toBe('var(--fav-bg-active)')
    })

    it('renders outline star if isfav is false and logged in', () => {
      const wrapper = createWrapper({ isLogin: true, isfav: false })
      const starComponent = wrapper.findComponent({ name: 'Star' })
      const starAttrs = starComponent.attributes()

      expect(starAttrs['theme']).toBe('outline')
      expect(starAttrs['fill']).toBe('var(--main)')
    })

    it('calls addFavoriteGame when isfav is false on click', async () => {
      const wrapper = createWrapper({ isLogin: true, isfav: false })
      await wrapper.find('.fav-icon').trigger('click')

      expect(mockAddFavoriteGame).toHaveBeenCalledWith(101)
      expect(mockRemoveFavoriteGame).not.toHaveBeenCalled()
    })

    it('calls removeFavoriteGame when isfav is true on click', async () => {
      const wrapper = createWrapper({ isLogin: true, isfav: true })
      await wrapper.find('.fav-icon').trigger('click')

      expect(mockRemoveFavoriteGame).toHaveBeenCalledWith(101)
      expect(mockAddFavoriteGame).not.toHaveBeenCalled()
    })

    it('fav-icon bind class when currentPage is "card"', () => {
      const wrapper = createWrapper({ isLogin: true }, { nowPage: 'card' })
      const favIcon = wrapper.find('.fav-icon')

      expect(favIcon.classes()).toContain('card')
    })
  })

  describe('openGame Method and Game Interaction', () => {
    it('shows login alert (ElMessageBox) if not logged in when trying to open game', async () => {
      const wrapper = createWrapper({
        isLogin: false,
        gameinfo: { ...defaultGameInfo, demoLink: undefined }
      })
      const gameEnterButton = wrapper.findComponent({ name: 'GameEnter' })
      
      await gameEnterButton.vm.$emit('click')

      expect(global.open).not.toHaveBeenCalled()
      expect(mockAddRecentData).not.toHaveBeenCalled()
    })

    it('opens game window and adds to recent if logged in', async () => {
      const wrapper = createWrapper({ isLogin: true })
      const gameEnterButton = wrapper.findComponent({ name: 'GameEnter' })
      
      await gameEnterButton.vm.$emit('click')

      expect(global.open).toHaveBeenCalledWith(
        'http://example.com/play/awesome-game',
        '',
        'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no'
      )
      expect(mockAddRecentData).toHaveBeenCalled()
    })
  })

  describe('addRecent Method', () => {
    beforeEach(() => {
      mockAddRecentData.mockClear()
    })
    
    it('calls addRecentData when game is opened and currentPage is not "card"', async () => {
      const wrapper = createWrapper({ isLogin: true }, { nowPage: 'casino' })
      const gameEnterButton = wrapper.findComponent({ name: 'GameEnter' })

      await gameEnterButton.vm.$emit('click')

      expect(mockAddRecentData).toHaveBeenCalled()
    })

    it('does not call addRecentData if game is opened and currentPage is "card"', async () => {
      const wrapper = createWrapper({ isLogin: true }, { nowPage: 'card' })
      const gameEnterButton = wrapper.findComponent({ name: 'GameEnter' })

      await gameEnterButton.vm.$emit('click')

      expect(mockAddRecentData).not.toHaveBeenCalled()
    })
  })
})
