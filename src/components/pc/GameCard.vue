<script setup lang="ts">
import { Star } from '@icon-park/vue-next'
import { useI18n } from 'vue-i18n'
import { useGetImgUrl } from '@/composables/useGetImgUrl'
import type { IGameInfo } from '@/types/game'
import { useCasinoGameStore } from '@/stores/casinoGame'
import { useRecentlyPlay } from "@/stores/recentPlay"
import GameIntro from '@/components/buttons/GameIntro.vue'
import FreeTrial from '@/components/buttons/FreeTrial.vue'
import GameEnter from '@/components/buttons/GameEnter.vue'
import { useImportTheme } from '@/composables/useImportTheme'
import GamePic from '@/components/GamePic.vue'
import { ElMessageBox } from 'element-plus'

const props = defineProps({
  gameinfo: {
    type: Object as PropType<IGameInfo>,
    default: () => {},
    required: true
  },
  gamepic: {
    type: String,
    default: '',
    required: true
  },
  // 最愛（星星icon）是否被選取
  isfav: {
    type: Boolean,
    default: false,
    required: false
  },
  cdnUrl: {
    type: String,
    default: '',
    required: true
  },
  isLogin: {
    type: Boolean,
    default: false,
    required: true
  },
  unixTime: {
    type: Number,
    default: 0,
    required: false
  }
})

const { getImageUrl } = useGetImgUrl()
const { t } = useI18n()
const recentPlayStore = useRecentlyPlay()
const casinoGameStore = useCasinoGameStore()
const currentPage = inject<string>('nowPage', '')
const { importStyle } = useImportTheme()

const addRecent = (game: IGameInfo) => {
  if(currentPage === 'card') {
    return
  }

  recentPlayStore.addRecentData(game)
}

const handleFavorite = async(gameid: number, isfav: boolean) => {
  try {
    if (isfav == false) {
      await casinoGameStore.addFavoriteGame(gameid)
    } else {
      await casinoGameStore.removeFavoriteGame(gameid)
    }
  } catch (err) {
    ElMessage.error((err as Error).message)
  }
}

const openGame = (url: string, game: IGameInfo) => {
  if (!props.isLogin) {
    ElMessageBox.alert(
      t('S_LOGIN_TIPS'),
      '',
      { center: true, showClose: false}
    )
    return
  }
  window.open(url, '', 'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no')
  addRecent(game)
}

const gameInfo = computed(() => {
  return props.gameinfo
})

importStyle('pc/gameCard')
</script>

<template>
  <div :class="['gamecard-wrap', currentPage]">
    <!-- 遊戲圖片 -->
    <GamePic
      :game-pic="props.gamepic"
      :class="currentPage"
      :failed-pic="getImageUrl('failed.png')"
    />
    <!-- 免費試玩 / 進入遊戲 / 遊戲介紹 按鈕 -->
    <a>
      <div :class="['mask', currentPage]">
        <FreeTrial
          v-if="!props.isLogin && gameInfo.demoLink"
          :is-list="false"
          :demo-link="gameInfo.demoLink"
        />
        <GameEnter
          v-else
          :is-list="false"
          @click="openGame(gameInfo.link, gameInfo)"
        />
        <GameIntro :game-id="props.gameinfo.gameId.toString()" :current-page="currentPage" :is-list="false" />
      </div>
    </a>
    <!-- 彩金條 -->
    <div
      v-if="gameInfo.jpAmount"
      class="ele-casino-game-jackpot-wrap"
    >
      <div class="ele-casino-jackpot-img">
        <img
            :src="`${props.cdnUrl}/client/static/image/hall/casino/game_${
              gameInfo.jpImg || 'minor'
            }.png?v=${props.unixTime}`"
        />
      </div>
      {{ gameInfo.jpAmount?.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}
    </div>
    <!-- 遊戲標籤 -->
    <div class="game-status">
      <div v-if="gameInfo.nickname" :class="['nickname', currentPage]">
        {{ gameInfo.nickname }}
      </div>
      <div v-if="gameInfo.icon" class="icon">{{ t(`icon.${gameInfo.icon.toLowerCase()}`) }}</div>
    </div>
    <!-- 遊戲名稱 -->
    <span :class="['game-name', currentPage]">
      {{ gameInfo.name || '(empty)' }}
    </span>
    <!--  加入最愛按鈕 -->
    <div v-if="props.isLogin" :class="['fav-icon', currentPage]" @click="handleFavorite(props.gameinfo.gameId, props.isfav)">
      <star v-if="props.isfav" theme="filled" size="18" :fill="'var(--fav-bg-active)'" />
      <star v-else theme="outline" size="18" fill="var(--main)" />
    </div>
  </div>
</template>

<style lang="scss" scoped>

  .gamecard-wrap {
    position: relative;
    border-radius: 10px;
    float: left;
    width: auto;
    height: auto;
    margin: 5px;
    background-color: #ffffff;
    border: 1px solid #ddd;
    box-shadow: -1px 1px 5px 2px #0000001f;

    &.card {
      height: 200px;
      display: flex;
      flex-direction: column;
      background-color: var(--pc-gameCard-gamecard-wrap-bg);
      border: 1px solid var(--pc-gameCard-gamecard-wrap-bg);
    }

    .el-image,
    img {
      width: 100%;
      height: 115px;
      border-radius: 10px 10px 0 0;

      &.card {
        padding: 8%;
        height: auto;
      }
    }

    .game-status {
      position: absolute;
      top: 10px;
      left: -8px;
      display: block;
      color: #ffffff;
      font-size: 12px;
      opacity: 0.9;
        .icon {
          width: fit-content;
          background: linear-gradient(45deg, #d5373a, #ea4a4a, #ef2f2f);
          padding: 5px;
          box-shadow: 1.5px 1.5px 0 rgba(102, 0, 0, 0.3);
          border-radius: 5px;
        }
        .nickname {
          display: none;
          width: fit-content;
          background: linear-gradient(45deg, #43688d, #1a76d2, #43688d);
          padding: 5px;
          box-shadow: 1.5px 1.5px 0 rgba(102, 0, 0, 0.3);
          border-radius: 5px;
          margin-bottom: 3px;

          &.fishing {
            display: block;
          }
        }
    }

    .mask {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 73%;
      background: rgba(101, 101, 101, 0.5);
      color: #ffffff;
      opacity: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      border-radius: 10px 10px 0 0;
      .el-button {
          margin: 5px 0;
          &:hover {
            border-color: var(--fav-bg-active);
            background-color: var(--fav-bg-active);
          }
      }
      &:hover {
          opacity: 1;
      }

      &.card {
        &:hover {
          background: var(--pc-gameCard-mask-card-hover-bg);
        }
      }
    }

    .game-name {
      display: block;
      max-width: 150px;
      padding: 6px 10px;
      text-align: start;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #777777;

      &.card {
        font-size: 1.2rem;
        text-align: center;
        max-width: unset;
        flex: auto;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--pc-gameCard-game-name-card-bg);
        color: var(--pc-gameCard-game-name-card-text);
        border-radius: 0 0 10px 10px;
      }
    }

    .fav-icon {
      &.card, 
      &.fishing {
        display: none;
      }
    }

    .i-icon-star {
      position: absolute;
      right: 8px;
      bottom: 6px;
      cursor: pointer;
    }
    &:hover {
      transform: translate(1%, 1%);
    }
  }

  .ele-casino-game-jackpot-wrap {
    padding: 3px 5px;
    position: absolute;
    left: 0%;
    bottom: 20%;
    width: 100%;
    display: flex;
    justify-content: space-around;
    align-items: center;
    background: url(/client/static/image/hall/casino/dialog_overlay_bg.png) 0 0 repeat;
    color: #f2b243;

    .ele-casino-jackpot-img {
      max-width: 50%;
      img {
        width: 50px;
        height: auto;
      }
    }
  }
</style>
