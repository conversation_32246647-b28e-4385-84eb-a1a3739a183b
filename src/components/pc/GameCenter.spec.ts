import { mount, VueWrapper } from '@vue/test-utils'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import GameCenter from './GameCenter.vue'
import { GameType, type IGameInfo } from '@/types/game'

vi.mock('@/components/pc/GameCard.vue', () => ({
  default: {
    name: 'GameCard',
    props: ['gamepic', 'gameinfo', 'isfav', 'cdnUrl', 'isLogin', 'unixTime'],
    template: '<div class="mock-game-card"></div>'
  }
}))

vi.mock('@/components/pc/GameList.vue', () => ({
  default: {
    name: 'GameList',
    props: ['gameinfo', 'isfav', 'isLogin'],
    template: '<div class="mock-game-list"></div>'
  }
}))

vi.mock('vue3-spinners', () => ({
  VueSpinnerIos: {
    name: 'VueSpinnerIos',
    template: '<div class="mock-spinner"></div>'
  }
}))

vi.mock('@/stores/basicInfo', () => ({
  useBasicInfoStore: vi.fn(() => ({
    siteInfo: ref({ unixTime: '**********' })
  }))
}))

vi.mock('@/stores/config', () => ({
  useConfigStore: vi.fn(() => ({
    cdnUrl: ref('https://cdn.example.com')
  }))
}))

vi.mock('@/stores/account', () => ({
  useAccountInfoStore: () => ({
    accountInfo: ref({
      isLogin: true,
      username: 'testUser'
    })
  })
}))

const mockGameList: IGameInfo[] = [
  {
    gameId: 101,
    name: 'Game One',
    link: 'http://game.one/play',
    gameKind: GameType.Casino,
    icon: 'game-one-icon.png'
  },
  {
    gameId: 102,
    name: 'Game Two',
    link: 'http://game.two/play',
    gameKind: GameType.BBlive,
    icon: 'game-two-icon.png'
  },
  {
    gameId: 103,
    name: 'Game Three',
    link: 'http://game.three/play',
    gameKind: GameType.Battle,
    icon: 'game-three-icon.png'
  }
]

const mockFavoriteGameList: IGameInfo[] = [
  {
    gameId: 101,
    name: 'Game One',
    link: 'http://game.one/play',
    gameKind: GameType.Casino,
    icon: 'game-one-icon.png'
  }
]

describe('GameCenter', () => {
  let wrapper: VueWrapper<any>

  beforeEach(() => {
    setActivePinia(createPinia())
  })

  const createWrapper = (props = {}) => {
    return mount(GameCenter, {
      props: {
        gameList: [],
        favoriteGameList: [],
        listMode: false,
        ...props
      }
    })
  }

  describe('Rendering', () => {
    it('should display loading spinner when gameList is empty', () => {
      wrapper = createWrapper({ gameList: [] })

      expect(wrapper.findComponent({ name: 'VueSpinnerIos' }).exists()).toBe(true)
      expect(wrapper.findComponent({ name: 'GameCard' }).exists()).toBe(false)
      expect(wrapper.findComponent({ name: 'GameList' }).exists()).toBe(false)
    })

    it('should not display loading spinner when gameList has data', () => {
      wrapper = createWrapper({ gameList: mockGameList })

      expect(wrapper.findComponent({ name: 'VueSpinnerIos' }).exists()).toBe(false)
    })
  })

  describe('Card Mode', () => {
    beforeEach(() => {
      wrapper = createWrapper({
        gameList: mockGameList,
        favoriteGameList: mockFavoriteGameList,
        listMode: false
      })
    })

    it('should render GameCard components correctly', () => {
      const gameCards = wrapper.findAllComponents({ name: 'GameCard' })

      expect(gameCards.length).toBe(3)
      expect(wrapper.findComponent({ name: 'GameList' }).exists()).toBe(false)
    })

    it('should pass correct props to GameCard components', () => {
      const gameCard = wrapper.findComponent({ name: 'GameCard' })
      const firstGame = mockGameList[0]

      expect(gameCard.props('gameinfo')).toEqual(firstGame)
      expect(gameCard.props('gamepic')).toBe(
        `https://cdn.example.com/client/static/image/gamePicture/pc/5/101.png?v=**********`
      )
      expect(gameCard.props('isfav')).toBe(true)
      expect(gameCard.props('cdnUrl')).toBe('https://cdn.example.com')
      expect(gameCard.props('isLogin')).toBe(true)
      expect(gameCard.props('unixTime')).toBe('**********')
    })

    it('should pass undefined for isfav prop for Battle type games', () => {
      const battleGame = mockGameList.find((g) => g.gameKind === GameType.Battle)

      wrapper = createWrapper({
        gameList: [battleGame],
        favoriteGameList: [],
        listMode: false
      })
      
      const gameCard = wrapper.findComponent({ name: 'GameCard' })

      expect(gameCard.props('isfav')).toBeUndefined()
    })
  })

  describe('List Mode', () => {
    beforeEach(() => {
      wrapper = createWrapper({
        gameList: mockGameList,
        favoriteGameList: mockFavoriteGameList,
        listMode: true
      })
    })

    it('should render GameList components correctly', () => {
      const gameLists = wrapper.findAllComponents({ name: 'GameList' })

      expect(gameLists.length).toBe(mockGameList.length)
      expect(wrapper.findComponent({ name: 'GameCard' }).exists()).toBe(false)
    })

    it('should pass correct props to GameList components', () => {
      const gameListComp = wrapper.findComponent({ name: 'GameList' })
      const firstGame = mockGameList[0]

      expect(gameListComp.props('gameinfo')).toEqual(firstGame)
      expect(gameListComp.props('isfav')).toBe(true)
      expect(gameListComp.props('isLogin')).toBe(true)
    })

    it('should pass undefined for isfav prop for Battle type games', () => {
      const battleGame = mockGameList.find((g) => g.gameKind === GameType.Battle)
      
      wrapper = createWrapper({
        gameList: [battleGame!],
        favoriteGameList: [],
        listMode: true
      })
      
      const gameListComp = wrapper.findComponent({ name: 'GameList' })

      expect(gameListComp.props('isfav')).toBeUndefined()
    })
  })

  describe('isFavorite function', () => {
    beforeEach(() => {
      wrapper = createWrapper({
        gameList: mockGameList,
        favoriteGameList: mockFavoriteGameList
      })
    })

    it('should return true if the game is in the favorite list', () => {
      expect(wrapper.vm.isFavorite(101)).toBe(true)
    })

    it('should return false if the game is not in the favorite list', () => {
      expect(wrapper.vm.isFavorite(2)).toBe(false)
    })

    it('should return false if favoriteGameList is empty', () => {
      wrapper = createWrapper({
        gameList: mockGameList,
        favoriteGameList: []
      })

      expect(wrapper.vm.isFavorite(1)).toBe(false)
    })

    it('should return false if favoriteGameList is undefined', () => {
      const componentInstance = mount(GameCenter, {
        props: {
          gameList: mockGameList
        }
      }).vm as any

      expect(componentInstance.isFavorite(1)).toBe(false)
    })
  })

  describe('getGamePic function', () => {
    it('should generate the correct game picture URL', () => {
      wrapper = createWrapper()
  
      const game = mockGameList[0]
      const expectedUrl = `https://cdn.example.com/client/static/image/gamePicture/pc/5/101.png?v=**********`
      
      expect(wrapper.vm.getGamePic(game)).toBe(expectedUrl)
    })
  })
})
