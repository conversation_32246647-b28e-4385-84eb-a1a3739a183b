<script setup lang="ts">
import { useBasicInfoStore } from "@/stores/basicInfo"
import { useConfigStore } from '@/stores/config'
import { useAccountInfoStore } from '@/stores/account'
import { VueSpinnerIos } from 'vue3-spinners'
import type { IGameInfo } from '@/types/game'
import { GameType } from '@/types/game'
import GameCard from '@/components/pc/GameCard.vue'
import GameList from '@/components/pc/GameList.vue'
import { storeToRefs } from 'pinia'

const props = defineProps({
  gameList: {
    type: Array as PropType<IGameInfo[]>,
    default: () => [],
    required: false
  },
  favoriteGameList: {
    type: Array as PropType<IGameInfo[]>,
    default: () => [],
    required: false
  },
  listMode: {
    type: Boolean,
    default: false,
    required: false
  },
})

const basicInfoStore = useBasicInfoStore()
const configStore = useConfigStore()
const accountInfoStore = useAccountInfoStore()
const { siteInfo } = storeToRefs(basicInfoStore)
const { accountInfo } = storeToRefs(accountInfoStore)
const { cdnUrl } = storeToRefs(configStore)

const isFavorite = (gameid: number) => {
  return props.favoriteGameList?.some(game => game.gameId === gameid) || false
}

const getGamePic = (game: IGameInfo) => {
  return `${cdnUrl.value}/client/static/image/gamePicture/pc/${game.gameKind}/${game.gameId}.png?v=${siteInfo.value.unixTime}`
}


</script>

<template>
    <!-- Casino/Fishing -->
    <div
      v-if="props.gameList.length > 0"
      :class="props.listMode ? 'list-mode' : 'game-list'"
    >
      <template v-for="game in props.gameList" :key="game.gameId">
        <!-- 列表模式 -->
        <GameList
          v-if="props.listMode"
          :gameinfo="game"
          :isfav="game.gameKind !== GameType['Battle'] ? isFavorite(game.gameId) : undefined"
          :is-login="accountInfo.isLogin"
        />
        <!-- 卡片模式 -->
        <GameCard
          v-else
          :gamepic="getGamePic(game)"
          :gameinfo="game"
          :isfav="game.gameKind !== GameType['Battle'] ? isFavorite(game.gameId) : undefined"
          :cdn-url="cdnUrl"
          :is-login="accountInfo.isLogin"
          :unix-time="siteInfo.unixTime"
        />
      </template>
    </div>
    <div v-else class="loading-wrap">
        <VueSpinnerIos size="50" :color="'var(--main)'" />
    </div>
</template>

<style lang="scss" scoped>

.list-mode {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
}

.game-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  margin: 20px auto;
}

.loading-wrap {
  position: relative;
  height: 55vh;
  .vue-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>