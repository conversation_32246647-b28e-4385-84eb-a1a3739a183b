import { mount, VueWrapper } from '@vue/test-utils'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import GameList from '@/components/pc/GameList.vue'
import type { IGameInfo } from '@/types/game'

const mockAddFavoriteGame = vi.fn()
const mockRemoveFavoriteGame = vi.fn()
const mockAddRecentData = vi.fn()

vi.mock('@/stores/casinoGame', () => ({
  useCasinoGameStore: () => ({
    addFavoriteGame: mockAddFavoriteGame,
    removeFavoriteGame: mockRemoveFavoriteGame,
  }),
}))

vi.mock('@/stores/recentPlay', () => ({
  useRecentlyPlay: () => ({
    addRecentData: mockAddRecentData,
  }),
}))

vi.mock('vue-i18n', () => ({
  useI18n: () => ({
    t: (key: string) => key,
  }),
}))

vi.mock('@icon-park/vue-next', () => ({
  Star: {
    name: 'Star',
    props: ['theme', 'size', 'fill'],
    template: '<span class="mock-star-icon" :data-theme="theme" :data-fill="fill"></span>',
  },
}))

vi.mock('@/components/buttons/GameIntro.vue', () => ({
  default: {
    name: 'GameIntro',
    props: ['gameId', 'currentPage', 'isList'],
    template: '<button class="mock-game-intro">Game Intro</button>',
  },
}))

vi.mock('@/components/buttons/FreeTrial.vue', () => ({
  default: {
    name: 'FreeTrial',
    props: ['isList', 'demoLink'],
    template: '<button class="mock-free-trial">Free Trial</button>',
  },
}))

vi.mock('@/components/buttons/GameEnter.vue', () => ({
  default: {
    name: 'GameEnter',
    props: ['isList'],
    emits: ['click'],
    template: '<button class="mock-game-enter" @click="$emit(\'click\')">Game Enter</button>',
  },
}))

const mockWindowOpen = vi.fn()
global.open = mockWindowOpen

const baseGameInfo: IGameInfo = {
  gameId: 1,
  name: 'Default Game',
  nickname: 'DG',
  icon: 'default-icon',
  link: 'http://example.com/default-play',
  demoLink: 'http://example.com/default-demo',
  gameKind: 5,
  jpAmount: undefined,
  jpImg: undefined
}

describe('GameList', () => {
  let wrapper: VueWrapper<any>

  const createWrapper = (props: Partial<InstanceType<typeof GameList>['$props']> = {}, provideOptions = { nowPage: 'otherPage' }) => {
    const defaultProps = {
      gameinfo: { ...baseGameInfo },
      isfav: false,
      isLogin: false,
    }

    return mount(GameList, {
      props: { ...defaultProps, ...props },
      global: {
        provide: {
          nowPage: provideOptions.nowPage,
        }
      }
    })
  }

  beforeEach(() => {
    setActivePinia(createPinia())
    mockAddFavoriteGame.mockClear()
    mockRemoveFavoriteGame.mockClear()
    mockAddRecentData.mockClear()
    mockWindowOpen.mockClear()
  })

  describe('Rendering', () => {
    it('should render game name correctly', () => {
      wrapper = createWrapper({ gameinfo: { ...baseGameInfo, name: 'Awesome Game' } })

      expect(wrapper.find('.game-name').text()).toBe('Awesome Game')
    })

    it('should render "no-name" class if game name is missing', () => {
      wrapper = createWrapper({ gameinfo: { ...baseGameInfo, name: '' } })

      expect(wrapper.find('.game-name').text()).toBe('(empty)')
      expect(wrapper.find('.game-name').classes()).toContain('no-name')
    })

    it('should render nickname if provided and apply currentPage class', () => {
      wrapper = createWrapper(
        { gameinfo: { ...baseGameInfo, nickname: 'CoolGame' } },
        { nowPage: 'casino' }
      )

      const nicknameEl = wrapper.find('.game-status .nickname')

      expect(nicknameEl.exists()).toBe(true)
      expect(nicknameEl.text()).toBe('CoolGame')
      expect(nicknameEl.classes()).toContain('casino')
    })

    it('should not render nickname if not provided', () => {
      wrapper = createWrapper({ gameinfo: { ...baseGameInfo, nickname: undefined } })

      expect(wrapper.find('.game-status .nickname').exists()).toBe(false)
    })

    it('should render icon text correctly', () => {
      wrapper = createWrapper(
        { gameinfo: { ...baseGameInfo, icon: 'new' } },
        { nowPage: 'fishing' }
      )

      const iconEl = wrapper.find('.game-status .icon')

      expect(iconEl.exists()).toBe(true)
      expect(iconEl.text()).toBe('icon.new')
      expect(wrapper.find('.game-status').classes()).toContain('fishing')
    })

    it('should not render icon if not provided', () => {
      wrapper = createWrapper({ gameinfo: { ...baseGameInfo, icon: '' } })

      expect(wrapper.find('.game-status .icon').exists()).toBe(false)
    })

    describe('Favorite Icon', () => {
      it('should render favorite icon if user is logged in', () => {
        wrapper = createWrapper({ isLogin: true })

        expect(wrapper.find('.fav-icon').exists()).toBe(true)
        expect(wrapper.findComponent({ name: 'Star' }).exists()).toBe(true)
      })

      it('should not render favorite icon if user is not logged in', () => {
        wrapper = createWrapper({ isLogin: false })

        expect(wrapper.find('.fav-icon').exists()).toBe(false)
      })

      it('should render filled star if isfav is true and user is logged in', () => {
        wrapper = createWrapper({ isLogin: true, isfav: true })

        const star = wrapper.findComponent({ name: 'Star' })

        expect(star.attributes('data-theme')).toBe('filled')
        expect(star.attributes('data-fill')).toBe('var(--fav-bg-active)')
      })

      it('should render outline star if isfav is false and user is logged in', () => {
        wrapper = createWrapper({ isLogin: true, isfav: false })

        const star = wrapper.findComponent({ name: 'Star' })

        expect(star.attributes('data-theme')).toBe('outline')
        expect(star.attributes('data-fill')).toBe('var(--main)')
      })

      it('should apply currentPage class to fav-icon', () => {
        wrapper = createWrapper({ isLogin: true }, { nowPage: 'testPage' })

        expect(wrapper.find('.fav-icon').classes()).toContain('testPage')
      })
    })

    describe('Action Buttons', () => {
      it('should render FreeTrial button if not logged in and demoLink exists', () => {
        wrapper = createWrapper({
          isLogin: false,
          gameinfo: { ...baseGameInfo, demoLink: 'some-demo-link' },
        })

        const freeTrialButton = wrapper.findComponent({ name: 'FreeTrial' })

        expect(freeTrialButton.exists()).toBe(true)
        expect(wrapper.findComponent({ name: 'GameEnter' }).exists()).toBe(false)
        expect(freeTrialButton.props('demoLink')).toBe('some-demo-link')
        expect(freeTrialButton.props('isList')).toBe(true)
      })

      it('should render GameEnter button if logged in', () => {
        wrapper = createWrapper({ isLogin: true })

        const gameEnterButton = wrapper.findComponent({ name: 'GameEnter' })

        expect(gameEnterButton.exists()).toBe(true)
        expect(wrapper.findComponent({ name: 'FreeTrial' }).exists()).toBe(false)
        expect(gameEnterButton.props('isList')).toBe(true)
      })

      it('should render GameEnter button if not logged in and demoLink does not exist', () => {
        wrapper = createWrapper({
          isLogin: false,
          gameinfo: { ...baseGameInfo, demoLink: undefined },
        })

        expect(wrapper.findComponent({ name: 'GameEnter' }).exists()).toBe(true)
        expect(wrapper.findComponent({ name: 'FreeTrial' }).exists()).toBe(false)
      })

      it('should render GameIntro button with correct props', () => {
        wrapper = createWrapper(
          { gameinfo: { ...baseGameInfo, gameId: 789 } },
          { nowPage: 'introPage' }
        )

        const gameIntro = wrapper.findComponent({ name: 'GameIntro' })

        expect(gameIntro.exists()).toBe(true)
        expect(gameIntro.props('gameId')).toBe('789')
        expect(gameIntro.props('currentPage')).toBe('introPage')
        expect(gameIntro.props('isList')).toBe(true)
      })
    })
  })

  describe('Interactions', () => {
    describe('handleFavorite', () => {
      const gameIdForFavTest = 456

      beforeEach(() => {
        mockAddFavoriteGame.mockClear()
        mockRemoveFavoriteGame.mockClear()
      })

      it('should call addFavoriteGame when fav-icon is clicked and isfav is false', async () => {
        wrapper = createWrapper({ isLogin: true, isfav: false, gameinfo: { ...baseGameInfo, gameId: gameIdForFavTest } })
        
        await wrapper.find('.fav-icon').trigger('click')
       
        expect(mockAddFavoriteGame).toHaveBeenCalled()
        expect(mockAddFavoriteGame).toHaveBeenCalledWith(456)
        expect(mockRemoveFavoriteGame).not.toHaveBeenCalled()
      })

      it('should call removeFavoriteGame when fav-icon is clicked and isfav is true', async () => {
        wrapper = createWrapper({ isLogin: true, isfav: true, gameinfo: { ...baseGameInfo, gameId: gameIdForFavTest } })
        
        await wrapper.find('.fav-icon').trigger('click')
        
        expect(mockRemoveFavoriteGame).toHaveBeenCalled()
        expect(mockRemoveFavoriteGame).toHaveBeenCalledWith(456)
        expect(mockAddFavoriteGame).not.toHaveBeenCalled()
      })
    })

    describe('openGame', () => {
      const gameToOpen: IGameInfo = { ...baseGameInfo, gameId: 999, link: 'http://example.com/specific-game' }
      
      beforeEach(() => {
        mockWindowOpen.mockClear()
        mockAddRecentData.mockClear()

        wrapper = createWrapper({ isLogin: true, gameinfo: gameToOpen })
      })

      it('should call window.open with correct parameters when GameEnter is clicked', async () => {
        await wrapper.findComponent({ name: 'GameEnter' }).trigger('click')

        expect(mockWindowOpen).toHaveBeenCalled()
        expect(mockWindowOpen).toHaveBeenCalledWith(
          'http://example.com/specific-game',
          '',
          'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no'
        )
      })

      it('should call addRecentData with game info when GameEnter is clicked', async () => {
        await wrapper.findComponent({ name: 'GameEnter' }).trigger('click')

        expect(mockAddRecentData).toHaveBeenCalled()
      })
    })
  })
})
