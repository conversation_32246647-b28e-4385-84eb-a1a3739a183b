<script setup lang="ts">
import { Star } from '@icon-park/vue-next'
import { useI18n } from 'vue-i18n'
import type { IGameInfo } from '@/types/game'
import { useCasinoGameStore } from '@/stores/casinoGame'
import { useRecentlyPlay } from "@/stores/recentPlay"
import GameIntro from '@/components/buttons/GameIntro.vue'
import FreeTrial from '@/components/buttons/FreeTrial.vue'
import GameEnter from '@/components/buttons/GameEnter.vue'

const props = defineProps({
  gameinfo: {
    type: Object as PropType<IGameInfo>,
    default: () => {},
    required: true
  },
  // 最愛（星星icon）是否被選取
  isfav: {
    type: Boolean,
    default: false,
    required: false
  },
  isLogin: {
    type: Boolean,
    default: false,
    required: true
  }
})

const { t } = useI18n()
const recentPlayStore = useRecentlyPlay()
const casinoGameStore = useCasinoGameStore()
const currentPage = inject<string>('nowPage', '')

const gameInfo = computed(() => {
  return props.gameinfo
})

const handleFavorite = async(gameid: number, isfav: boolean) => {
  try {
    if (isfav == false) {
      await casinoGameStore.addFavoriteGame(gameid)
    } else {
      await casinoGameStore.removeFavoriteGame(gameid)
    }
  } catch (err) {
    ElMessage.error((err as Error).message)
  }
}

const addRecent = (game: IGameInfo) => {
  recentPlayStore.addRecentData(game)
}

const openGame = (url: string, game: IGameInfo) => {
  window.open(url, '', 'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no')
  addRecent(game)
}

</script>

<template>
  <div class="gamelist-wrap">
    <div :class="['game-status', currentPage]">
      <div v-if="props.gameinfo.nickname" :class="['nickname', currentPage]">
        {{ props.gameinfo.nickname }}
      </div>
      <div v-if="gameInfo.icon" class="icon">
        {{ t(`icon.${gameInfo.icon.toLowerCase()}`) }}
      </div>
    </div>

    <div
      v-if="props.isLogin"
      :class="['fav-icon', currentPage]"
      @click="handleFavorite(props.gameinfo.gameId, props.isfav)"
    >
      <star v-if="props.isfav" theme="filled" size="18" :fill="'var(--fav-bg-active)'" />
      <star v-else theme="outline" size="18" fill="var(--main)" />
    </div>

    <span class="game-name" :class="gameInfo.name ? '' : 'no-name'">
        {{ gameInfo.name || '(empty)' }}
    </span>

    <div class="listmode-btn">
      <FreeTrial
        v-if="!props.isLogin && gameInfo.demoLink"
        :is-list="true"
        :demo-link="gameInfo.demoLink"
      />
      <GameEnter
        v-else
        :is-list="true"
        @click="openGame(gameInfo.link, gameInfo)"
      />
      <GameIntro :game-id="props.gameinfo.gameId.toString()" :current-page="currentPage" :is-list="true" />
    </div>
  </div>
</template>

<style lang="scss" scoped>

  .gamelist-wrap {
    position: relative;
    width: auto;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-evenly;
    padding: 15px 30px;
    background: #ffffff;
    border-radius: 5px;
    box-shadow: 1px 1px 5px 1px #0000001f;
    margin: 0.5% 0.6%;

    @media (max-width: 800px) {
      padding: 15px 5px 15px 10px;
    }

    .fav-icon {
      display: flex;
      flex: 0.15;
      justify-content: end;

      .i-icon-star {
        cursor: pointer;
      }

      &.card,
      &.fishing {
        display: none;
      }
    }

  .game-name {
    line-height: 18px;
    font-weight: 500;
    text-overflow: ellipsis;
    overflow: hidden;
    text-align: center;
    flex: 0.5;

    &.no-name {
      color: #bbb;
    }

    @media (max-width: 800px) {
      width: 88px;
    }
  }

  .listmode-btn {
    display: flex;
    flex: 0.2;
    justify-content: end;

    .free-btn,
    .enter-btn {
      padding: 0 2px;
      background: white;
      border-color: var(--button-color);

      &:hover {
        background: var(--button-color);
      }
    }
  }

  .game-status {
    position: absolute;
    top: 5px;
    left: 5px;
    opacity: 0.9;

    .icon,
    .nickname {
      width: fit-content;
      text-align: center;
      color: #ffffff;
      font-size: small;
      padding: 5px;
      box-shadow: 1.5px 1.5px 0 rgba(102, 0, 0, 0.3);
      border-radius: 5px;

      @media (max-width: 800px) {
        padding: 3px;
      }
    }

    .icon {
      background: linear-gradient(45deg, #d5373a, #ea4a4a, #ef2f2f);
    }

    .nickname {
      background: linear-gradient(45deg, #43688d, #1a76d2, #43688d);
      &.casino {
        display: none;
      }
    }

    @media (max-width: 800px) {
      top: -3px;
      left: 0px;
      &.fishhing {
        width: 150px;
        display: flex;
        top: -8px;
      }
    }

  }
}

</style>