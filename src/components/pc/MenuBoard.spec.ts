import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import MenuBoard from '@/components/pc/MenuBoard.vue'

class ResizeObserverMock {
  observe() {}
  unobserve() {}
  disconnect() {}
}

vi.stubGlobal('ResizeObserver', ResizeObserverMock)

describe('MenuBoard', () => {
  it('renders correctly', async () => {
    const wrapper = mount(MenuBoard, {
      slots: {
        default: '<div>Slot Content</div>',
      },
    })

    await nextTick()

    expect(wrapper.exists()).toBe(true)
    expect(wrapper.text()).toContain('Slot Content')
  })

  it('becomes fixed when scrolled past the threshold', async () => {
    const wrapper = mount(MenuBoard, {
      slots: {
        default: '<div>Slot Content</div>',
      },
    })

    await nextTick()  
  
    const menuBoard = wrapper.find('.menu-board')
    const parentElement = menuBoard.element.parentElement as HTMLElement
    const getBoundingClientRectMock = vi.spyOn(
      parentElement,
      'getBoundingClientRect'
    )

    getBoundingClientRectMock.mockReturnValue({ top: 86 } as DOMRect)
    window.dispatchEvent(new Event('scroll'))
    await nextTick()

    expect(menuBoard.classes()).toContain('fixed')
    expect(menuBoard.attributes('style')).toContain('width')
  })

  it('is not fixed when scrolled above the threshold', async () => {
    const wrapper = mount(MenuBoard, {
      slots: {
        default: '<div>Slot Content</div>',
      },
    })

    await nextTick()

    const menuBoard = wrapper.find('.menu-board')
    const parentElement = menuBoard.element.parentElement as HTMLElement
    const getBoundingClientRectMock = vi.spyOn(
      parentElement,
      'getBoundingClientRect'
    )

    getBoundingClientRectMock.mockReturnValue({ top: 88 } as DOMRect)
    window.dispatchEvent(new Event('scroll'))
    await nextTick()

    expect(menuBoard.classes()).not.toContain('fixed')
    expect(menuBoard.attributes('style')).toBe('')
  })

  it('cleans up event listeners on unmount', async () => {
    const wrapper = mount(MenuBoard, {
      slots: {
        default: '<div>Slot Content</div>',
      },
    })

    await nextTick()

    const removeEventListenerSpy = vi.spyOn(window, 'removeEventListener')
    const resizeObserverUnobserveSpy = vi.spyOn(
      ResizeObserverMock.prototype,
      'unobserve'
    )

    wrapper.unmount()

    expect(removeEventListenerSpy).toHaveBeenCalledWith(
      'scroll',
      expect.any(Function)
    )
    expect(removeEventListenerSpy).toHaveBeenCalledWith(
      'resize',
      expect.any(Function)
    )
    expect(resizeObserverUnobserveSpy).toHaveBeenCalled()
  })
})
