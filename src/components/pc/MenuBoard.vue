<script setup lang="ts">
const isFixed = ref(false)
const menuBoard = ref()
const menuBoardClientWidth = ref(0)

const scrollEvent = () => {  
  isFixed.value = menuBoard.value?.parentElement.getBoundingClientRect().top < 87
}

const resizeObserver = new ResizeObserver(() => {
  scrollEvent();
})

onMounted(() => {
  window.addEventListener('scroll', scrollEvent)
  window.addEventListener('resize', scrollEvent)
  nextTick(() => {
    menuBoardClientWidth.value = menuBoard.value.clientWidth
    resizeObserver.observe(menuBoard.value.parentElement)
  })
})

onBeforeUnmount(() => {
  window.removeEventListener('scroll', scrollEvent)
  window.removeEventListener('resize', scrollEvent)
  resizeObserver.unobserve(menuBoard.value.parentElement)
})

</script>

<template>
   <div>
      <div 
        class="menu-board"
        :class="{ fixed: isFixed }"
        :style="isFixed ? `width: ${menuBoardClientWidth}px;` : ''"
        ref="menuBoard"
      >
        <slot/>
      </div>
      <div v-if="isFixed" :style="`height: ${menuBoard?.clientHeight}px;`" />
    </div>
</template>

<style lang="scss" scoped>
.menu-board {
  &.fixed {
    position: fixed;
    width: inherit;
    top: 0;
    z-index: 3000;
  }
}
</style>