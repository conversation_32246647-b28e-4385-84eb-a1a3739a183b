import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import SearchBox from './SearchBox.vue'
import { createI18n } from 'vue-i18n'
import { Search } from '@icon-park/vue-next'

const i18n = createI18n({
  locale: 'en',
  messages: {
    en: {
      S_ENTER_GAME_NAME: 'Enter game name',
    },
  },
})

describe('SearchBox', () => {
  it('renders correctly with show false', () => {
    const wrapper = mount(SearchBox, {
      global: {
        plugins: [i18n],
      },
      props: {
        data: '',
        show: false,
      },
    })

    expect(wrapper.exists()).toBe(true)

    const search = wrapper.findComponent(Search)
    expect(search.exists()).toBe(true)
    expect(search.classes()).not.toContain('active')
    expect(search.props('fill')).toBe('var(--icon-bg)')

    expect(wrapper.find('.search-input').exists()).toBe(false)
  })

  it('renders correctly with show true', () => {
    const wrapper = mount(SearchBox, {
      global: {
        plugins: [i18n],
      },
      props: {
        data: '',
        show: true,
      },
    })

    const search = wrapper.findComponent(Search)
    expect(search.exists()).toBe(true)
    expect(search.classes()).toContain('active')
    expect(search.props('fill')).toBe('var(--icon-bg-active)')

    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.search-input').exists()).toBe(true)
  })

  it('toggles show state on Search icon click', async () => {
    const wrapper = mount(SearchBox, {
      global: {
        plugins: [i18n],
      },
      props: {
        data: '',
        show: false,
      },
    })

    await wrapper.vm.$nextTick()
    await nextTick()
    await wrapper.findComponent(Search).trigger('click')

    expect(wrapper.emitted('update:show')).toEqual([[true]]);
    expect(wrapper.find('.search-input').exists()).toBe(true)

    await wrapper.findComponent(Search).trigger('click')

    expect(wrapper.emitted('update:show')).toEqual([[true], [false]]);
    expect(wrapper.find('.search-input').exists()).toBe(false)
  })

  it('displays the correct placeholder', () => {
    const wrapper = mount(SearchBox, {
      global: {
        plugins: [i18n],
      },
      props: {
        data: '',
        show: true,
      },
    })

    const input = wrapper.find('input')
    expect(input.attributes('placeholder')).toBe('Enter game name')
  })

  it('emits the correct data when input changes', async () => {
    const wrapper = mount(SearchBox, {
      global: {
        plugins: [i18n],
      },
      props: {
        data: '',
        show: true,
      },
    })

    const input = wrapper.find('input')
    await input.setValue('test')
    expect(wrapper.emitted('update:data')).toEqual([['test']]);
  })
})