<script setup lang="ts">
import { Search } from '@icon-park/vue-next'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const data = defineModel('data', {
  type: String,
  required: true,
  default: ''
});

const show = defineModel('show', {
  type: Boolean,
  required: true,
  default: false
});
</script>

<template>
  <div>
    <Search
      :class="show ? 'active' : ''"
      theme="outline"
      size="25"
      :fill="show ? 'var(--icon-bg-active)' : 'var(--icon-bg)'"
      @click="show = !show"
    />
    <div v-if="show" class="search-input active">
      <el-input
        v-model.trim="data"
        :placeholder="t('S_ENTER_GAME_NAME')"
      />
    </div>
  </div>
</template>