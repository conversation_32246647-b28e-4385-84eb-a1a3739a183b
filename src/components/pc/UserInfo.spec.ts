import { mount } from '@vue/test-utils'
import { setActive<PERSON>inia, createPinia } from 'pinia'
import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest'
import { nextTick } from 'vue'
import { useAccountInfoStore } from '@/stores/account'
import UserInfo from "./UserInfo.vue"

vi.mock('@/stores/account', () => ({
  useAccountInfoStore: vi.fn()
}))

describe('BasicInfo component', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  afterEach(() => {
    vi.resetAllMocks();
  })

  it('should display the user avatar and name correctly when the user is logged in', async () => {
    vi.mocked(useAccountInfoStore).mockReturnValue({
      accountInfo: ref({
        alias: 'testUser',
        balance: 100,
        bankrupt: false,
        block: false,
        currency: 'CNY',
        enable: true,
        test: false,
        username: 'testUser',
        isLogin: true
      })
    } as any)

    const wrapper = mount(UserInfo)

    await nextTick()
    await wrapper.vm.$nextTick();

    expect(wrapper.find('.account-name').text()).toBe('testUser')
    expect(wrapper.find('.bbin-balance').text()).toBe('100')
  })

  it('should display the ballguest name correctly when the user is not logged in', async () => {
    vi.mocked(useAccountInfoStore).mockReturnValue({
      accountInfo: ref({
        username: 'ballguest',
        isLogin: false
      })
    } as any)

    const wrapper = mount(UserInfo);

    await nextTick()
    await wrapper.vm.$nextTick();

    expect(wrapper.find('.account-name').text()).toBe('ballguest')
    expect(wrapper.find('.bbin-balance').exists()).toBe(false)
  })
})  