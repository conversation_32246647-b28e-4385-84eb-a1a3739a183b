<script setup lang="ts">
import { useAccountInfoStore } from "@/stores/account"
import { ElAvatar } from "element-plus"
import { storeToRefs } from 'pinia'

const accountInfoStore = useAccountInfoStore()
const { accountInfo } = storeToRefs(accountInfoStore)

</script>

<template>
  <ElAvatar
      class="avatar"
      src="/client/site/default/image/user.png"
  />
  <span class="account-name">{{ accountInfo.username }}</span>
  <span v-if="accountInfo.isLogin" class="bbin-balance">{{ accountInfo.balance.toLocaleString() }}</span>
</template>

<style lang="scss" scoped>
.avatar {
  position: absolute;
  top: 10px;
  width: 4vw;
  height: 4vw;
  border: 2px solid var(--main);
  box-shadow: 1px 1px 5px 1px #0000001f;
}

.account-name,
.bbin-balance {
  display: inline-block;
  position: relative;
  left: 10%;
  margin-left: 15px;
}

.account-name {
  color: var(--main);
}

.bbin-balance {
  color: var(--main);
}
</style>