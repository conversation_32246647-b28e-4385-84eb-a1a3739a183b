import { mount, VueWrapper } from '@vue/test-utils'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import bbSwitchBoard from './bbSwitchBoard.vue'

let mockRoute: { name: string }
const mockPush = vi.fn()
vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
  useRoute: () => mockRoute
}))


vi.mock('@/composables/useImportTheme', () => ({
  useImportTheme: () => ({
    importStyle: vi.fn(),
  }),
}))

const mockPcMenuList = [
  {
    gameId: 1,
    name: 'Other Game',
    sub: { other: { nickname: 'Other' } },
  },
  {
    gameId: 5,
    name: 'BBIN',
    sub: {
      bbcasino: { nickname: 'BB', gameType: 1, maintain: false },
      fisharea: { nickname: 'Fishing', gameType: 2, maintain: false },
    },
  },
]

vi.mock('@/stores/basicInfo', () => ({
  useBasicInfoStore: vi.fn(() => ({
    siteInfo: ref({ unixTime: '1234567890' }),
  })),
}))

vi.mock('@/stores/config', () => ({
  useConfigStore: vi.fn(() => ({
    cdnUrl: ref('https://cdn.example.com'),
  })),
}))

vi.mock('@/stores/lobbySwitch', () => ({
  useLobbySwitchStore: vi.fn(() => ({
    pcMenuList: ref(mockPcMenuList)
  })),
}))

describe('bbSwitchBoard', () => {
  let wrapper: VueWrapper<any>
  
  beforeEach(() => {
    setActivePinia(createPinia())
    mockPush.mockClear()
  })

  const createWrapper = (routeName: string) => {
    mockRoute = { name: routeName }
    
    return mount(bbSwitchBoard)
  }

  it('renders correctly with initial data', () => {
    wrapper = createWrapper('someOtherRoute')
    const buttons = wrapper.findAll('.bbswitch-button')

    expect(buttons.length).toBe(2)
    
    const casinoButton = buttons[0]
    
    expect(casinoButton.find('span').text()).toBe('BB')
    expect(casinoButton.find('.bbcasino-icon').exists()).toBe(true)
    expect(casinoButton.find('.bbcasino-icon').attributes('style')).toContain(
      `background-image: url(https://cdn.example.com/client/static/image/hall/casino/category/nav_logo.png?v=1234567890)`
    )

    const fishingButton = buttons[1]

    expect(fishingButton.find('span').text()).toBe('Fishing')
    expect(fishingButton.find('.fisharea-icon').exists()).toBe(true)
    expect(fishingButton.find('.fisharea-icon').attributes('style')).toContain(
      `background-image: url(https://cdn.example.com/client/static/image/hall/casino/category/nav_logo.png?v=1234567890)`
    )
  })

  it('computes activeSelect correctly for "casino" route', () => {
    wrapper = createWrapper('casino')

    expect(wrapper.vm.activeSelect).toBe('bbcasino')
    
    const casinoButtonSpan = wrapper.findAll('.bbswitch-button span')[0]
    
    expect(casinoButtonSpan.classes()).toContain('active')
  })

  it('computes activeSelect correctly for "fishing" route', () => {
    wrapper = createWrapper('fishing')
    
    expect(wrapper.vm.activeSelect).toBe('fisharea')
    
    const fishingButtonSpan = wrapper.findAll('.bbswitch-button span')[1]
    
    expect(fishingButtonSpan.classes()).toContain('active')
  })

  it('computes activeSelect correctly for other routes', () => {
    wrapper = createWrapper('home')
   
    expect(wrapper.vm.activeSelect).toBe('')
    
    const spans = wrapper.findAll('.bbswitch-button span')
    
    expect(spans[0].classes()).not.toContain('active')
    expect(spans[1].classes()).not.toContain('active')
  })

  it('computes selectList correctly', () => {
    wrapper = createWrapper('casino')
    
    expect(Object.keys(wrapper.vm.selectList).length).toBe(2)
    expect(wrapper.vm.selectList.bbcasino.nickname).toBe('BB')
    expect(wrapper.vm.selectList.fisharea.nickname).toBe('Fishing')
  })

  it('calls changeRoute when BB button is clicked', async () => {
    wrapper = createWrapper('fishing')
    
    const casinoButton = wrapper.findAll('.bbswitch-button')[0]
    
    await casinoButton.trigger('click')
    
    expect(mockPush).toBeCalled()
    expect(mockPush).toHaveBeenCalledWith({ name: 'casino' })
  })

  it('calls changeRoute when Fishing button is clicked', async () => {
    wrapper = createWrapper('casino')
    
    const fishingButton = wrapper.findAll('.bbswitch-button')[1]
    
    await fishingButton.trigger('click')
    
    expect(mockPush).toBeCalled()
    expect(mockPush).toHaveBeenCalledWith({ name: 'fishing' })
  })
})
