<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router'
import { useBasicInfoStore } from "@/stores/basicInfo"
import { useConfigStore } from '@/stores/config'
import { useLobbySwitchStore } from '@/stores/lobbySwitch'
import { useImportTheme } from '@/composables/useImportTheme'
import { storeToRefs } from 'pinia'

const { importStyle } = useImportTheme()
const router = useRouter()
const route = useRoute()
const basicInfoStore = useBasicInfoStore()
const configStore = useConfigStore()
const lobbySwitchStore = useLobbySwitchStore()
const { siteInfo } = storeToRefs(basicInfoStore) 
const { cdnUrl } = storeToRefs(configStore)
const { pcMenuList } = storeToRefs(lobbySwitchStore)

const activeSelect = computed(() => {
  if (route.name == 'fishing') {
    return 'fisharea'
  }
  if (route.name == 'casino') {
    return 'bbcasino'
  }
  return ''
})

const selectList = computed(() => {
  return (pcMenuList.value.find(item => item.gameId === 5)?.sub  || {}) as {[key: string]: any }
})

const changeRoute = (location: string) => {
  switch (location) {
    case 'BB':
      router.push({name: 'casino'})
      break;

    case 'Fishing':     
      router.push({name: 'fishing'})
      break;
  }
}

importStyle('pc/bbSwitchBoard')
</script>

<template>
    <div class="bbswitch-group">
      <template v-for="(item, key) in selectList" :key="key">
      <div
        class="bbswitch-button"
        @click="changeRoute(item.nickname)"
      >
        <div
          :class="`${key}-icon`"
          :style="{
            backgroundImage: `url(${cdnUrl}/client/static/image/hall/casino/category/nav_logo.png?v=${siteInfo.unixTime})`
          }"
        />
        <span :class="{ active: activeSelect === key }">{{ item.nickname }}</span>
      </div>
    </template>
    </div>
</template>

<style lang="scss" scoped>
.bbswitch-group {
  display: flex;
  background: var(--pc-bbSwitchBoard-group-bg);

  .bbswitch-button {
    margin: 5px 8px;
    display: flex;
    align-items: center;
    cursor: pointer;

    span {
      font-size: 1.2rem;
      font-weight: 400;
      color: var(--main);
      letter-spacing: 1.2px;
      margin: 5px;

      &:hover {
        color: var(--pc-bbSwitchBoard-group-text-active);
      }
      
      &.active {
        color: var(--pc-bbSwitchBoard-group-text-active);
        font-weight: bold;
      }
    }
  }

  .bbcasino-icon {
    width: 22px;
    height: 22px;
    background-position: -110px 0;
  }

  .fisharea-icon {
    width: 22px;
    height: 22px;
    background-position: -88px 0;
  }
}
</style>