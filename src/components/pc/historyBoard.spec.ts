import { mount, VueWrapper } from '@vue/test-utils'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import HistoryBoard from './historyBoard.vue'
import { createPinia, setActivePinia } from 'pinia'
import { nextTick } from 'vue'
import type { IGameInfo } from '@/types/game'
import { GameType } from '@/types/game'

vi.mock('vue-i18n', () => ({
  useI18n: () => ({
    t: (key: string) => key
  })
}))


vi.mock('@/composables/useGetImgUrl', () => ({
  useGetImgUrl: () => ({
    getImageUrl: (name: string) => `mocked_img_url/${name}`
  })
}))

const mockImportStyle = vi.fn()
vi.mock('@/composables/useImportTheme', () => ({
  useImportTheme: () => ({
    importStyle: mockImportStyle
  })
}))

vi.mock('@/stores/basicInfo', () => ({
  useBasicInfoStore: vi.fn(() => ({
    siteInfo: ref({ unixTime: '1234567890' }),
  })),
}))

vi.mock('@/stores/config', () => ({
  useConfigStore: vi.fn(() => ({
    cdnUrl: ref('https://cdn.example.com'),
  })),
}))

const mockRecentPlayList = ref<IGameInfo[]>([])
const mockAddRecentData = vi.fn()
vi.mock('@/stores/recentPlay', () => ({
  useRecentlyPlay: () => ({
    recentPlayList: mockRecentPlayList,
    addRecentData: mockAddRecentData,
  }),
}))

vi.mock('@icon-park/vue-next', () => ({
  History: { name: 'History', template: '<span class="mock-history-icon" />' },
  Right: { name: 'Right', template: '<span class="mock-right-icon" />' },
  Left: { name: 'Left', template: '<span class="mock-left-icon" />' },
  Up: { name: 'Up', template: '<span class="mock-up-icon" />' },
  Down: { name: 'Down', template: '<span class="mock-down-icon" />' }
}))

const mockWindowFocus = vi.fn()
const mockWindowOpen = vi.fn().mockReturnValue({ focus: mockWindowFocus })
global.open = mockWindowOpen


const mockObserve = vi.fn()
const mockUnobserve = vi.fn()
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: mockObserve,
  unobserve: mockUnobserve,
  disconnect: vi.fn(),
}))

const mockGame1: IGameInfo = { gameId: 1, name: 'Game 1', link: 'http://game1.com', gameKind: GameType.Casino, demoLink: 'http://demo1.com', nickname: 'G1', icon: 'Hot' }
const mockGame2: IGameInfo = { gameId: 2, name: 'Game 2', link: 'http://game2.com', gameKind: GameType.BBlive, demoLink: 'http://demo2.com', nickname: 'G2', icon: 'New' }

describe('HistoryBoard', () => {
  let wrapper: VueWrapper<any>

  beforeEach(() => {
    setActivePinia(createPinia())
  })

  const setupStoreAndMount = (recentGames: IGameInfo[] = []) => {
    mockRecentPlayList.value = recentGames
    
    wrapper = mount(HistoryBoard, {
      global: {
        stubs: {
          transition: false,
        },
      },
    })
  }

  beforeEach(() => {
    vi.spyOn(window, 'addEventListener')
    vi.spyOn(window, 'removeEventListener')

    mockWindowOpen.mockClear()
    mockObserve.mockClear()
    mockUnobserve.mockClear()
    mockImportStyle.mockClear()
    mockAddRecentData.mockClear()
  })

  afterEach(() => {
    delete (window as any).innerHeight
  })

  it('should call importStyle on setup', () => {
    setupStoreAndMount()

    expect(mockImportStyle).toHaveBeenCalledWith('pc/historyBoard')
  })

  it('should render collapsed initially and toggle visibility on click', async () => {
    setupStoreAndMount()

    expect(wrapper.find('.slide-container').isVisible()).toBe(false)
    expect(wrapper.findComponent({ name: 'up' }).exists()).toBe(true)
    expect(wrapper.findComponent({ name: 'down' }).exists()).toBe(false)

    const toggleButton = wrapper.find('.recent-header')
    await toggleButton.trigger('click')

    expect(wrapper.findComponent({ name: 'up' }).exists()).toBe(false)
    expect(wrapper.findComponent({ name: 'down' }).exists()).toBe(true)

    await toggleButton.trigger('click')

    expect(wrapper.findComponent({ name: 'up' }).exists()).toBe(true)
  })

  it('should display "no data" message when recentPlayList is empty and board is expanded', async () => {
    setupStoreAndMount([])
    
    await wrapper.find('.recent-header').trigger('click')
    
    expect(wrapper.find('.no-data').exists()).toBe(true)
    expect(wrapper.find('.no-data').text()).toBe('S_NO_DATA_CL')
    expect(wrapper.find('.recent-game-list').exists()).toBe(false)
  })

  it('should display recent games when recentPlayList is not empty and board is expanded', async () => {
    setupStoreAndMount([mockGame1, mockGame2])
    const vm = wrapper.vm

    await wrapper.find('.recent-header').trigger('click')
    await nextTick()

    if (vm.$refs.gamecontainer) {
      Object.defineProperty(vm.$refs.gamecontainer, 'scrollWidth', { value: 500, configurable: true })
      Object.defineProperty(vm.$refs.gamecontainer, 'clientWidth', { value: 300, configurable: true })
      Object.defineProperty(vm.$refs.gamecontainer, 'scrollLeft', { value: 0, writable: true, configurable: true })
      vm.detectScrollWidth()

      await nextTick()
    }
    
    expect(wrapper.find('.no-data').exists()).toBe(false)
    expect(wrapper.find('.recent-game-list').exists()).toBe(true)

    const gameItems = wrapper.findAll('.recent-game-list li')
    
    expect(gameItems.length).toBe(2)
    expect(gameItems[0].find('a').text()).toBe('Game 1')
    expect(gameItems[1].find('a').text()).toBe('Game 2')
    
    const gamePics = wrapper.findAllComponents({ name: 'GamePic' })
    
    expect(gamePics[0].props('gamePic')).toBe(`https://cdn.example.com/client/static/image/gamePicture/pc/5/1.png?v=1234567890`)
    expect(gamePics[1].props('gamePic')).toBe(`https://cdn.example.com/client/static/image/gamePicture/pc/3/2.png?v=1234567890`)
  })

  it('should open game and add to recent plays on game item click', async () => {
    setupStoreAndMount([mockGame1])

    await wrapper.find('.recent-header').trigger('click')
    await nextTick()

    const gameItem = wrapper.find('.recent-game-list li')
    await gameItem.trigger('click')

    expect(window.open).toHaveBeenCalledWith(
      'http://game1.com',
      '',
      'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no'
    )
    expect(mockAddRecentData).toHaveBeenCalled()
  })

  it('should handle scrolling and update button visibility correctly', async () => {
    setupStoreAndMount([mockGame1, mockGame2, { ...mockGame1, gameId: 3 }, { ...mockGame2, gameId: 4 }, { ...mockGame1, gameId: 5 }])
    const vm = wrapper.vm

    await wrapper.find('.recent-header').trigger('click')
    await nextTick() 

    let currentScrollLeft = 0
    const gameListElement = wrapper.vm.gamecontainer as HTMLDivElement

    Object.defineProperty(gameListElement, 'scrollWidth', { configurable: true, writable: true, value: 600 })
    Object.defineProperty(gameListElement, 'clientWidth', { configurable: true, writable: true, value: 250 })
    Object.defineProperty(gameListElement, 'scrollLeft', {
      configurable: true,
      get: () => currentScrollLeft,
      set: (val: number) => {
        currentScrollLeft = val
      }
    })

    gameListElement.scrollLeft = 0
    
    vm.detectScrollWidth()
    await nextTick()

    expect(vm.showRightBtn).toBe(true)
    expect(vm.showLeftBtn).toBe(false)
    expect(currentScrollLeft).toBe(0)

    await wrapper.findComponent({ name: 'Right' }).trigger('click')
    
    expect(currentScrollLeft).toBe(200)
    expect(vm.showLeftBtn).toBe(true)
    expect(vm.showRightBtn).toBe(true)

    await wrapper.findComponent({ name: 'Right' }).trigger('click')
    
    expect(currentScrollLeft).toBe(400)
    expect(vm.showLeftBtn).toBe(true)
    expect(vm.showRightBtn).toBe(true)
    
    await wrapper.findComponent({ name: 'Left' }).trigger('click')
   
    expect(currentScrollLeft).toBe(200)
    expect(vm.showLeftBtn).toBe(true)
    expect(vm.showRightBtn).toBe(true) 

    await wrapper.findComponent({ name: 'Left' }).trigger('click')
    
    expect(currentScrollLeft).toBe(0)
    expect(vm.showLeftBtn).toBe(false)
    expect(vm.showRightBtn).toBe(true)
  })

  it('should toggle fixed position and placeholder based on scroll events', async () => {
    setupStoreAndMount()
    const vm = wrapper.vm
    const mockGetBoundingClientRect = vi.fn()

    const historyBoardElement = wrapper.vm.historyBoard as HTMLDivElement

    Object.defineProperty(historyBoardElement, 'clientWidth', { configurable: true, writable: true, value: 800 })
    Object.defineProperty(historyBoardElement, 'clientHeight', { configurable: true, writable: true, value: 120 })
    Object.defineProperty(historyBoardElement, 'parentElement', { configurable: true, value: { getBoundingClientRect: mockGetBoundingClientRect } })
    Object.defineProperty(window, 'innerHeight', { value: 600, configurable: true, writable: true })
    mockGetBoundingClientRect.mockReturnValue({ bottom: 700 })

    vm.scrollEvent()
    await nextTick()

    expect(vm.isFixed).toBe(true)
    expect(wrapper.find('.history-board').classes('fixed')).toBe(true)    
    expect(wrapper.find('[style="height: 120px;"]').exists()).toBe(true)

    mockGetBoundingClientRect.mockReturnValue({ bottom: 500 })

    vm.scrollEvent()
    await nextTick()

    expect(vm.isFixed).toBe(false)
    expect(wrapper.find('.history-board').classes('fixed')).toBe(false)
    expect(wrapper.find('[style="height: 120px;"]').exists()).toBe(false)
  })

  it('should setup and cleanup event listeners and ResizeObserver', async () => {
    let observedElement: Element | null = null
    mockObserve.mockImplementation((el) => { observedElement = el })

    setupStoreAndMount()
    const vm = wrapper.vm
  
    const originalScrollEvent = vm.scrollEvent

    await nextTick()

    expect(window.addEventListener).toHaveBeenCalledWith('scroll', originalScrollEvent)
    expect(window.addEventListener).toHaveBeenCalledWith('resize', originalScrollEvent)
    
    expect(mockObserve).toHaveBeenCalled()
    const historyBoardElement = wrapper.find('.history-board').element
    expect(observedElement).toBe(historyBoardElement.parentElement)
    expect(vm.historyBoardClientWidth).toBe(historyBoardElement.clientWidth)

    wrapper.unmount()
    
    expect(window.removeEventListener).toHaveBeenCalledWith('scroll', originalScrollEvent)
    expect(window.removeEventListener).toHaveBeenCalledWith('resize', originalScrollEvent)
    expect(mockUnobserve).toHaveBeenCalled()
  })
})

