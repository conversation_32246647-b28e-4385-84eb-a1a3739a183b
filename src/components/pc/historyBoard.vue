<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { History, Right, Left, Up, Down } from '@icon-park/vue-next'
import { useGetImgUrl } from '@/composables/useGetImgUrl'
import { useBasicInfoStore } from "@/stores/basicInfo"
import { useConfigStore } from '@/stores/config'
import { useRecentlyPlay } from "@/stores/recentPlay"
import { storeToRefs } from 'pinia'
import type { IGameInfo } from '@/types/game'
import { useImportTheme } from '@/composables/useImportTheme'
import GamePic from '@/components/GamePic.vue'
import { nextTick } from 'vue'

const { t } = useI18n()
const gamecontainer: globalThis.Ref = ref(null)
const { getImageUrl } = useGetImgUrl()
const basicInfoStore = useBasicInfoStore()
const configStore = useConfigStore()
const recentPlayStore = useRecentlyPlay()
const { siteInfo } = storeToRefs(basicInfoStore)
const { cdnUrl } = storeToRefs(configStore)
const { recentPlayList } = storeToRefs(recentPlayStore)
const showLeftBtn = ref(false)
const showRightBtn = ref(false)
const maxScrollLeft = ref(0)
const containerVisible = ref(false)
const isFixed = ref(false)
const historyBoard = ref()
const historyBoardClientWidth = ref(0)
const { importStyle } = useImportTheme()

const openGame = (url: string, game: IGameInfo) => {
  window.open(url, '', 'top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no')
  recentPlayStore.addRecentData(game)
}

const scrollRight = () => {
  gamecontainer.value.scrollLeft += 200

  if (gamecontainer.value.scrollLeft == 0) {
    showLeftBtn.value = false
  } else if ( Math.round(gamecontainer.value.scrollLeft) == maxScrollLeft.value) {
      showRightBtn.value = false
      showLeftBtn.value = true
  } else {
    showLeftBtn.value = true
  }
}

const scrollLeft = () => {
  gamecontainer.value.scrollLeft -= 200

  if (gamecontainer.value.scrollLeft > 0 && gamecontainer.value.scrollLeft < maxScrollLeft.value) {
    showLeftBtn.value = true
  } else if (gamecontainer.value.scrollLeft == 0) {
    showLeftBtn.value = false
    showRightBtn.value = true
  } else {
    showLeftBtn.value = false
  }
}

const detectScrollWidth = () => {
  if( recentPlayList.value.length > 0 && gamecontainer.value.scrollWidth ) {
      maxScrollLeft.value = gamecontainer.value.scrollWidth - gamecontainer.value.clientWidth
  }
  if(maxScrollLeft.value > 0) {
    showRightBtn.value = true
  }
}

const onToggleClick = async () => {
  containerVisible.value = !containerVisible.value
  
  if (containerVisible.value) {
    await nextTick()
    detectScrollWidth()
  }
};

const scrollEvent = () => {  
  isFixed.value = historyBoard.value?.parentElement.getBoundingClientRect().bottom > window.innerHeight;
};

const resizeObserver = new ResizeObserver(() => {
  scrollEvent();
});

watch( recentPlayList, async () => {
  await nextTick()
  detectScrollWidth()
})

onMounted(() => {
  window.addEventListener('scroll', scrollEvent)
  window.addEventListener('resize', scrollEvent)
  nextTick(() => {
    historyBoardClientWidth.value = historyBoard.value.clientWidth
    resizeObserver.observe(historyBoard.value.parentElement)
  });
});

onBeforeUnmount(() => {
  window.removeEventListener('scroll', scrollEvent)
  window.removeEventListener('resize', scrollEvent)
  resizeObserver.unobserve(historyBoard.value.parentElement)
});

importStyle('pc/historyBoard')
</script>

<template>
  <div 
    class="history-board"
    :class="{ fixed: isFixed }"
    :style="isFixed ? `width: ${historyBoardClientWidth}px;` : ''"
    ref="historyBoard"
  >
    <div class="recent-header" @click="onToggleClick">
      <span class="toggle">
        <up
          v-if="!containerVisible"
          class="right-btn"
          theme="filled"
          size="30"
          fill="#fff"
        />
        <down
          v-else
          class="right-btn"
          theme="filled"
          size="30"
          fill="#fff"
        />
      </span>
      <div class="divider"></div>
    </div>
    <transition name="history-board-slide">
      <div v-show="containerVisible" class="slide-container">
        <div class="recent-container">
          <div class="recent-title">
            <history 
              theme="outline"
              size="40"
              fill="var(--pc-historyBoard-recent-title-text)"
            />
            <span>{{ t('S_RECENTLY_PLAYED') }}</span>
          </div>
          <div v-if="recentPlayList.length > 0" ref="gamecontainer" class="recent-game-list">
            <template v-for="game in recentPlayList" :key="game.gameId">
              <li @click="openGame(game.link, game)">
                <GamePic
                  :game-pic="`${cdnUrl}/client/static/image/gamePicture/pc/${game.gameKind}/${game.gameId}.png?v=${siteInfo.unixTime}`"
                  :failed-pic="getImageUrl('failed.png')"
                />
                <a>{{ game.name }}</a>
              </li>
            </template>
          </div>
          <div v-else class="no-data">
            {{ t('S_NO_DATA_CL') }}
          </div>
          <div class="control-btn">
            <right
              v-if="showRightBtn"
              class="right-btn"
              theme="filled"
              size="50"
              fill="var(--pc-historyBoard-recent-game-list-text)"
              @click="scrollRight()"
            />
            <left
              v-if="showLeftBtn"
              class="right-btn"
              theme="filled"
              size="50"
              fill="var(--pc-historyBoard-recent-game-list-text)"
              @click="scrollLeft()"
            />
          </div>
        </div>
      </div>
    </transition>
  </div>
  <div v-if="isFixed" :style="`height: ${historyBoard?.clientHeight}px;`" />
</template>

<style lang="scss" scoped>
.history-board {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 3000;

  &.fixed {
    position: fixed;
    width: inherit;
    bottom: 60px;
  }

  .recent-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0;

    .divider {
      width: 100%;
      height: 30px;
      background: #812224;
    }

    .toggle {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 65px;
      height: 35px;
      border-radius: 35px 35px 0 0;
      background: #812224;
      position: relative;
      top: 1px;
    }
  }

  .slide-container {
    height: 120px;
    background: white;
  }

  .recent-container {
    background: var(--pc-historyBoard-recent-container-bg);
    height: 120px;
    display: flex;
    justify-content: space-around;
    align-items: center;

    .recent-title {
      width: 10%;
      padding: 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      font-size: 1.1rem;
      color: var(--pc-historyBoard-recent-title-text);
      span {
        margin: 2px;
      }
    }

    .please-login {
      width: 90%;
      text-align: center;
      color: var(--text-nonactive);
    }

    .recent-game-list {
      overflow-x: scroll; 
      display: flex;
      &::-webkit-scrollbar {
        display: none;
      }
      flex: 1;

      li {
        list-style: none;
        margin: 5px;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        color: var(--pc-historyBoard-recent-game-list-text);
        cursor: pointer;
        &:hover {
          color: var(--pc-historyBoard-historyBoard-text-active);
        }
      }
      .el-image {
        width: 70px;
        height: 70px;
        overflow: hidden;
        border-radius: 5px;
        margin: 5px;
      }
      :deep(.el-image) {
        img {
          object-fit: cover;
          object-position: center;
        }
      }
    }

    .no-data {
      width: 100%;
      color: var(--text-nonactive);
      text-align: center;
      flex: 1;
    }

    .control-btn {
      margin: auto;
      display: flex;
      flex-direction: column;
    }

    .right-btn, .left-btn {
      cursor: pointer;
    }
  }
}

// historyBoard slid
.history-board-slide-enter-from,
.history-board-slide-leave-to {
  height: 0 !important;
}
.history-board-slide-enter-active,
.history-board-slide-leave-active {
  transition: all 0.3s ease-out;
}
</style>