<script setup lang="ts">
import { VueSpinnerIos } from 'vue3-spinners'
import { useGetImgUrl } from '@/composables/useGetImgUrl'
import { useI18n } from 'vue-i18n'
import { DoubleUp, DoubleDown } from '@icon-park/vue-next'
import { useEjpStore } from '@/stores/ejpremote'
import { storeToRefs } from 'pinia'

const props = defineProps({
  showRank: {
    type: Boolean,
    default: true,
    required: false
  },
  rankHeight: {
    type: String,
    default: '100%',
    required: false
  },
})

const { t } = useI18n()
const ejpStore = useEjpStore()
const { jpAmount, topList } = storeToRefs(ejpStore)
const { getImageUrl } = useGetImgUrl()
const dialogTableVisible = ref(false)

const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex < 10) {
    return 'top-10'
  } else if (rowIndex % 2 == 1) {
    return 'warning-row'
  } else {
    return 'success-row'
  }
}

const showRankBoard = ref(true)

</script>

<template>
  <div v-if="showRankBoard === false" class="jackpot-btn" :class="showRankBoard? '' : 'toggle-close' " @click="showRankBoard = true">
    {{ 'JACKPOT' }}
    <double-down theme="outline" size="30" fill="#FEE398" :stroke-width="4"/>
  </div>
  <div v-else :class="props.showRank ? 'top-container' : ''">
    <div class="title"></div>
    <div class="jpamount">{{ jpAmount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</div>
    <div v-if="props.showRank" class="list">
      <div v-if="topList.length == 0" class="loading-wrap">
        <VueSpinnerIos size="30" color="#fff" />
      </div>
      <template v-for="(top, idx) in topList" :key="top.name">
        <div v-if="idx < 10" :class="idx === 0 ? 'top1' : 'others'">
          <div
            class="num"
            :style="{
              background: `url(${getImageUrl('jp_num.png')}) 0 -${idx * 32.2}px no-repeat`
            }"
          ></div>
          <span>{{ top.name }}</span>
          {{ top.score }}
        </div>
      </template>
    </div>
    <div v-if="props.showRank" class="more-btn" @click.stop="dialogTableVisible = true">{{ t('S_MORE')}}</div>
    <div class="toggle-btn" @click="showRankBoard = false">
      <double-up theme="outline" size="30" fill="#FEE398" :stroke-width="4"/>
    </div>
    <el-dialog v-model="dialogTableVisible" title="Top Rank" width="600" :align-center="true">
      <el-table
        width="200"
        :data="topList"
        header-cell-class-name="head-color"
        :row-class-name="tableRowClassName"
        :row-style="{ height: '0' }"
        :cell-style="{ padding: '0', textAlign: 'center' }"
      >
        <el-table-column type="index" />
        <el-table-column property="name" label="Name" />
        <el-table-column property="score" label="Score" />
      </el-table>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>

.jackpot-btn {
  width: 115px;
  margin-left: auto;
  display: flex;
  align-items: center;
  background: #9B3234;
  color: #ffe599;
  font-weight: bold;
  border-radius: 10px 10px 0 0;
  padding: 5px 10px;
  cursor: pointer;

  &.toggle-close {
    position: absolute;
    top: -40px;
    right: -2px;
    cursor: pointer;
  }
}

.top-container {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 100%;
  height: v-bind(rankHeight);
  background: #812224;

  .toggle-btn {
    width: 30px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #BB373A;
    color: #FEE398;
    font-weight: bold;
    cursor: pointer;
  }

  .title {
    margin: 0 5px;
    display: block;
    position: relative;
    width: 70px;
    height: 20px;
    background: url('@/assets/jp_grand.png') no-repeat 0 0;
    background-size: 100% 200%;
  }

  .jpamount {
    margin: 0 10px;
    width: 15%;
    height: 35px;
    font-size: 1.5vw;
    font-weight: 600;
    color: #ffe599;
    text-align: center;
    letter-spacing: 1.8px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #000 center center;
    background-size: 100% 100%;
    @media (max-width: 800px) {
      margin: 0 5px;
      padding: 0 5px; 
      width: 30%;
      height: 30px;
      font-size: 3.5vw;
    }
  }

  .loading-wrap {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .list {
    width: 100%;
    overflow-x: auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-left: auto; 
    letter-spacing: 0;
    font-weight: bold;
    color: #eca98c;

    .top1 {
      width: 120px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
      text-align: center;
      padding: 0 15px;
      border-right: 1px solid #eca98c;
    }

    .others {
      margin: 6px;
      width: 130px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
      text-align: center;
    }

    .num {
      display: inline-block;
      width: 1.8rem;
      height: 1.8rem;
      line-height: 2rem;
      margin-right: 3px;
    }
  }

  .more-btn {
    margin: 0 10px;
    width: 70px;
    height: 30px;
    line-height: 30px;
    color: #ffedb7;
    text-align: center;
    cursor: pointer;
    background: url('@/assets/jp_more.png') no-repeat;
    background-size: 100% 200%; 
    border-radius: 30px;
    &:hover {
      background-position: 0 100%;
    }
    @media (max-width: 1000px) {
      margin: 0 5px;
      font-size: 0.9rem;
    }
  }

  :deep(.el-dialog__header) {
    font-weight: 900;
    text-align: center;
  }

  :deep(.el-dialog) {
    position: relative;
    @media (max-width: 800px) {
      top: -5%;
      height: 70vh;
      overflow: scroll;
    }
  }

  :deep(.head-color) {
    padding: 2px;
    color: #fff;
    font-size: 12px;
    text-align: center;
    background: var(--main-3);
  }

  :deep(.el-table) {
    .top-10 {
      --el-table-tr-bg-color: var(--main-7);
      color: #333;
      font-weight: 600;
      font-size: 1.2rem;
    }
    .warning-row {
      --el-table-tr-bg-color: var(--main-9);
    }
    .success-row {
      --el-table-tr-bg-color: #fff;
    }
  }
}
</style>
