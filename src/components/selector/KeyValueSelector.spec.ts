import { mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'
import KeyValueSelector from '@/components/selector/KeyValueSelector.vue'
import { nextTick } from 'vue'

vi.mock('vue-i18n', () => ({
  createI18n: vi.fn(),
  useI18n: () => ({
    t: (key: string) => key
  })
}))

describe('KeyValueSelector', () => {
  it('renders select options and emits change event', async () => {
    const mockDataList = <Record<string, string>>{
      '1': 'Option 1',
      '2': 'Option 2',
      '3': 'Option 3'
    }

    const wrapper = mount(KeyValueSelector, {
      props: {
        dataList: mockDataList,
        modelValue: '1',
      },
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    const elSelect = wrapper.findComponent({ name: 'el-select' })
    expect(elSelect.exists()).toBe(true)

    const options = wrapper.findAllComponents({ name: 'el-option' })
    expect(options.length).toBe(Object.keys(mockDataList).length)
    Object.entries(mockDataList).forEach(([, value], index) => {
      expect(options[index].text()).toBe(value)
    })

    await options[1].trigger('click')

    expect(elSelect.emitted('change')).toBeDefined()
    expect(elSelect.emitted('change')?.[0]?.[0]).toBe('2')
  })
})
