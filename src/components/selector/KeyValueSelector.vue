<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const emit = defineEmits(['change'])

const data = defineModel({
  type: String,
  required: true,
  default: ''
});

const props = defineProps({
  dataInitialLabel: {
    type: String,
    required: false,
    default: ''
  },
  dataInitialValue: {
    type: String,
    required: false,
    default: ''
  },
  dataList: {
    type: Object as PropType<Record<string, string | number>>,
    required: true
  }
})
</script>

<template>
  <el-select v-model="data" :placeholder="props.dataInitialLabel || t('S_PLEASE_SELECT')" size="large" @change="emit('change')">
    <el-option
      v-if="props.dataInitialLabel"
      :value="props.dataInitialValue"
      :label="props.dataInitialLabel"
    />
    <el-option
      v-for="value, key in props.dataList"
      :key="key"
      :label="value"
      :value="key"
    />
  </el-select>
</template>

<style lang="scss" scoped>
.el-select {
  width: 30%;
  margin: 10px;
}
</style>
