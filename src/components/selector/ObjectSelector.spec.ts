import { mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'
import ObjectSelector from '@/components/selector/ObjectSelector.vue'
import { nextTick } from 'vue'

vi.mock('vue-i18n', () => ({
  createI18n: vi.fn(),
  useI18n: () => ({
    t: (key: string) => key
  })
}))

describe('ObjectSelector', () => {
  it('renders select options and emits change event', async () => {
    const mockDataList = [{
      label: 'Option 1',
      value: '1'
    }, {
      label: 'Option 2',
      value: '2'
    }, {
      label: 'Option 3',
      value: '3'
    }]

    const wrapper = mount(ObjectSelector, {
      props: {
        dataList: mockDataList,
        modelValue: 'Option 1',
      },
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    const elSelect = wrapper.findComponent({ name: 'el-select' })
    expect(elSelect.exists()).toBe(true)

    const options = wrapper.findAllComponents({ name: 'el-option' })
    expect(options.length).toBe(mockDataList.length)
    mockDataList.forEach((item, index) => {
      expect(options[index].text()).toBe(item.label)
    })

    await options[1].trigger('click')

    expect(elSelect.emitted('change')).toBeDefined()
    expect(elSelect.emitted('change')?.[0]?.[0]).toBe('2')
  })
})
