<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const emit = defineEmits(['change'])

const data = defineModel({
  type: [String, Number] as PropType<string | number>,
  required: true,
  default: ''
});

const props = defineProps({
  dataList: {
    type: Array as PropType<{label: string, value: string | number}[]>,
    default: () => [],
    required: true
  }
})

</script>

<template>
  <el-select v-model="data" :placeholder="t('S_PLEASE_SELECT')" size="large" @change="emit('change')">
    <el-option
      v-for="item in props.dataList"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
</template>

<style lang="scss" scoped>
.el-select {
  width: 30%;
  margin: 10px;
}
</style>
