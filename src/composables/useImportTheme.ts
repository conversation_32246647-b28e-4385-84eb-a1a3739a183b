const scssFiles = import.meta.glob('@/styles/themes/**/*.scss')
const isLoadingTheme = ref(false)
const initComplete = ref(false)
let theme = 'default'
const unloadFiles: string[] = []
const fileRecord: string[] = []

const getInitComplete = computed(() => initComplete.value)

// 加載過 Theme 再加載各自樣式
watch(() => isLoadingTheme.value, async (newValue: boolean) => {
  if (newValue) {
    let promiseArray = <any[]>[]

    promiseArray = unloadFiles.reduce((acc, fileName) => {
      const filePath = `/src/styles/themes/${theme}/${fileName}.scss`
      if (scssFiles[filePath]) acc.push(scssFiles[filePath]())
      return acc
    },  <any>[])

    await Promise.all(promiseArray)

    initComplete.value = true
  }
}, { once: true })

export function useImportTheme() {
  const importTheme = async(themeName: string) => {
    const themePath = `/src/styles/themes/${themeName}/default.scss`
    if (scssFiles[themePath]) {
      theme = themeName
      await scssFiles[themePath]()
    }

    isLoadingTheme.value = true
  }

  const importStyle = async(fileName: string) => {
    const filePath = `/src/styles/themes/${theme}/${fileName}.scss`

    // 樣式是否加載過
    if (!fileRecord.includes(filePath)) {
      fileRecord.push(filePath)

      // 是否已載入Theme
      if (!isLoadingTheme.value) {
        unloadFiles.push(fileName)
      } else {
        if (scssFiles[filePath]) await scssFiles[filePath]()
      }
    }
  }

  return {
    importTheme,
    importStyle,
    getInitComplete
  }
}
