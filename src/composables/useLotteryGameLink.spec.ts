import { describe, it, expect, vi } from 'vitest'

vi.mock('vue3-cookies', () => ({
  useCookies: vi.fn().mockReturnValue({
    cookies: {
      get: (key: string) => {
        if (key === 'SESSION_ID') return 'session_id'
        if (key === 'lang') return 'en'
        return null
      }
    }
  }),
}))

vi.mock('@/stores/lobbyLink', () => ({
  useLobbyLinkStore: vi.fn(() => ({
    lobbyLink: ref({ 
      lottery: {
        domainPath: '/a.com'
      }
    })
  }))
}))

describe('useLotteryGameLink', async() => {
  const { useLotteryGameLink } = await import('./useLotteryGameLink')

  it('should return a correctly formatted game link URL', () => {
    const testUrlPath = '/test/game/path'
    const { getGameLink } = useLotteryGameLink()
    const resultUrl = getGameLink(testUrlPath)

    expect(resultUrl).toBe(`/api/entrance/gameroutepage?hall_id=0&session_id=session_id&lang=en&game_kind=12&game_domain=%2Fa.com&path=%2Ftest%2Fgame%2Fpath&is_mobile=false`)
  })
})
