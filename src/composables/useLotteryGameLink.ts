import { useLobbyLinkStore } from '@/stores/lobbyLink'
import { storeToRefs } from 'pinia'
import { useCookies } from 'vue3-cookies'
import { GameType } from '@/types/game'

const lobbyLinkStore = useLobbyLinkStore()
const { lobbyLink } = storeToRefs(lobbyLinkStore)
const { cookies } = useCookies()

export function useLotteryGameLink() {
  const getGameLink = (url: string) => {
    const query = {
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
      session_id: cookies.get('SESSION_ID'),
      lang: cookies.get('lang'),
      game_kind: GameType.Lottery,
      game_domain: lobbyLink.value.lottery.domainPath,
      path: url,
      is_mobile: false
    } as { [key: string]: any }
  
    const params = new URLSearchParams()
  
    for (const key in query) {
      params.append(key, query[key])
    }
  
    return `/api/entrance/gameroutepage?${params}`
  }

  return {
    getGameLink
  }
}
