import { Me, HistoryQuery, Remind } from '@icon-park/vue-next'

export interface MenuItem {
  name: string
  icon: Component
  label: string
  subMenu: {
    name: string
    label: string
  }[]
}

export const menuList: MenuItem[] = [
  {
    name: 'account',
    icon: Me,
    label: 'S_MY_ACCOUNT',
    subMenu: [
      {
        name: 'effectivebetting',
        label: 'S_EF_STR'
      },
      {
        name: 'betinfo',
        label: 'S_PERSONAL_BET_LIMIT'
      }
    ],
  },
  {
    name: 'record',
    icon: HistoryQuery,
    label: 'S_ACCOUNT_INQUIRY',
    subMenu: [
      {
        name: 'bet',
        label: 'S_BET_RECORD2'
      },
      {
        name: 'cash',
        label: 'S_ACCOUNT_INQUIRY'
      }
    ],
  },
  {
    name: 'news',
    icon: Remind,
    label: 'S_ANNOUNCEMENT',
    subMenu: [
      {
        name: 'gamenews',
        label: 'S_ANNOUNCEMENT'
      }
    ]
  }
]

export const getMenuList = (): MenuItem[] => {
  return menuList
}

export const getMenu = (menuName: string): MenuItem | undefined => {
  return menuList.find(item => item.name === menuName);
}

export const getMainMenuBySubMenu = (menuName: string): MenuItem | undefined => {
  return menuList.find(item => item.subMenu.find(subItem => subItem.name === menuName))
}

export default menuList
