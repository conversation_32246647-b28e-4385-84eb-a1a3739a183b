export type Theme = 'red' | 'gold' | 'orange' | 'blue' | 'purple' | 'default' | ''

export interface SiteConfig {
  theme: Theme
}

export interface SiteConfigs {
  [key: string]: SiteConfig
}

export const siteConfig: SiteConfigs = {
  default: {
    theme: 'default'
  },
  bbinbgp: {
    theme: 'default'
  },
  bbq: {
    theme: ''
  },
  dmdm: {
    theme: 'orange'
  },
  harry: {
    theme: 'blue'
  },
  wh8: {
    theme: 'purple'
  }
} as const

export const getSiteConfig = (siteName: string): SiteConfig => {
  return siteConfig[siteName] || siteConfig.default
}

export default siteConfig
