import cn from '@/language/zh-cn.json'
import { createI18n } from 'vue-i18n'
import en from '@/language/en-us.json'
import tw from '@/language/zh-tw.json'
import opcodeEn from '@/language/opcode/en-us.json'
import opcodeCn from '@/language/opcode/zh-cn.json'
import opcodeTw from '@/language/opcode/zh-tw.json'

const i18n = createI18n({
  legacy: false, // To use Composition API, you must set `false`.
  locale: 'zh-cn', // 设置默认语言
  fallbackLocale: 'zh-cn', // 设置备用语言
  messages: {
    'zh-tw': { ...tw, opcode: opcodeTw },
    'en': { ...en, opcode: opcodeEn },
    'zh-cn': { ...cn, opcode: opcodeCn }
  }
})

export default i18n