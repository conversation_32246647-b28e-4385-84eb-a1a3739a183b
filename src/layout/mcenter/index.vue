<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useConfigStore } from '@/stores/config'
import { getSiteConfig } from '@/config/siteConfig'
import { useHallInfoStore } from '@/stores/hallInfo'
import { useServerInfoStore } from '@/stores/serverInfo'
import { useAccountInfoStore } from '@/stores/account'
import { useLobbySwitchStore } from '@/stores/lobbySwitch'
import TopHeader from '@/components/mcenter/TopHeader.vue'
import MainMenu from '@/components/mcenter/MainMenu.vue'
import SubMenu from '@/components/mcenter/SubMenu.vue'
import { useI18n } from 'vue-i18n'
import { useImportTheme } from '@/composables/useImportTheme'
import { getMenu } from '@/config/mcenterMenu'
import { storeToRefs } from 'pinia'
import { useCookies } from 'vue3-cookies'

const { locale, t } = useI18n()
const { cookies } = useCookies()
const configStore = useConfigStore()
const hallInfoStore = useHallInfoStore()
const serverInfoStore = useServerInfoStore()
const accountInfoStore = useAccountInfoStore()
const lobbySwitchStore = useLobbySwitchStore()
const { isConfigError } = storeToRefs(configStore)
const { accountInfo } = storeToRefs(accountInfoStore)
const router = useRouter()
const hoverMenuItem = ref('record')
const clickedMenuItem = ref('record')
const activeSubMenuItem = ref('bet')
const { importTheme, importStyle, getInitComplete } = useImportTheme()
const errorDialogVisible = ref(false)
const errorMessage = ref('')

const setTheme = () => {
  const siteConfig = getSiteConfig(localStorage.getItem('hallinfo_website') || 'default')
  const theme = siteConfig.theme
  document.documentElement.setAttribute('tpl-theme', theme)
  importTheme(theme)
  importStyle('mcenter/index')
}

const getDefaultSubMenu = (menu: string) => {
  const mainMenu = getMenu(menu)
  return mainMenu?.subMenu[0].name || 'bet'
}

const onMenuClick = () => {
  activeSubMenuItem.value = getDefaultSubMenu(clickedMenuItem.value)
  router.push({ name: activeSubMenuItem.value })
}

const onMenuMouseleave = () => {
  hoverMenuItem.value = clickedMenuItem.value
}

const handleDialogClick = () => {
  errorDialogVisible.value = false
  init()
  window.close()
}

const init = () => {
  return new Promise<void>((resolve, reject) => {
    hallInfoStore.getHallInfo()
      .then(() => {
        locale.value = cookies.get('lang')
        
        return Promise.all([
            serverInfoStore.getServerInfo(),
            accountInfoStore.getAccountInfo(),
            lobbySwitchStore.getLobbySwitch()
          ])
          .then(() => {
            if (!accountInfo.value.isLogin) throw new Error('*********')
            router.push({ name: activeSubMenuItem.value })
          })
      })
      .catch((error: Error) => {
        let message = ''

        if (error.message.includes('*********')) {
          message = t('S_LOGOUT2')
        } else {
          message = `${t('S_SYSTEM_ERROR')} ${error.message}`
        }

        errorDialogVisible.value = true
        errorMessage.value = message
      })
      .finally(() => {
        try {
          if (isConfigError.value) errorDialogVisible.value = true
          watch(() => getInitComplete.value, () => {
            resolve()
          }, { once: true })
          setTheme()
        } catch (error) {
          reject(error)
        }
      })
  })
}

try {
  await init()
} catch (error) {
  console.error(error)
}
</script>

<template>
  <TopHeader />
  <div class="menu-container" @mouseleave="onMenuMouseleave">
    <MainMenu
      v-model:menu-item-selected="clickedMenuItem"
      v-model:menu-item-hover="hoverMenuItem"
      @click="onMenuClick"
    />
    <SubMenu
      v-model:main-menu-item-selected="clickedMenuItem"
      :main-menu-hover="hoverMenuItem"
      v-model:sub-menu-item-selected="activeSubMenuItem"
    />
  </div>
  <div class="content">
    <router-view />
  </div>
  <!-- Error Dialog -->
  <el-dialog
    v-model="errorDialogVisible"
    fullscreen
    center
    :show-close="false"
  >
    <h1>
      {{ isConfigError ? t('S_SYSTEM_ERROR')  : errorMessage }}
    </h1>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleDialogClick()">Ok</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.el-dialog {
  h1 {
    margin-top: 100px;
    font-size: 1.8rem;
    text-align: center;
  }
}
</style>