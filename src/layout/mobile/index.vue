<script setup lang="ts">
import { RouterView, useRouter, useRoute } from 'vue-router';
import { useConfigStore } from '@/stores/config';
import { useHallInfoStore } from '@/stores/hallInfo'
import { useServerInfoStore } from '@/stores/serverInfo'
import { useAccountInfoStore } from '@/stores/account';
import { useLobbySwitchStore } from '@/stores/lobbySwitch'
import { useI18n } from 'vue-i18n'
import { getSiteConfig } from '@/config/siteConfig'
import mHeader from '@/layout/mobile/mheader.vue'
import mFooter from '@/layout/mobile/mfooter.vue'
import { useImportTheme } from '@/composables/useImportTheme'
import { storeToRefs } from 'pinia'
import { useCookies } from 'vue3-cookies'

const { cookies } = useCookies()
const configStore = useConfigStore()
const hallInfoStore = useHallInfoStore()
const serverInfoStore = useServerInfoStore()
const accountInfoStore = useAccountInfoStore()
const lobbySwitchStore = useLobbySwitchStore()
const { importTheme, importStyle, getInitComplete } = useImportTheme()
const { isConfigError } = storeToRefs(configStore)
const { locale, t } = useI18n()
const errorDialogVisible = ref(false)
const router = useRouter()
const route = useRoute()
const isUpupMaintain = ref(false)

const setTheme = () => {
  const siteConfig = getSiteConfig(localStorage.getItem('hallinfo_website') || 'default')
  const theme = siteConfig.theme
  document.documentElement.setAttribute('tpl-theme', theme)
  importTheme(theme)
  importStyle('mobile/index')
}

const queryInit = () => {
  const queryMap = {
    lang: { cookie: 'lang', default: 'zh-cn' },
    pageSite: { cookie: 'page_site', default: 'game' },
    sessionId: { cookie: 'SESSION_ID', default: 'guest' }
  } as { [key: string]: any }
  const query = route.query as any

  if (route.path !== '/m/main') return
  
  sessionStorage.setItem('page_site', '')

  if (Object.keys(query).length > 0) {
    Object.entries(queryMap).forEach(([key, value]) => {
      cookies.set(value.cookie, query[key] || value.default)
    })
  }
}

const init = () => {
  return new Promise<void>((resolve, reject) => {
    hallInfoStore.getHallInfo()
      .then(() => {
        locale.value = cookies.get('lang')
        
        return Promise.all([
          serverInfoStore.getServerInfo(),
          accountInfoStore.getAccountInfo(),
          lobbySwitchStore.getLobbySwitch()
        ])
      })
      .catch((error: Error) => {
        if (error.message === 'Under maintenance') {
          isUpupMaintain.value = true
        } else {
          ElMessage.error(error.message)
        }
      })
      .finally(() => {
        try {
          watch(() => getInitComplete.value, () => {
            resolve()
          }, { once: true })
          setTheme()
        } catch (error) {
          reject(error)
        }
      })
  })
}

document.documentElement.setAttribute('style', 'background: var(--bg);')

watch(() => isConfigError.value, (value) => {
  errorDialogVisible.value = value
}, { immediate: true })

try {
  queryInit()
  await init()

  if (isUpupMaintain.value) {
    setTimeout(() => {
      router.push('/upup')
    })
  }
} catch (error) {
  console.error(error)
}
</script>

<template>
    <m-header />
        <div class="mobile-container">
            <RouterView />
        </div>
    <m-footer />
    <!-- Error Dialog -->
    <el-dialog
      v-model="errorDialogVisible"
      fullscreen
      center
      :show-close="false"
    >
      <span class="error">{{ t('S_SYSTEM_ERROR') }}</span>
    </el-dialog>
</template>

<style lang="scss" scoped>
    .mobile-container {
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
    }

    .error {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 300px;
      font-size: 20px;
    }
</style>