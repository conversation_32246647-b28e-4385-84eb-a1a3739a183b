import { mount } from '@vue/test-utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import <PERSON>Footer from './mfooter.vue'
import { setActivePinia, createPinia } from 'pinia'

vi.mock('@/stores/hallInfo', () => ({
  useHallInfoStore: vi.fn(() => ({
    hallInfo: ref({ name: 'test' })
  }))
}))

describe('MFooter', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('render', () => {
    const wrapper = mount(MFooter)
    expect(wrapper.text()).toContain('Copyright © test Reserved')
  })
})