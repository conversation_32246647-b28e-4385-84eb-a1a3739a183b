<script setup lang="ts">
import { useHallInfoStore } from '@/stores/hallInfo'
import { storeToRefs } from 'pinia';

const hallInfoStore = useHallInfoStore()
const { hallInfo } = storeToRefs(hallInfoStore)
</script>

<template>
    <div class="m-footer">
        <p>{{ `Copyright © ${hallInfo.name} Reserved` }}</p>
    </div>
</template>

<style lang="scss" scoped>
    .m-footer {
        width: 100%;
        height: 40px;
        position: fixed;
        bottom: -1px;
        left: 0;
        background: #333;
        color: #bbb;
        display: flex;
        justify-content: center;
        align-items: center;
    }
</style>