<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { useMobileBasicInfoStore } from "@/stores/mobilebasic"
import { useConfigStore } from '@/stores/config'
import bbSwitchBoard from '@/components/mobile/bbSwitchBoard.vue'
import ComponentUserInfo from '@/components/mobile/UserInfo.vue'
import { useImportTheme } from '@/composables/useImportTheme'
import { storeToRefs } from 'pinia'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const currentPageName = ref<string>('')
const basicInfoStore = useMobileBasicInfoStore()
const configStore = useConfigStore()
const { siteInfo } = storeToRefs(basicInfoStore)
const { cdnUrl } = storeToRefs(configStore)
const { importStyle } = useImportTheme()

const showTopBoard = computed(() => {
  if (route.name == 'mcasino' || route.name == 'mfishing') {
    return true
  }
  return false
})

const backToMenu = () => {
  router.push({name: 'mobile_menu'})
}

watch(route, () => {
  if(route.name == 'mobile') {
    currentPageName.value = ''
  } else {
    switch (route.name) {
      case 'mnewbbsport':
        currentPageName.value = t('subnav.sports.bbsports')
        break

      case 'mesport':
        currentPageName.value = t('subnav.sports.bbesports')
        break
    
      case 'mbbsport':
        currentPageName.value = t('subnav.sports.oldbbsports')
        break
      
      case 'mlive':
        currentPageName.value = t('subnav.live')
        break

      case 'mfishing':
        currentPageName.value = t('subnav.casino.fishing')
        break
      
      case 'mcasino':
        currentPageName.value = t('subnav.casino.bbcasino')
        break

      case 'mlottery':
        currentPageName.value = t('subnav.lottery')
        break

      case 'mcard':
        currentPageName.value = t('subnav.battle')
        break
    }
  }
})

importStyle('mobile/mHeader')
</script>

<template>
  <div class="m-header">
    <div v-if="route.name !== 'mobile_menu'" class="left">
      <img
        class="back-btn"
        :src="`${cdnUrl}/m/new/img/components/theme/theme1/btn_navbar_exit_n.png?v=${siteInfo.unitTime}`"
        @click="backToMenu()"
      />
      <span>{{ currentPageName }}</span>
    </div>
    <div class="right">
      <ComponentUserInfo />
    </div>
  </div>
  <div v-if="showTopBoard" class="boards">
    <bbSwitchBoard />
  </div>
</template>

<style lang="scss" scoped>
  .m-header {
    position: fixed;
    width: 100%;
    height: 40px;
    top: 0;
    left: 0;
    background: var(--mobile-mHeader-header-bg);
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    z-index: 2;

    .left {
      display: flex;
      align-items: center;

      .back-btn {
        width: 30px;
        height: auto;
        transform: rotate(180deg);
        margin: 0 10px;
        cursor: pointer;
      }
    }

    .right {
      display: flex;
      align-items: center;
      margin: 0 10px;
      text-align: right;
      margin-left: auto;
    }
  }

  .boards {
    width: 100%;
    margin-top: 40px;
  }
    
</style>