<script setup lang="ts">
import { useImportTheme } from '@/composables/useImportTheme'
import { useHallInfoStore } from '@/stores/hallInfo'
import { storeToRefs } from 'pinia'

const { importStyle } = useImportTheme()
const hallInfoStore = useHallInfoStore()
const { hallInfo } = storeToRefs(hallInfoStore)

importStyle('pc/footer')
</script>

<template>
  <div class="footer-container">{{ `Copyright © ${hallInfo.name} Reserved` }}</div>
</template>

<style scoped>
.footer-container {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 60px;
  background: var(--pc-footer-footer-container-bg);
  color: var(--pc-footer-footer-container-text);
  font-size: small;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 3000;
}
</style>
