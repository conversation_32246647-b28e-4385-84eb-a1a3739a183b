<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { logout } from '@/api/logout'
import { useAccountInfoStore } from '@/stores/account'
import { useLobbySwitchStore } from '@/stores/lobbySwitch'
import UserInfo from '@/components/pc/UserInfo.vue'
import MemberCenter from '@/components/buttons/MemberCenter.vue'
import { useCookies } from "vue3-cookies"
import { useImportTheme } from '@/composables/useImportTheme'
import { ElMessageBox } from 'element-plus'
import { storeToRefs } from 'pinia'
import { getLiveLobbyLink, getSportLobbyLink } from '@/api/game'

const { cookies } = useCookies()
const { t } = useI18n()
const router = useRouter()
const route = useRoute()
const accountInfoStore = useAccountInfoStore()
const lobbySwitchStore = useLobbySwitchStore()
const { accountInfo } = storeToRefs(accountInfoStore)
const { pcMenuList } = storeToRefs(lobbySwitchStore)

const { importStyle } = useImportTheme()

interface LobbyLink {
  isMobile: boolean
  enterPage: string
  fnKey: string
  windowName: string
}

const lobbyLinkFn: { [key: string]: (lobbyLink: LobbyLink) => Promise<void> } = {
  live: async (lobbyLink: LobbyLink) => {
    if (!accountInfo.value.isLogin) {
      ElMessageBox.alert(
        t('S_LOGIN_TIPS'), 
        '', 
        { center: true, showClose: false}
      )
      return
    }
    
    try {
      const params = {
        session_id: cookies.get('SESSION_ID'),
        lang: cookies.get('lang'),
        hall_id: Number(localStorage.getItem('hallinfo_hallid')),
        domain_url: location.hostname,
        is_mobile: false,
        enter_page: lobbyLink.enterPage
      }

      const res = await getLiveLobbyLink(params)

      if (res.maintain) {
        ElMessage.error({
          dangerouslyUseHTMLString: true,
          message: res.maintainInfo
        })
        return
      }

      window.open(
        res.link,
        lobbyLink.windowName,
        'width=1200,height=740'
      )?.focus()
    } catch(error) {
      ElMessage.error((error as Error).message)
      console.error(error)
    }
  },
  sport: async (lobbyLink: LobbyLink) => {
    try {
      const params = {
        session_id: cookies.get('SESSION_ID') || undefined,
        lang: cookies.get('lang'),
        hall_id: Number(localStorage.getItem('hallinfo_hallid')),
        domain_url: location.hostname,
        is_mobile: false,
        enter_page: lobbyLink.enterPage || undefined
      }
      
      const res = await getSportLobbyLink(params)

      if (res.maintain) {
        ElMessage.error({
          dangerouslyUseHTMLString: true,
          message: res.maintainInfo
        })
        return
      }
     
      window.open(
        res.link, 
        lobbyLink.windowName
      )?.focus()
    } catch(error) {
      ElMessage.error((error as Error).message)
      console.error(error)
    }
  }
}

const openLobby = async (lobbyLink: LobbyLink) => {
  lobbyLinkFn[lobbyLink.fnKey]?.(lobbyLink)
}


const clickNavLink = (menuItem: { title: string; link?: string; lobbyLink?: LobbyLink; }) => {
  if (accountInfo.value.bankrupt) {
    ElMessageBox.alert(
      t('S_POWER_STOP'),
      '', 
      { center: true, showClose: false}
    )

    return
  }   

  if (menuItem.lobbyLink) {
    openLobby(menuItem.lobbyLink)
  } else if(menuItem.link) {
    changeRoute(menuItem.link)
  }
}

const changeRoute = (link: string) => {
  let navLink = ''
    switch (link) {
      case '/game':
        navLink = '/casino'
        break;
      case '/game/5':
        navLink = '/casino'
        break;
      case '/game/fisharea':
        navLink = '/fishing'
        break;
      case '/live':
        navLink = '/live'
        break;
      case '/ltlottery':
        navLink = '/lottery'
        break;
      case '/ball':
        navLink = ''
        break;
      case '/card/66':
         navLink = '/card'
        break;
    }
  router.push(navLink)
}

const logOut = async() => {
  try {
    const params = {
      session_id: cookies.get('SESSION_ID'),
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
    }

    await logout(params)
    window.location.reload()
  } catch (err) {
    console.error(err)
    ElMessage.error((err as Error).message)
  }
}

const checkRoute = () => {
  if(document.documentElement.clientWidth < 800) {
    location.href = '/m'
    return
  }

  if (route.path === '/') {
    // 視訊：live、機率：game、彩票：Ltlottery、New BB體育：nball、BB捕魚大師：fisharea
    let page = cookies.get('page_site') || 'game'
  
    switch (page) {
      case 'game':
        router.push({name: 'casino'})
        break
      case 'Ltlottery':
        router.push({name: 'lottery'})
        break
      case 'live':
        router.push({name: 'live'})
        break
      case 'fisharea':
        router.push({name: 'fishing'})
        break
      case 'nball':
        router.push({name: ''})
        break
    }
  }
}

checkRoute()
importStyle('pc/header')
</script>

<template>
  <div class="header-top">
    <div class="left">
      <UserInfo />
    </div>
    <div class="right">
      <MemberCenter v-if="accountInfo.isLogin" window-config="top=50,left=50,width=1024,height=768,status=no,scrollbars=yes,resizable=no" />
      <span v-if="accountInfo.isLogin" class="deco-line">|</span>
      <span class="log_out" @click="logOut()">{{ t('S_CLOSE_WINDOW') }}</span>
      <div class="icon-space"></div>
    </div>
  </div>

  <div class="sub-navwrap">
    <div class="sub-navlist">
      <el-menu
        mode="horizontal"
        :ellipsis="false"
        popper-class="navlist-popper"
      >
        <el-sub-menu
          v-for="nav in pcMenuList"
          :key="nav.title"
          :index="nav.title"
          @click="clickNavLink(nav)"
        >
          <template #title>
            <span>{{ nav.title }}</span>
          </template>
          <el-menu-item
            v-for="sub in nav.sub"
            :key="sub.title"
            :index="sub.title"
            @click="clickNavLink(sub)"
          >
            <span>
              {{ sub.title}}
            </span>
          </el-menu-item>
        </el-sub-menu>
      </el-menu>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.header-top {
  position: relative;
  width: 100%;
  box-shadow: 1px 1px 5px 1px #0000001f;
  background: var(--bg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 10%;
  font-size: 1.15rem;
  border-bottom: 2px solid var(--main);
  z-index: 3;

  .left,
  .right {
    width: 100%;
    display: flex;
    align-items: center;
  }

  .right {
    justify-content: flex-end;
  }

  .to_mcenter,
  .log_out {
    display: inline-block;
    color: var(--main);
    letter-spacing: 2px;
    margin: 0 10px;
    cursor: pointer;
    @media (max-width: 1000px) {
      margin: 0;
      text-align: center;
    }
  }

  .deco-line {
    color: var(--main);
  }

  .el-select {
    width: 160px;
    margin: 0 10px 0 0;
    @media (max-width: 1000px) {
      margin: 0 10px 0 0;
    }
  }
  .icon-space {
    display: none;
    margin: 0 10px 0 0;
    @media (max-width: 800px) {
      display: block;
      width: 20px;
    }
  }

  @media (max-width: 1000px) {
    background: #eee;
    padding: 10px 5px;
    font-size: 1rem;
  }
}

.sub-navwrap {
  position: relative;
  z-index: 1;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center; 
  background: var(--pc-header-sub-navwrap-bg);
  
  :deep(.el-menu) {
    height: 20px;
    background: unset;
    border: unset;

    .is-opened {
      .el-sub-menu__title {
        span {
          line-height: 20px;
          color: var(--text-active);
          border-radius: 10px;
          background-color: var(--pc-header-sub-navwrap-li-bg);
        }
      }
    }

    .el-sub-menu__title {
      font-size: 20px;
      color: var(--text-nonactive);
      border: unset;

      i {
        display: none;
      }

      &:hover {
        background-color: unset;
      }
    }
  }
}

.demonstration {
  color: var(--el-text-color-secondary);
}
</style>
<style lang="scss">
  .navlist-popper.el-popper {
    border: unset;
    background-color: var(--pc-header-sub-list-bg);
    margin-top: 10px;
    border-radius: 10px;

    .el-menu.el-menu--popup {
      background-color: var(--pc-header-sub-list-bg);
      border-radius: 10px;

      .el-menu-item {
        color: #fff;
        font-size: 17px;
        background-color: var(--pc-header-sub-list-bg);
        border-radius: 10px;
        
        .icon {
          display: inline-block;
          width: 22px;
          height: 22px;
          margin-right: 5px;

          &.fishing-icon {
            background-position: -88px 0;
          }

          &.bb-icon {
            background-position: -110px 0;
          }
        }

        &:hover {
          color: var(--pc-header-sub-list-text-active);
        }
      }
    }

    &::before {
      content: "";
      position: absolute;
      top: -8px;
      left: 22px;
      display: block;
      width: 0;
      height: 0;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-bottom: 8px solid var(--pc-header-sub-list-bg);
    }
  }
</style>
