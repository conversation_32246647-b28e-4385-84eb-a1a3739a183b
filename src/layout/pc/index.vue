<script setup lang="ts">
import { useRoute } from 'vue-router'
import { useBasicInfoStore } from '@/stores/basicInfo'
import { useConfigStore } from '@/stores/config'
import { useHallInfoStore } from '@/stores/hallInfo'
import { useServerInfoStore } from '@/stores/serverInfo'
import { useAccountInfoStore } from '@/stores/account'
import { useLobbySwitchStore } from '@/stores/lobbySwitch'
import { useI18n } from 'vue-i18n'
import { getSiteConfig } from '@/config/siteConfig'
import pcHeader from '@/layout/pc/header.vue'
import pcFooter from '@/layout/pc/footer.vue'
import BackToTop from '@/components/BackToTop.vue'
import { useImportTheme } from '@/composables/useImportTheme'
import { storeToRefs } from 'pinia'
import { useCookies } from 'vue3-cookies'

const route = useRoute()
const { cookies } = useCookies()
const basicInfoStore = useBasicInfoStore()
const configStore = useConfigStore()
const hallInfoStore = useHallInfoStore()
const serverInfoStore = useServerInfoStore()
const accountInfoStore = useAccountInfoStore()
const lobbySwitchStore = useLobbySwitchStore()
const { siteInfo } = storeToRefs(basicInfoStore)
const { isConfigError } = storeToRefs(configStore)
const { locale, t } = useI18n()
const bgImagePath: { [key: string]: string } = {
  casino: '/client/site/default/image/game_bg.jpg?v=',
  card: '/client/site/default/image/card_bg.jpg?v=',
  live: '/client/site/default/image/live_bg.jpg?v=',
  lottery: '/client/site/default/image/lottery_bg.jpg?v=',
  fishing: '/client/site/default/image/game_bg.jpg?v=',
}
const { importTheme, importStyle, getInitComplete } = useImportTheme()
const errorDialogVisible = ref(false)

const bgUrl = computed(() => {
  const routeName = route.name as string
  return bgImagePath[routeName] ? `${bgImagePath[routeName]}${siteInfo.value.unixTime}` : ''
})

const setTheme = () => {
  const siteConfig = getSiteConfig(localStorage.getItem('hallinfo_website') || 'default')
  const theme = siteConfig.theme
  document.documentElement.setAttribute('tpl-theme', theme)
  importTheme(theme)
  importStyle('pc/index')
}

const queryInit = () => {
  const queryMap = {
    lang: { cookie: 'lang', default: 'zh-cn' },
    pageSite: { cookie: 'page_site', default: 'game' },
    sessionId: { cookie: 'SESSION_ID', default: 'guest' }
  } as { [key: string]: any }
  const query = route.query as any

  if (route.path !== '/m/main') return
  
  sessionStorage.setItem('page_site', '')

  if (Object.keys(query).length > 0) {
    Object.entries(queryMap).forEach(([key, value]) => {
      cookies.set(value.cookie, query[key] || value.default)
    })
  }
}

const init = () => {
  return new Promise<void>((resolve, reject) => {
    hallInfoStore.getHallInfo()
      .then(() => {
        locale.value = cookies.get('lang')

        return Promise.all([
            serverInfoStore.getServerInfo(),
            accountInfoStore.getAccountInfo(),
            lobbySwitchStore.getLobbySwitch()
          ])
      })
      .catch((error: Error) => {
        ElMessage.error(error.message)
      })
      .finally(() => {
        try {
          watch(() => getInitComplete.value, () => {
            resolve()
          }, { once: true })
          setTheme()
        } catch(error) {
          reject(error)
        }
      })
  })
}

document.documentElement.setAttribute('style', 'background: var(--bg);')

watch(() => isConfigError.value, (value) => {
  errorDialogVisible.value = value
}, { immediate: true })

try {
  queryInit()
  await init()
} catch (error) {
  console.error(error)
}
</script>

<template>
  <el-container :style="`background: url(${bgUrl}) 50% 0 no-repeat;`">
    <el-scrollbar>
      <pc-header />
      <el-main>
        <router-view />
        <BackToTop />
      </el-main>
    </el-scrollbar>
    <el-footer>
      <pc-footer />
    </el-footer>
  </el-container>
  <!-- Error Dialog -->
  <el-dialog
    v-model="errorDialogVisible"
    fullscreen
    center
    :show-close="false"
  >
    <span class="error">{{ t('S_SYSTEM_ERROR') }}</span>
  </el-dialog>
</template>

<style lang="scss" scoped>
.el-container {
  display: flex;
  flex-direction: column;
}

.el-main {
  margin-top: 100px;
  margin-bottom: 100px;
  padding: 3% 10%;
}

.error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  font-size: 20px;
}
</style>
