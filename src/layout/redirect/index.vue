<script setup lang="ts">
import { Info } from '@icon-park/vue-next';

const platform = ref('Mobile')
const url = ref(`${location.origin}/m`)
const sessionId = ref('bg46c3b88eaea616048dc0719f517aedbfe268e8ca')
const lang = ref('zh-cn')
const pageSite = ref('game')


const onClick = () => {
  const query = {
    sessionId: sessionId.value,
    lang: lang.value,
    pageSite: pageSite.value
  } as { [key: string]: string }
  const params = new URLSearchParams()

  for (const key of Object.keys(query)) {
    params.set(key, query[key])
  }

  location.href = `${url.value}?${params}`
}

const onChange = () => {
  if (platform.value === 'PC') {
    url.value = location.origin
  } else {
    url.value = `${location.origin}/m`
  }
}
</script>

<template>
  <div class="form">
    <div class="container">
      <div class="item">
        <span class="label">
          <p>選擇裝置:</p>
        </span>
        <el-select v-model="platform" @change="onChange">
          <el-option
            v-for="item in ['PC', 'Mobile']"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </div>
      <div class="item">
        <span class="label">
          <el-tooltip
            class="box-item"
            effect="dark"
            content="網站電腦或手機首頁網址"
            placement="top"
          >
            <Info theme="filled" size="18" fill="#5ac2e0" />
          </el-tooltip>
          <p>跳轉連結:</p>
        </span>
        <el-input v-model="url"></el-input>
      </div>
      <div class="item">
        <span class="label">
          <el-tooltip
            class="box-item"
            effect="dark"
            content="APIDOC 服務所產生的會員 Session ID"
            placement="top"
          >
            <Info theme="filled" size="18" fill="#5ac2e0" />
          </el-tooltip>
          <p> Session ID: </p>
        </span>
        <el-input v-model="sessionId"></el-input>
      </div>
      <div class="item">
        <span class="label">
          <el-tooltip
            class="box-item"
            effect="dark"
            content="語系: zh-cn(簡中) zh-tw(繁中) en-us(英文) "
            placement="top"
          >
            <Info theme="filled" size="18" fill="#5ac2e0" />
          </el-tooltip>
          <p>語系:</p>
        </span>
        <el-input v-model="lang"></el-input>
      </div>
      <div class="item">
        <span class="label">
          <el-tooltip
            class="box-item"
            effect="dark"
            placement="top"
          >
            <template #content>
              <div>跳轉子頁面:</div>
              <div>Mobile: game(機率) fisharea(捕魚) bbcard(棋牌)</div>
              <div>PC: game(機率) fisharea(捕魚) Ltlottery(彩票) live(視訊)</div>
            </template>
            <Info theme="filled" size="18" fill="#5ac2e0" />
          </el-tooltip>
          <p>Page Site:</p>
        </span>
        <el-input v-model="pageSite"></el-input>
      </div>
      <el-button color="#59bafc" @click="onClick">送出</el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@media (min-width: 800px) {
  .form {
    .container {
      width: 500px;
    }
  }
}

.form {
  height: 500px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.container {
  width: 100%;
  padding: 0 10px;
  display: flex;
  flex-direction: column;
  gap: 15px;

  .item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;

    .label {
      width: 150px;
      display: flex;
      align-items: center;
      gap: 5px;

      p {
        flex: 1;
        text-wrap: nowrap;
      }
    }
  }

  button {
    width: 150px;
    height: 30px;
    --el-button-text-color: white !important;
  }
}
</style>