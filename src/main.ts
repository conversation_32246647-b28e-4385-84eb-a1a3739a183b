import '@/styles/main.scss'
import '@icon-park/vue-next/styles/index.css'
import 'element-plus/dist/index.css';

import App from './app.vue'
import DayJS from 'dayjs'
import DayJSTimezone from 'dayjs/plugin/timezone'
import DayJSUtc from 'dayjs/plugin/utc'
import Duration from 'dayjs/plugin/duration'
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import i18n from '@/language/i18n'
import router from './router'
import { useConfigStore } from './stores/config'
import { useCookies } from 'vue3-cookies'

const { cookies } = useCookies()
const env = import.meta.env as {[key: string]: any}
const app = createApp(App)

DayJS.extend(DayJSUtc)
DayJS.extend(Duration)
DayJS.extend(DayJSTimezone)

app.use(i18n)
app.use(createPinia())
app.use(router)

const initDev = () => {
  const envParamsMap = {
    VITE_PROXY_HOST: { cookie: 'host', default: '' },
    VITE_PROXY_SESSION_ID: { cookie: 'SESSION_ID', default: '' },
    VITE_PROXY_LOGINCHK: { cookie: 'login_check', default: 'N' },
    VITE_PROXY_TPL: { cookie: 'tpl', default: 'bbinbgp' },
    VITE_PROXY_LANG: { cookie: 'lang', default: 'zh-cn' },
    VITE_PROXY_LANGX: { cookie: 'langx', default: 'big5' },
  }

  Object.entries(envParamsMap).forEach(([key, value]: [string, {[key: string]: string}]) => {
    cookies.set(value.cookie, env[key] || value.default)
  })
}

const init = async () => {
  if (env.DEV && env.MODE === 'development') initDev()
  if (!cookies.get('lang')) cookies.set('lang', 'zh-cn')
  const configStore = useConfigStore()
  await configStore.fetchConfig()
  app.mount('#app')
}

init()
