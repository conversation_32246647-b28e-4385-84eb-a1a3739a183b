import { describe, it, expect } from 'vitest'
import router from './index'

describe('Router', () => {
  it('route_m', () => {
    const route = router.getRoutes().find(r => r.path === '/m')
    expect(route).toBeDefined()
    expect(route!.name).toBe('mobile')
  })

  it('route_mcenter', () => {
    const route = router.getRoutes().find(r => r.path === '/mcenter')
    expect(route).toBeDefined()
    expect(route!.name).toBe('mcenter')
  })

  it('route_upup', () => {
    const route = router.getRoutes().find(r => r.path === '/upup')
    expect(route).toBeDefined()
    expect(route!.name).toBe('upup')
  })

  it('route_index', () => {
    const route = router.getRoutes().find(r => r.path === '/')
    expect(route).toBeDefined()
    expect(route!.name).toBe('index')
  })

  it('route_error404', () => {
    const route = router.getRoutes().find(r => r.path === '/:catchAll(.*)')
    expect(route).toBeDefined()
    expect(route!.name).toBe('error404')
  })
})