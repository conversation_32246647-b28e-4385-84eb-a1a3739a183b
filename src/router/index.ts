import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(''),
  routes: [
    {
      path: '/m',
      name: 'mobile',
      redirect: '/m/main',
      component: () => import('@/layout/mobile/index.vue'),
      children: [
        {
          path: 'main',
          name: 'mobile_menu',
          component:  () => import('@/views/mobile/index.vue')
        },
        {
          path: 'casino',
          name: 'mcasino',
          component: () => import('@/views/mobile/casino.vue')
        },
        {
          path: 'fishing',
          name: 'mfishing',
          component: () => import('@/views/mobile/fishing.vue')
        },
        {
          path: 'card',
          name: 'mcard',
          component: () => import('@/views/mobile/card.vue')
        },
        {
          path: 'live',
          name: 'mlive',
          component: () => import('@/views/mobile/live.vue')
        },
        {
          path: 'lottery',
          name: 'mlottery',
          component: () => import('@/views/mobile/lottery.vue')
        },
      ]
    },
    {
      path: '/redirect',
      name: 'redirect',
      component: () => import('@/layout/redirect/index.vue'),
    },
    {
      path: '/mcenter',
      name: 'mcenter',
      component: () => import('@/layout/mcenter/index.vue'),
      meta: { title: 'Member Center' },
      children: [
        {
          path: '/effectivebetting',
          name: 'effectivebetting',
          component: () => import('@/views/mcenter/effectivebetting.vue')
        },
        {
          path: '/betinfo',
          name: 'betinfo',
          component: () => import('@/views/mcenter/betinfo.vue')
        },
        {
          path: '/bet',
          name: 'bet',
          component: () => import('@/views/mcenter/bet.vue')
        },
        {
          path: '/cash',
          name: 'cash',
          component: () => import('@/views/mcenter/cash.vue')
        },
        {
          path: '/gamenews',
          name: 'gamenews',
          component: () => import('@/views/mcenter/gamenews.vue')
        }
      ]
    },
    {
      path: '/upup',
      name: 'upup',
      component: () => import('@/views/upup.vue')
    },
    {
      path: '/:catchAll(.*)',
      name: 'error404',
      component: () => import('@/views/error404.vue')
    },
    {
      path: '/',
      name: 'index',
      component: () => import('@/layout/pc/index.vue'),
      children: [
        {
          path: '/live',
          name: 'live',
          component: () => import('@/views/pc/live.vue')
        },
        {
          path: '/casino',
          name: 'casino',
          component: () => import('@/views/pc/casino.vue')
        },
        {
          path: '/fishing',
          name: 'fishing',
          component: () => import('@/views/pc/fishing.vue')
        },
        {
          path: '/lottery',
          name: 'lottery',
          component: () => import('@/views/pc/lottery.vue')
        },
        {
          path: '/card',
          name: 'card',
          component: () => import('@/views/pc/card.vue')
        }
      ]
    }
  ]
})

export default router
