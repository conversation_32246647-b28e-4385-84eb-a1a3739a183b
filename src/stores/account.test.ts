import { beforeEach, describe, expect, it, vi } from 'vitest'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { getAccountInfo as getAccountInfoData } from '@/api/account'
import { nextTick } from 'vue'
import { useAccountInfoStore } from './account'

vi.mock('@/api/account', () => ({
  getAccountInfo: vi.fn()
}))

vi.mock('vue3-cookies', () => ({
  useCookies: vi.fn().mockReturnValue({
    cookies: {
      get: (key: string) => {
        if (key === 'session_id') return 'session_id'
        return null
      }
    }
  }),
}))

describe('useAccountInfoStore', () => {
  const mockResponse = {
    alias: 'winnie',
    balance: 1002613.5,
    bankrupt: false,
    block: false,
    currency: 'CNY',
    enable: true,
    test: false,
    username: 'winnie',
    isLogin: true
  }

  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('getAccountInfo', async () => {
    const store = useAccountInfoStore()

    vi.mocked(getAccountInfoData).mockResolvedValue(mockResponse)

    await store.getAccountInfo()
    await nextTick();

    expect(store.accountInfo).toEqual({
      alias: 'winnie',
      balance: 1002613.5,
      bankrupt: false,
      block: false,
      currency: 'CNY',
      enable: true,
      test: false,
      username: 'winnie',
      isLogin: true
    })
  })
})
