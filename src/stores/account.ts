import { getAccountInfo as getAccountInfoData } from '@/api/account'
import { defineStore } from 'pinia'
import { IAccountInfo } from '@/types/account'
import { useCookies } from "vue3-cookies"

export const useAccountInfoStore = defineStore('account_info', () => {
  const accountInfo = ref<IAccountInfo>({} as any)
  const { cookies } = useCookies()

  const getAccountInfo = async () => {
    try {
      const res = await getAccountInfoData({ 
        hall_id: Number(localStorage.getItem('hallinfo_hallid')),
        session_id: cookies.get('SESSION_ID')
      })

      accountInfo.value = {
        ...res,
        balance: res.balance || 0,
        bankrupt: res.bankrupt || false,
      }
  
      localStorage.setItem('bascinfo_username', accountInfo.value.username)
    } catch(error) {
      accountInfo.value = {
        username: 'ballguest',
        isLogin: false,
        balance: 0,
        bankrupt: false
      } as IAccountInfo
      localStorage.setItem('bascinfo_username', '')

      console.error(error)
    }
  }

  return { getAccountInfo, accountInfo }
})