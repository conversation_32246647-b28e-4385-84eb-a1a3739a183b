import { beforeEach, describe, expect, it, vi } from 'vitest'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { useBasicInfoStore } from './basicInfo'

vi.mock('@/utils/date', () => ({
  getTodayDate: vi.fn(() => '2025-03-17')
}));

vi.mock('axios', () => {
  return {
    default: {
      get: vi.fn(),
    }
  }
})

describe('useBasicInfoStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('siteInfo', async () => {
    const store = useBasicInfoStore()

    expect(store.siteInfo).toEqual({
      isMobile: false,
      unixTime: new Date('2025-03-17').getTime()
    })
  })
})
