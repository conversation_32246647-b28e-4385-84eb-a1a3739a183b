import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { create<PERSON><PERSON>, setActiveP<PERSON> } from 'pinia'
import { useCardGameStore } from '@/stores/cardGame'
import { getCardGameMenu, getCardGameList, getCardLobbyLink } from '@/api/game'
import type { IGetGameList, IGameMenu, ILobbyLink } from '@/types/game'

const mockCardGameMenu = [
  { id: 84, gameCount: 2, sort: 1, name: 'All', games: [1, 2] },
  { id: 96, gameCount: 1, sort: 3, name: 'Card', games: [1] }
] as IGameMenu[]

const mockCardGameList = {
  gameList: [
    { gameId: 1, gameKind: 66, name: 'Game 1', icon: '', link: 'link1', externalId: '' },
    { gameId: 2, gameKind: 66, name: 'Game 2', icon: '', link: 'link2', externalId: '' },
  ],
  lang: 'en',
  lobbyEntry: 'http://example.com/lobby',
} as IGetGameList

const mockLobbyLink = {
  link: 'test.com',
  maintain: false,
  maintainInfo: ''
} as ILobbyLink

vi.mock('vue3-cookies', () => ({
  useCookies: vi.fn().mockReturnValue({
    cookies: {
      get: (key: string) => {
        if (key === 'SESSION_ID') return 'session_id'
        if (key === 'lang') return 'en'
        return null
      }
    }
  }),
}))

vi.mock('@/api/game', () => ({
  getCardGameMenu: vi.fn(),
  getCardGameList: vi.fn(),
  getCardLobbyLink: vi.fn()
}))

describe('useCardGameStore', () => {
  let store: ReturnType<typeof useCardGameStore>

  beforeEach(() => {
    setActivePinia(createPinia())
    store = useCardGameStore()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('fetchGameMenu should populate game menu', async () => {
    vi.mocked(getCardGameMenu).mockResolvedValue(mockCardGameMenu)
    await store.fetchGameMenu()

    expect(store.gameMenu).toHaveLength(2)
    expect(store.gameMenu[0]).toEqual({
      id: 84,
      gameCount: 2,
      sort: 1,
      name: 'All',
      games: [1, 2],
    })
  })

  it('fetchGameMenu should reject when request fails', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(getCardGameMenu).mockRejectedValue(mockError)

    await expect(store.fetchGameMenu()).rejects.toThrow(mockError)
  })

  it('fetchGameList should populate game list', async () => {
    vi.mocked(getCardGameList).mockResolvedValue(mockCardGameList)
    await store.fetchGameList(false)

    expect(store.gameList).toHaveLength(2)
    expect(store.gameList[0]).toEqual({
      gameId: 1,
      gameKind: 66,
      name: 'Game 1',
      icon: '',
      link: 'link1',
      externalId: '',
    })
  })

  it('fetchGameList should populate game names', async () => {
    vi.mocked(getCardGameList).mockResolvedValue(mockCardGameList)
    await store.fetchGameList(false)

    expect(store.gameList).toHaveLength(2)
    expect(store.gameNames).toEqual({
      1: 'Game 1',
      2: 'Game 2'
    })
  })

  it('fetchGameList with lobbyUrl empty', async () => {
    vi.mocked(getCardGameList).mockResolvedValue({
      ...mockCardGameList,
      lobbyEntry: '',
    })
    await store.fetchGameList(true)

    expect(store.gameList).toHaveLength(2)
    expect(store.gameList[0]).toEqual({
      gameId: 1,
      gameKind: 66,
      name: 'Game 1',
      icon: '',
      link: 'link1',
      externalId: '',
    })

    expect(store.lobbyUrl).toBe('')
  })

  it('fetchGameList should reject when request fails', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(getCardGameList).mockRejectedValue(mockError)

    await expect(store.fetchGameList(false)).rejects.toThrow(mockError)
  })

  it('fetchLobbyLink should populate lobby link', async () => {
    vi.mocked(getCardLobbyLink).mockResolvedValue(mockLobbyLink)
    await store.fetchLobbyLink()

    expect(store.lobbyUrl).toBe('test.com')
  })

  it('fetchLobbyLink should reject when response is maintain', async () => {
    vi.mocked(getCardLobbyLink).mockResolvedValue({
      maintain: true,
      maintainInfo: 'test'
    } as any)

    await expect(store.fetchLobbyLink()).rejects.toThrow('test')
  })

  it('fetchLobbyLink should reject when request fails', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(getCardLobbyLink).mockRejectedValue(mockError)

    await expect(store.fetchLobbyLink()).rejects.toThrow(mockError)
  })

  it('fetchCardData should populate gameList', async () => {
    vi.mocked(getCardGameMenu).mockResolvedValue(mockCardGameMenu)
    vi.mocked(getCardGameList).mockResolvedValue(mockCardGameList)
    await store.fetchCardData(false)

    expect(store.gameList).toHaveLength(2)
    expect(store.gameList[0]).toEqual({
      gameId: 1,
      name: 'Game 1',
      icon: '',
      link: 'link1',
      gameKind: 66,
      externalId: '',
    })
  })

  it('fetchCardData with mobile', async () => {
    vi.mocked(getCardGameMenu).mockResolvedValue(mockCardGameMenu)
    vi.mocked(getCardGameList).mockResolvedValue(mockCardGameList)
    await store.fetchCardData(true)

    expect(getCardGameMenu).toHaveBeenCalledWith({
      session_id: 'session_id',
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
      lang: 'en',
    })
  })

  it('fetchCardData should reject when request fails', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(getCardGameMenu).mockRejectedValue(mockError)
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    await expect(store.fetchCardData(false)).rejects.toThrow(mockError)
    expect(console.error).toHaveBeenCalledWith(mockError)
  })

  it('getAllGameMenu should return filtered menu', async () => {
    vi.mocked(getCardGameMenu).mockResolvedValue(mockCardGameMenu)
    await store.fetchCardData(false)
    const result = store.getAllGameMenu()

    expect(result).toHaveLength(2)
    expect(result[0].id).toBe(84)
    expect(result[1].id).toBe(96)
  })

  it('getGameMenu should return specific menu', async () => {
    vi.mocked(getCardGameMenu).mockResolvedValue(mockCardGameMenu)
    await store.fetchCardData(false)
    const result = store.getGameMenu(96)

    expect(result).toEqual({ id: 96, gameCount: 1, sort: 3, name: 'Card', games: [1] })
  })

  it('getGameListByMenuId should return game list', async () => {
    vi.mocked(getCardGameMenu).mockResolvedValue(mockCardGameMenu)
    vi.mocked(getCardGameList).mockResolvedValue(mockCardGameList)
    await store.fetchCardData(false)
    const result = store.getGameListByMenuId(96)

    expect(result).toHaveLength(1)
    expect(result).toEqual([{ gameId: 1, name: 'Game 1', icon: '', link: 'link1', gameKind: 66, externalId: '' }])
  })

  it('getGameListByMenuId with invalid menu id', async () => {
    vi.mocked(getCardGameMenu).mockResolvedValue(mockCardGameMenu)
    vi.mocked(getCardGameList).mockResolvedValue(mockCardGameList)
    await store.fetchCardData(false)
    const result = store.getGameListByMenuId(999)

    expect(result).toHaveLength(0)
    expect(result).toEqual([])
  })

  it('searchGame should return filtered game list', async () => {
    vi.mocked(getCardGameMenu).mockResolvedValue(mockCardGameMenu)
    vi.mocked(getCardGameList).mockResolvedValue(mockCardGameList)
    await store.fetchCardData(false)
    const result = store.searchGame('game 2')

    expect(result).toHaveLength(1)
    expect(result[0].name).toBe('Game 2')
  })
})
