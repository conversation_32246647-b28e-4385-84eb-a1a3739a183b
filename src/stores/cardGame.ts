import { defineStore } from 'pinia'
import { useCookies } from "vue3-cookies"
import type { IGameInfo, IGameMenu } from '@/types/game'
import { getCardGameMenu, getCardGameList, getCardLobbyLink } from '@/api/game'

export const useCardGameStore = defineStore('card_game', () => {
  const gameList = ref<IGameInfo[]>([])
  const gameMenu = ref<IGameMenu[]>([])
  const lobbyUrl = ref<string>('')
  const { cookies } = useCookies()
  const gameNames = computed(() => gameList.value.reduce((acc, item) => {
    acc[item.gameId] = item.name
    return acc
  }, {} as { [key: number]: string }))

  const fetchGameMenu = async () => {
    try {
      const params = {
        session_id: cookies.get('SESSION_ID'),
        hall_id: Number(localStorage.getItem('hallinfo_hallid')),
        lang: cookies.get('lang'),
      }

      gameMenu.value = await getCardGameMenu(params)
    } catch (err) {
      return Promise.reject(err)
    }
  }

  const fetchGameList = async (isMobile: boolean) => {
    try {
      const params = {
        session_id: cookies.get('SESSION_ID'),
        hall_id: Number(localStorage.getItem('hallinfo_hallid')),
        is_mobile: isMobile,
        lang: cookies.get('lang'),
      }
      const res = await getCardGameList(params)

      gameList.value = res.gameList
    } catch (err) {
      return Promise.reject(err)
    }
  }

  const fetchLobbyLink = async () => {
    try {
      const params = {
        session_id: cookies.get('SESSION_ID'),
        hall_id: Number(localStorage.getItem('hallinfo_hallid')),
        lang: cookies.get('lang'),
      }

      const res = await getCardLobbyLink(params)

      if (res.maintain) throw new Error(res.maintainInfo)

      lobbyUrl.value = res.link
    } catch (err) {
      return Promise.reject(err)
    }
  }

  const fetchCardData = async (isMobile: boolean) => {
    try {
      await Promise.all([
        fetchGameMenu(),
        fetchGameList(isMobile)
      ])
    } catch (err) {
      console.error(err)
      return Promise.reject(err)
    }
  }

  const getAllGameMenu = () => {
    return gameMenu.value.filter((menu: IGameMenu) => menu.id !== 35)
  }

  const getGameMenu = (menuId: number) => {
    return gameMenu.value.find((menu: IGameMenu) => menu.id == menuId)
  }

  const getGameListByMenuId = (menuId: number) => {
    const menu = getGameMenu(menuId)
    if (menu) {
      return menu.games.map((gameId: number) => gameList.value.find((game: IGameInfo) => game.gameId == gameId))
        .filter(game => game !== undefined)
    }
    return []
  }

  const searchGame = (search: string) => {
    return gameList.value.filter((game: IGameInfo) => {
      return game.name.toLowerCase().includes(search.toLowerCase())
    })
  }

  return { gameList, gameMenu, gameNames, fetchCardData, fetchGameList, fetchGameMenu, fetchLobbyLink, getAllGameMenu, getGameMenu, getGameListByMenuId, searchGame, lobbyUrl }
})
