import { afterEach,beforeEach, describe, expect, it, vi } from 'vitest'
import { create<PERSON><PERSON>, setActivePinia } from 'pinia'
import { useCasinoGameStore } from '@/stores/casinoGame'
import { getCasinoGameList, getCasinoGameMenu } from '@/api/game'
import { getFavoriteGameList, addFavoriteGameList, removeFavoriteGameList } from '@/api/favorite'
import type { IGameMenu, IGetGameList } from '@/types/game'

vi.mock('@/stores/ejpremote', () => ({
  useEjpStore: vi.fn(() => ({
    setJackpotSwitch: vi.fn()
  }))
}))

const mockCasinoGameList = {
  gameList: [
    { gameId: 1, gameKind: 5, name: 'Game 1', icon: '', link: 'link1' },
    { gameId: 2, gameKind: 5, name: 'Game 2', icon: '', link: 'link2' },
    { gameId: 3, gameKind: 5, name: 'Game 3', icon: '', link: 'link3' }
  ],
  jackpotSwitch: false,
} as IGetGameList

const mockLowerGameMenu = [{
  id: 37, gameCount: 1, sort: 2, name: 'Other', games: [3], lower: []
}]

const mockCasinoGameMenu = [
  { id: 2, gameCount: 1, sort: 2, name: 'Hot', games: [1] },
  { id: 1, gameCount: 1, sort: 3, name: 'New', games: [1] },
  { id: 84, gameCount: 2, sort: 1, name: 'All', games: [1,2] },
  { id: 96, gameCount: 1, sort: 5, name: 'Card', games: [1] },
  { id: 3, gameCount: 1, sort: 6, name: 'Casino', games: [1], lower: mockLowerGameMenu }
] as IGameMenu[]

const mockFavoriteGameList = [1, 2]

vi.mock('vue3-cookies', () => ({
  useCookies: vi.fn().mockReturnValue({
    cookies: {
      get: (key: string) => {
        if (key === 'SESSION_ID') return 'session_id'
        if (key === 'lang') return 'en'
        return null
      }
    }
  }),
}))

vi.stubGlobal('localStorage', {
  getItem: vi.fn((key) => {
    if (key === 'hallinfo_hallid') return '6'
    return null
  })
})

vi.mock('@/api/game', () => ({
  getCasinoGameMenu: vi.fn(),
  getCasinoGameList: vi.fn(),
}))

vi.mock('@/api/favorite', () => ({
  getFavoriteGameList: vi.fn(),
  addFavoriteGameList: vi.fn(),
  removeFavoriteGameList: vi.fn(),
}))

describe('useCasinoGameStore', () => {
  let store: ReturnType<typeof useCasinoGameStore>

  beforeEach(() => {
    setActivePinia(createPinia())
    store = useCasinoGameStore()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('fetchGameList should populate gameList', async () => {
    vi.mocked(getCasinoGameList).mockResolvedValue(mockCasinoGameList)

    vi.spyOn(store, 'fetchGameList')
    await store.fetchGameList(false)

    expect(store.gameList).toEqual(mockCasinoGameList.gameList)
    expect(getCasinoGameList).toHaveBeenCalledWith({
      session_id: 'session_id',
      hall_id: 6,
      lang: 'en',
      is_mobile: false,
    })
  })

  it('fetchGameList should reject when request fails', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(getCasinoGameList).mockRejectedValue(mockError)
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    await expect(store.fetchGameList(false)).rejects.toThrow(mockError)
    expect(console.error).toHaveBeenCalledWith(mockError)
  })

  it('fetchGameMenu should populate gameMenu', async () => {
    vi.mocked(getCasinoGameMenu).mockResolvedValue(mockCasinoGameMenu)

    vi.spyOn(store, 'fetchGameMenu')
    await store.fetchGameMenu(false)

    expect(store.gameMenu).toEqual(mockCasinoGameMenu)
    expect(getCasinoGameMenu).toHaveBeenCalledWith({
      session_id: 'session_id',
      hall_id: 6,
      lang: 'en',
      is_mobile: false,
    })
  })

  it('fetchGameMenu should reject when request fails', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(getCasinoGameMenu).mockRejectedValue(mockError)
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    await expect(store.fetchGameMenu(false)).rejects.toThrow(mockError)
    expect(console.error).toHaveBeenCalledWith(mockError)
  })

  it('fetchFavoriteGame should populate favoriteGameList', async () => {
    store.gameList = mockCasinoGameList.gameList
    vi.mocked(getFavoriteGameList).mockResolvedValue(mockFavoriteGameList)
    await store.fetchFavoriteGame()

    expect(store.favoriteGameList).toEqual([mockCasinoGameList.gameList[0], mockCasinoGameList.gameList[1]])
  })

  it('fetchFavoriteGame should reject when request fails', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(getFavoriteGameList).mockRejectedValue(mockError)
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    await expect(store.fetchFavoriteGame()).rejects.toThrow(mockError)
    expect(console.error).toHaveBeenCalledWith(mockError)
  })

  it('fetchCasinoData should populate gameList', async () => {
    vi.mocked(getCasinoGameList).mockResolvedValue(mockCasinoGameList)
    vi.mocked(getCasinoGameMenu).mockResolvedValue(mockCasinoGameMenu)
    vi.mocked(getFavoriteGameList).mockResolvedValue(mockFavoriteGameList)
    await store.fetchCasinoData(false)

    expect(store.gameList).toEqual(mockCasinoGameList.gameList)
    expect(store.favoriteGameList).toEqual([mockCasinoGameList.gameList[0], mockCasinoGameList.gameList[1]])
  })

  it('fetchCasinoData should reject when request fails', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(getCasinoGameList).mockRejectedValue(mockError)
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    await expect(store.fetchCasinoData(false)).rejects.toThrow(mockError)
    expect(console.error).toHaveBeenCalledWith(mockError)
  })

  it('addFavoriteGame should add favorite game', async () => {
    vi.mocked(getCasinoGameList).mockResolvedValue(mockCasinoGameList)
    vi.mocked(getCasinoGameMenu).mockResolvedValue(mockCasinoGameMenu)
    vi.mocked(getFavoriteGameList).mockResolvedValue([])
    vi.mocked(addFavoriteGameList).mockResolvedValue(undefined)
    await store.fetchCasinoData(false)
    await store.addFavoriteGame(1)
    expect(store.favoriteGameList).toEqual([mockCasinoGameList.gameList[0]])
  })

  it('addFavoriteGame should reject when request fails', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(addFavoriteGameList).mockRejectedValue(mockError)
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    await expect(store.addFavoriteGame(1)).rejects.toThrow(mockError)
    expect(console.error).toHaveBeenCalledWith(mockError)
  })

  it('removeFavoriteGame should remove favorite game', async () => {
    vi.mocked(getCasinoGameList).mockResolvedValue(mockCasinoGameList)
    vi.mocked(getCasinoGameMenu).mockResolvedValue(mockCasinoGameMenu)
    vi.mocked(getFavoriteGameList).mockResolvedValue(mockFavoriteGameList)
    vi.mocked(removeFavoriteGameList).mockResolvedValue(undefined)
    await store.fetchCasinoData(false)
    await store.removeFavoriteGame(1)
    expect(store.favoriteGameList).toEqual([mockCasinoGameList.gameList[1]])
  })

  it('removeFavoriteGame should reject when request fails', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(removeFavoriteGameList).mockRejectedValue(mockError)
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    await expect(store.removeFavoriteGame(1)).rejects.toThrow(mockError)
    expect(console.error).toHaveBeenCalledWith(mockError)
  })

  it('getGameListByMenuId should return gameList', async () => {
    vi.mocked(getCasinoGameList).mockResolvedValue(mockCasinoGameList)
    vi.mocked(getCasinoGameMenu).mockResolvedValue(mockCasinoGameMenu)
    vi.mocked(getFavoriteGameList).mockResolvedValue(mockFavoriteGameList)
    await store.fetchCasinoData(false)

    expect(store.getGameListByMenuId(84)).toEqual([mockCasinoGameList.gameList[0], mockCasinoGameList.gameList[1]])
  })

  it('getGameListByMenuId should return empty array when menuId is not found', async () => {
    vi.mocked(getCasinoGameList).mockResolvedValue(mockCasinoGameList)
    vi.mocked(getCasinoGameMenu).mockResolvedValue(mockCasinoGameMenu)
    vi.mocked(getFavoriteGameList).mockResolvedValue(mockFavoriteGameList)
    await store.fetchCasinoData(false)

    expect(store.getGameListByMenuId(99)).toEqual([])
  })

  it('getGame should return game', async () => {
    vi.mocked(getCasinoGameList).mockResolvedValue(mockCasinoGameList)
    vi.mocked(getCasinoGameMenu).mockResolvedValue(mockCasinoGameMenu)
    vi.mocked(getFavoriteGameList).mockResolvedValue(mockFavoriteGameList)
    await store.fetchCasinoData(false)

    expect(store.getGame(1)).toEqual(mockCasinoGameList.gameList[0])
  })

  it('getGameMenu should return gameMenu', async () => {
    vi.mocked(getCasinoGameList).mockResolvedValue(mockCasinoGameList)
    vi.mocked(getCasinoGameMenu).mockResolvedValue(mockCasinoGameMenu)
    vi.mocked(getFavoriteGameList).mockResolvedValue(mockFavoriteGameList)
    await store.fetchCasinoData(false)

    expect(store.getGameMenu(84)).toEqual({ id: 84, gameCount: 2, sort: 1, name: 'All', games: [1, 2], lower: [] })
  })

  it('getTopGameMenu should return top gameMenu', async () => {
    vi.mocked(getCasinoGameList).mockResolvedValue(mockCasinoGameList)
    vi.mocked(getCasinoGameMenu).mockResolvedValue(mockCasinoGameMenu)
    vi.mocked(getFavoriteGameList).mockResolvedValue(mockFavoriteGameList)
    await store.fetchCasinoData(false)

    const topGameMenu = store.getTopGameMenu()
    expect(topGameMenu).toHaveLength(3)
    expect(topGameMenu[0]).toEqual({ id: 2, gameCount: 1, sort: 2, name: 'Hot', games: [1], lower: [] })
    expect(topGameMenu[1]).toEqual({ id: 1, gameCount: 1, sort: 3, name: 'New', games: [1], lower: [] })
    expect(topGameMenu[2]).toEqual({ id: 84, gameCount: 2, sort: 1, name: 'All', games: [1, 2], lower: [] })
  })
  
  it('getTopGameMenu should return top gameMenu with demo', async () => {
    const mockGameMenu = [
      mockCasinoGameMenu[0],
      mockCasinoGameMenu[1],
      mockCasinoGameMenu[2],
      { id: 94, gameCount: 1, sort: 5, name: 'Demo', games: [1] },
      mockCasinoGameMenu[3],
      mockCasinoGameMenu[4]
    ] as IGameMenu[]

    vi.mocked(getCasinoGameList).mockResolvedValue(mockCasinoGameList)
    vi.mocked(getCasinoGameMenu).mockResolvedValue(mockGameMenu)
    vi.mocked(getFavoriteGameList).mockResolvedValue(mockFavoriteGameList)
    await store.fetchCasinoData(false)

    const topGameMenu = store.getTopGameMenu()
    expect(topGameMenu).toHaveLength(4)
  })

  it('getSubGameMenu should return sub gameMenu', async () => {
    vi.mocked(getCasinoGameList).mockResolvedValue(mockCasinoGameList)
    vi.mocked(getCasinoGameMenu).mockResolvedValue(mockCasinoGameMenu)
    vi.mocked(getFavoriteGameList).mockResolvedValue(mockFavoriteGameList)
    await store.fetchCasinoData(false)

    const subGameMenu = store.getSubGameMenu()
    expect(subGameMenu).toHaveLength(2)
    expect(subGameMenu[0]).toEqual({ id: 84, sort: 1, name: 'All', games: [1, 2], lower: [], gameCount: 2 })
    expect(subGameMenu[1]).toEqual({ id: 3,  sort: 6, name: 'Casino', games: [1], gameCount: 1, lower: [{ id: 37, sort: 2, name: 'Other', games: [3], gameCount: 1 }] })
  })

  it('getSubGameMenu should return sub gameMenu with demo', async () => {
    const mockGameMenu = [
      mockCasinoGameMenu[0],
      mockCasinoGameMenu[1],
      mockCasinoGameMenu[2],
      { id: 94, gameCount: 1, sort: 5, name: 'Demo', games: [1] },
      mockCasinoGameMenu[3],
      mockCasinoGameMenu[4]
    ] as IGameMenu[]

    vi.mocked(getCasinoGameList).mockResolvedValue(mockCasinoGameList)
    vi.mocked(getCasinoGameMenu).mockResolvedValue(mockGameMenu)
    vi.mocked(getFavoriteGameList).mockResolvedValue(mockFavoriteGameList)
    await store.fetchCasinoData(true)

    const subGameMenu = store.getSubGameMenu()
    expect(subGameMenu).toHaveLength(2)
    expect(subGameMenu[0]).toEqual({ id: 84, sort: 1, name: 'All', games: [1, 2], lower: [], gameCount: 2 })
    expect(subGameMenu[1]).toEqual({ id: 3, sort: 6, name: 'Casino', games: [1], gameCount: 1, lower: [{ id: 37, sort: 2, name: 'Other', games: [3], gameCount: 1 }] })
  })

  it('searchGame should return gameList', async () => {
    vi.mocked(getCasinoGameList).mockResolvedValue(mockCasinoGameList)
    vi.mocked(getCasinoGameMenu).mockResolvedValue(mockCasinoGameMenu)
    vi.mocked(getFavoriteGameList).mockResolvedValue(mockFavoriteGameList)
    await store.fetchCasinoData(false)

    expect(store.searchGame('Game 1')).toEqual([mockCasinoGameList.gameList[0]])
  })
})
