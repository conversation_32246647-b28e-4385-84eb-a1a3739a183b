import { defineStore } from 'pinia'
import type { IGameInfo, IGameMenu } from '@/types/game'
import { getCasinoGameList, getCasinoGameMenu } from '@/api/game'
import { getFavoriteGameList, addFavoriteGameList, removeFavoriteGameList } from '@/api/favorite'
import { GameType } from '@/types/game'
import { useCookies } from 'vue3-cookies'
import { useEjpStore } from '@/stores/ejpremote'

export const useCasinoGameStore = defineStore('casino_game', () => {
  const gameList = ref<IGameInfo[]>([])
  const favoriteGameList = ref<IGameInfo[]>([])
  const gameMenu = ref<IGameMenu[]>([])
  const allGameMenu = ref<IGameMenu[]>([])
  const isDemo = ref<boolean>(false)
  const { cookies } = useCookies()
  const ejpStore = useEjpStore()

  const fetchGameList = async (isMobile: boolean) => {
    try {
      const params = {
        session_id: cookies.get('SESSION_ID'),
        hall_id: Number(localStorage.getItem('hallinfo_hallid')),
        is_mobile: isMobile,
        lang: cookies.get('lang'),
      }
      const res = await getCasinoGameList({...params})
      gameList.value = res.gameList
      ejpStore.setJackpotSwitch(res.jackpotSwitch || false)
    } catch (err) {
      console.error(err)
      return Promise.reject(err)
    }
  }

  const fetchGameMenu = async (isMobile: boolean) => {
    try {
      const params = {
        session_id: cookies.get('SESSION_ID'),
        hall_id: Number(localStorage.getItem('hallinfo_hallid')),
        is_mobile: isMobile,
        lang: cookies.get('lang'),
      }
      gameMenu.value = await getCasinoGameMenu({...params})
    } catch (err) {
      console.error(err)
      return Promise.reject(err)
    }
  }

  const fetchFavoriteGame = async () => {
    try {
      const params = {
        session_id: cookies.get('SESSION_ID'),
        hall_id: Number(localStorage.getItem('hallinfo_hallid')),
        game_kind: GameType['Casino']
      }

      const res = await getFavoriteGameList({ ...params })

      favoriteGameList.value = gameList.value.filter((game: IGameInfo) => res.includes(game.gameId))
    } catch (err) {
      console.error(err)
      return Promise.reject(err)
    }
  }

  const fetchCasinoData = async (isMobile: boolean) => {
    try {
      await Promise.all([
        fetchGameList(isMobile),
        fetchGameMenu(isMobile)
      ])

      if (cookies.get('SESSION_ID')) await fetchFavoriteGame()

      const resGameMenu = gameMenu.value
      gameMenu.value = []
      resGameMenu.sort((a: IGameMenu, b: IGameMenu) => a.sort - b.sort);
      isDemo.value = resGameMenu.find((menu: IGameMenu) => menu.id == 94) ? true : false
      allGameMenu.value = []
  
      resGameMenu.forEach((menu: any) => {
        const lower: IGameMenu[] = []
  
        if (menu.lower) {
          Object.entries(menu.lower).forEach(([, lowerMenu]: [string, any]) => {
            lower.push({
              id: lowerMenu.id,
              gameCount: lowerMenu.gameCount,
              sort: lowerMenu.sort,
              name: lowerMenu.name,
              games: lowerMenu.games,
            })
          })
        }
  
        gameMenu.value.push({
          id: menu.id,
          gameCount: menu.gameCount,
          sort: menu.sort,
          name: menu.name,
          games: menu.games,
          lower: lower,
        })
  
        allGameMenu.value.push({
          id: menu.id,
          gameCount: menu.gameCount,
          sort: menu.sort,
          name: menu.name,
          games: menu.games,
          lower: lower,
        }, ...lower)
      })
    } catch(err) {
      console.error(err)
      return Promise.reject(err)
    }
  }

  const addFavoriteGame = async (gameid: number) => {
    try {
      const params = {
        hall_id: Number(localStorage.getItem('hallinfo_hallid')),
        session_id: cookies.get('SESSION_ID'),
        game_id: gameid,
        game_kind: GameType['Casino']
      }

      await addFavoriteGameList({...params})

      const game = getGame(gameid)
      if (game) {
        favoriteGameList.value.push(game)
      }
    } catch (err) {
      console.error(err)
      return Promise.reject(err)
    }
  }

  const removeFavoriteGame = async (gameid: number) => {
    try {
      const params = {
        hall_id: Number(localStorage.getItem('hallinfo_hallid')),
        session_id: cookies.get('SESSION_ID'),
        game_id: gameid,
        game_kind: GameType['Casino']
      }

      await removeFavoriteGameList({...params})

      favoriteGameList.value = favoriteGameList.value.filter((game: IGameInfo) => game.gameId !== gameid)
    } catch (err) {
      console.error(err)
      return Promise.reject(err)
    }
  }

  const getGameListByMenuId = (menuId: number) => {
    const menu = getGameMenu(menuId)
    if (menu) {
      return menu.games.map((gameId: number) => gameList.value.find((game: IGameInfo) => game.gameId == gameId))
    }
    return []
  }

  const getGame = (gameid: number) => {
    return gameList.value.find((game: IGameInfo) => game.gameId == gameid)
  }

  const getGameMenu = (menuId: number) => {
    return allGameMenu.value.find((menu: IGameMenu) => menu.id == menuId)
  }

  const getTopGameMenu = () => {
    // 95: 試玩, 2: 熱門, 1: 最新, 85: 特色遊戲, 84: 全部
    const menuId = isDemo.value ? [94, 2, 1, 4, 85, 84] : [2, 1, 4, 85, 84]
    const result = menuId.reduce((acc, id) => {
      const menu = allGameMenu.value.find(menu => menu.id === id)
      if (menu) acc.push(menu)
      return acc
    }, [] as IGameMenu[])
    return result
  }

  const getSubGameMenu = () => {
    // 84: 全部, 3: 老虎機, 82: 大型機台
    const subMenuId = [84, 3, 82]
    const result = subMenuId.reduce((acc, id) => {
      const menu = allGameMenu.value.find(menu => menu.id === id)
      if (menu) acc.push(menu)
      return acc
    }, [] as IGameMenu[])
    return result
  }

  const searchGame = (search: string) => {
    return gameList.value.filter((game: IGameInfo) => {
      return game.name.toLowerCase().includes(search.toLowerCase())
    })
  }

  return { gameList, gameMenu, favoriteGameList, fetchGameList, fetchGameMenu, fetchCasinoData, fetchFavoriteGame, getGameMenu, getGameListByMenuId, getTopGameMenu, getSubGameMenu, searchGame, getGame, addFavoriteGame, removeFavoriteGame }
})
