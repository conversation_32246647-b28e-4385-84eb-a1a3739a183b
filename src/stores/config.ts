import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import axios from 'axios'
import type { SiteConfig } from '@/types/config'

export const useConfigStore = defineStore('config', () => {
  const config = ref<SiteConfig | null>(null)
  const apiUrl = ref('')
  const isLoading = ref(false)
  const isConfigError = ref(false)
  const TEST_TIMEOUT = 3000

  const cdnUrl = computed(() => config.value?.x_cdn || '')

  const testApiEndpoints = async () => {
    if (!config.value?.api.length) return

    apiUrl.value = config.value.api[0]

    try {
      await Promise.any(
        config.value.api.map(async (url) => {
          try {
            await axios.get(`${url}/testtime.php`, { timeout: TEST_TIMEOUT })
            apiUrl.value = url
            return url
          } catch {
            throw new Error(`Failed to connect to ${url}`)
          }
        })
      )
    } catch {
      // 所有測試都失敗，改為使用原網址
      apiUrl.value = ''
    }
  }

  const fetchConfig = async () => {
    if (config.value) return
    if (!import.meta.env.VITE_CCD_CONFIG_URL) return

    isLoading.value = true
    isConfigError.value = false

    try {
      const response = await axios.get<SiteConfig>(import.meta.env.VITE_CCD_CONFIG_URL, {
        timeout: TEST_TIMEOUT
      })
      config.value = response.data
      await testApiEndpoints()
    } catch (err) {
      console.error('Failed to fetch config:', err)
    } finally {
      isLoading.value = false
    }
  }

  return {
    config,
    isLoading,
    isConfigError,
    cdnUrl,
    apiUrl,
    fetchConfig,
    testApiEndpoints
  }
})