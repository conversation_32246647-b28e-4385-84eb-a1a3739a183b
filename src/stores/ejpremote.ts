import type { jpData, rankData } from '@/types/game'

import { defineStore } from 'pinia'
import { useApiUrl } from '@/utils/apiUrl'

export const useEjpStore = defineStore('ejpremote', () => {
  const jpList = ref<jpData[]>([])
  const jpAmount = ref(0)
  const topList = ref<rankData[]>([])
  const jackpotSwitch = ref(false)
  const isShowJackpot = computed(() => jackpotSwitch.value && jpList.value.length > 0)
  let timeCounter = -1 as any
  let ws: WebSocket | undefined

  const connectWebSocket = () => {
    if (!jackpotSwitch.value || ws?.url) return
  
    const apiUrl = useApiUrl()
    const path = '/fxGM/fxLB?gameType=ejp'
    const url = apiUrl.value || location.origin
    const isHttps = url.includes('https')
    const host = isHttps ? url.split('https://')[1] : url.split('http://')[1]
    const link = `${isHttps ? 'wss://' : 'ws://'}${host}${path}`
    ws = new WebSocket(link)

    ws.onopen = () => {
      console.log('WebSocket connected')
    }
    
    ws.onmessage = (event) => {

      const data = JSON.parse(event.data)
      
      switch (data.action) {
        case 'ready': 
          ws?.send(JSON.stringify({
            action: "setHallID",
            hallID: localStorage.getItem('hallinfo_hallid')
          }))
          break

        case 'updateWinnerList3':
          topList.value = data.grand20.map((gamer: string) => {
            return {
              name: gamer.split(' ')[0],
              score: gamer.split(' ')[1],
            }
          })
          break

        case 'updateJP':
          jpAmount.value = data.JP1
          break

        case 'updateJP2':
       
          if(data.a.length > 0) {
            data.a.forEach(( game: jpData) => {
              jpList.value.push({ ...game })
            })
          }
          break

        default:
          break
      }
    }
  
    ws.onerror = (error) => {
      console.error('WebSocket error:', error)
    }
  
    ws.onclose = () => {
      console.log('WebSocket closed')
      timeCounter = setTimeout(connectWebSocket, 60000)
    }
  }

  const disconnectWebSocket = () => {
    clearTimeout(timeCounter)
    ws?.close()
    ws = undefined
  }

  const setJackpotSwitch = (value: boolean) => {
    jackpotSwitch.value = value
  }
  
  return {
    connectWebSocket,
    disconnectWebSocket,
    setJackpotSwitch,
    jpAmount,
    topList,
    jpList,
    isShowJackpot,
    jackpotSwitch
  }
})