import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { useFishingGameStore } from '@/stores/fishingGame'
import { getBBfishingList, getBBfishingIntro } from '@/api/bbfish'
import type { IGameInfo } from '@/types/game'

const mockResponse = [
  { gameId: 1, name: 'Game 1', icon: '', link: 'link1', gameKind: 38, ruleLink: '/rule1' },
  { gameId: 2, name: 'Game 2', icon: '', link: 'link2', gameKind: 38, ruleLink: '/rule2' },
] as IGameInfo[]

vi.mock('@/api/bbfish', () => ({
  getBBfishingList: vi.fn(),
  getBBfishingIntro: vi.fn()
}))

vi.mock('vue3-cookies', () => ({
  useCookies: vi.fn().mockReturnValue({
    cookies: {
      get: (key: string) => {
        if (key === 'SESSION_ID') return 'session_id'
        if (key === 'lang') return 'en'
        return null
      }
    }
  })
}))

describe('useFishingGameStore', () => {
  let store: ReturnType<typeof useFishingGameStore>

  beforeEach(() => {
    setActivePinia(createPinia())
    store = useFishingGameStore()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('fetchFishingData should populate gameList', async () => {
    vi.mocked(getBBfishingList).mockResolvedValue(mockResponse)
    await store.fetchFishingData(false)

    expect(store.gameList).toHaveLength(2)
    expect(store.gameList[0]).toEqual({
      gameId: 1,
      name: 'Game 1',
      icon: '',
      link: 'link1',
      gameKind: 38,
      ruleLink: '/rule1',
    })
  })

  it('fetchFishingData with mobile', async () => {
    vi.mocked(getBBfishingList).mockResolvedValue(mockResponse)
    await store.fetchFishingData(true)

    expect(getBBfishingList).toHaveBeenCalledWith({
      hall_id: 0,
      is_mobile: true,
      lang: 'en',
      session_id: 'session_id',
    })
  })

  it('fetchFishingData should reject when request fails', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(getBBfishingList).mockRejectedValue(mockError)
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    await expect(store.fetchFishingData(false)).rejects.toThrow(mockError)
    expect(console.error).toHaveBeenCalledWith(mockError)
  })

  it('getGameList should return game list', async () => {
    vi.mocked(getBBfishingList).mockResolvedValue(mockResponse)
    await store.fetchFishingData(false)
    const result = store.getGameList()

    expect(result).toHaveLength(2)
    expect(result[0].gameId).toBe(1)
    expect(result[1].gameId).toBe(2)
  })

  it('fetchFishingIntro should reject when request fails', async () => {
    const mockError = new Error('Network Error')
    vi.mocked(getBBfishingIntro).mockRejectedValue(mockError)
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    await expect(store.fetchFishingIntro()).rejects.toThrow(mockError)
    expect(console.error).toHaveBeenCalledWith(mockError)
  })

  it('searchGame should return filtered game list', async () => {
    vi.mocked(getBBfishingList).mockResolvedValue(mockResponse)
    await store.fetchFishingData(false)
    const result = store.searchGame('game 2')

    expect(result).toHaveLength(1)
    expect(result[0].name).toBe('Game 2')
  })
})
