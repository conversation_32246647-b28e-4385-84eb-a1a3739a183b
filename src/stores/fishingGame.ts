import { defineStore } from 'pinia'
import type { IGameInfo, IGameIntro } from '@/types/game'
import { getBBfishingList, getBBfishingIntro } from '@/api/bbfish'
import { useCookies } from "vue3-cookies"

export const useFishingGameStore = defineStore('fishing_game', () => {
  const gameList = ref<IGameInfo[]>([])
  const fishingIntro = ref<IGameIntro[]>([])
  const { cookies } = useCookies()
  const gameNames = computed(() => gameList.value.reduce((acc, item) => {
    acc[item.gameId] = item.name
    return acc
  }, {} as { [key: number]: string }))


  const fetchFishingData = async (isMobile: boolean) => {
    try {
      const params = {
        session_id: cookies.get('SESSION_ID'),
        hall_id: Number(localStorage.getItem('hallinfo_hallid')),
        is_mobile: isMobile,
        lang: cookies.get('lang'),
      }

      gameList.value = await getBBfishingList({...params})
    } catch (error) {
      console.error(error)
      return Promise.reject(error)
    }
  }

  const fetchFishingIntro = async () => {
    try {
      const res = await getBBfishingIntro()
      fishingIntro.value = res.introData
    } catch (error) {
      console.error(error)
      return Promise.reject(error)
    }
  }

  const getGameList = () => {
    return gameList.value
  }

  const searchGame = (search: string) => {
    return gameList.value.filter((game: IGameInfo) => {
      return game.name.toLowerCase().includes(search.toLowerCase())
    })
  }

  return { gameList, fishingIntro, gameNames, fetchFishingData, fetchFishingIntro, getGameList, searchGame }
})
