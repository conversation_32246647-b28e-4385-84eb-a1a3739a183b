import { beforeEach, describe, expect, it, vi } from 'vitest'
import { createP<PERSON>, setActivePinia } from 'pinia'
import apiRequest from '@/utils/apiRequest';
import { nextTick } from 'vue'
import { useHallInfoStore } from './hallInfo';

vi.mock('@/utils/apiRequest', () => {
  return {
    default: {
      get: vi.fn(),
    }
  }
})

describe('useHallInfoStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('getHallInfo', async () => {
    const store = useHallInfoStore()


    vi.mocked(apiRequest.get).mockResolvedValue({
      data: {
        data: {
          hall_id: 3820474,
          website: 'bbinbgp',
          name: 'BGP API 測試廳',
          login_code: 'bgp',
          enable: true
        }
      }
    })

    await store.getHallInfo()
    await nextTick();

    expect(store.hallInfo).toEqual({
      hallId: 3820474,
      website: 'bbinbgp',
      name: 'BGP API 測試廳',
      loginCode: 'bgp',
      enable: true
    })
  })
})
