import type { HallInfo } from '@/types/hall'
import { defineStore } from 'pinia'
import { getHallInfo as getHallInfoData } from '@/api/hall'

export const useHallInfoStore = defineStore('hall_info', () => {
  const hallInfo = ref<HallInfo>({} as HallInfo)

  const getHallInfo = async () => {
    try {
      const res = await getHallInfoData()
            
      if (res) {
        localStorage.setItem('hallinfo_hallid', res.hallId?.toString())
        localStorage.setItem('hallinfo_website', res.website)
        localStorage.setItem('hallinfo_name', res.name)
        localStorage.setItem('hallinfo_logincode', res.loginCode)
        localStorage.setItem('hallinfo_enable', res.enable?.toString())
      }

      hallInfo.value = res
    } catch(error) {
      console.error(error)
      return Promise.reject(error)
    }
  }

  return { getHallInfo, hallInfo }
})