import { setActivePinia, createPinia } from 'pinia'
import { useLangDictStore } from './langDict'
import apiRequest from '@/utils/apiRequest';
import { describe, expect, it, vi, beforeEach } from 'vitest'
import { nextTick } from 'vue'

vi.mock('@/utils/apiRequest', () => {
  return {
    default: {
      get: vi.fn(),
    }
  }
})

describe('useLangDictStore', () => {
  const mockResponse = {
    data: {
      code: 0,
      message: '',
      data: [
        {
          index: 'S_ACCOUNT_INQUIRY',
          translated: '往來記錄'
        },
        {
          index: 'S_ACOU',
          translated: '帳 號'
        }
      ]
    }
  }

  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('fetchData', async () => {
    const store = useLangDictStore()

    vi.mocked(apiRequest.get).mockResolvedValue(mockResponse)

    await store.fetchData('zh-cn')
    await nextTick();

    expect(store.langDict).toEqual({
      'S_ACCOUNT_INQUIRY': '往來記錄',
      'S_ACOU': '帳 號'
    })
  })

  it('fetchData_duplicate', async () => {
    const store = useLangDictStore()

    vi.mocked(apiRequest.get).mockResolvedValue(mockResponse)

    const langDict1 = store.getLangDict('zh-cn')
    const langDict2 = await store.getLangDict('zh-cn')

    expect(langDict1).toBeInstanceOf(Promise)
    expect(langDict2).toEqual({
      'S_ACCOUNT_INQUIRY': '往來記錄',
      'S_ACOU': '帳 號'
    })
  })

  it('executeApiCall_api_error', async () => {
    const store = useLangDictStore()
    const mockError = new Error('API Error')
    vi.mocked(apiRequest.get).mockRejectedValue(mockError)
    vi.spyOn(console, 'error').mockImplementation(() => {})

    await store.fetchData('zh-cn')

    expect(console.error).toHaveBeenCalledWith(mockError)
  })
})