import { defineStore } from 'pinia'
import { getLangList } from '@/api/lang'
import { LangDict } from "@/types/dict"

export const useLangDictStore = defineStore('lang_dict', () => {
  const langDict = ref<LangDict>({})
  let currentFetch: Promise<void> | null = null

  const executeApiCall = async (lang: string) => {
    const res = await getLangList({ lang })
    langDict.value = res
  }

  const fetchData = async (lang: string) => {
    if (currentFetch) {
      return currentFetch
    }

    try {
      currentFetch = executeApiCall(lang)
      await currentFetch
    } catch (err) {
      console.error(err)
    } finally {
      currentFetch = null
    }
  }

  const getLangDict = async (lang: string) => {
    if (!langDict.value || Object.keys(langDict.value).length === 0) {
      await fetchData(lang)
    }

    return langDict.value
  }

  return { getLangDict, fetchData, langDict }
})