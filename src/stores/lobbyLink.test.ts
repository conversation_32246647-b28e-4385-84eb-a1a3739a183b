import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useLobbyLinkStore } from '@/stores/lobbyLink'
import { getLotteryLobbyLink } from '@/api/game'
import { testApiEndpoints } from '@/utils/testApiEndpoints'

vi.mock('@/api/game', () => ({
  getLotteryLobbyLink: vi.fn(),
}))

vi.mock('@/utils/testApiEndpoints', () => {
  return {
    testApiEndpoints: vi.fn()
  }
})

describe('useLobbyLinkStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('fetchLotteryLobby', async () => {
    const store = useLobbyLinkStore()
    const mockApiResponse = {
      domain: ['a.com', 'b.com'],
      link: '/test.json'
    }
    const mockTestResponse = {
      domain: 'https://b.com',
      domainPath: 'b.com',
      url: 'https://b.com/test.json'
    }

    vi.mocked(getLotteryLobbyLink).mockResolvedValue(mockApiResponse)
    vi.mocked(testApiEndpoints).mockResolvedValue(mockTestResponse)
    
    const result = await store.fetchLotteryLobby({
      hall_id: 123,
      session_id: '456',
      domain_url: 'test.com',
      lang: 'zh-cn',
      is_mobile: false
    })

    expect(store.lobbyLink.lottery).toEqual(mockTestResponse)
    expect(result).toEqual(mockTestResponse)
  })
})
