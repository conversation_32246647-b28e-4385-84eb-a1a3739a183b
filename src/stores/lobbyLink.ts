import { defineStore } from 'pinia'
import { getLotteryLobbyLink } from '@/api/game'
import { IGetLotteryLobbyLinkParams } from '@/types/lottery'
import { testApiEndpoints } from '@/utils/testApiEndpoints'
import { GameType } from '@/types/game'

interface ILobbyLink {
  [key: string]: {
    domain: string
    url: string,
    domainPath: string
  }
}

export const useLobbyLinkStore = defineStore('lobby_link', () => {
  const lobbyLink: Ref<ILobbyLink> = ref({
    lottery: { domain: '', url: '', domainPath: '' }
  })
  
  const fetchLotteryLobby = async (params: IGetLotteryLobbyLinkParams) => {
    try {
      const res = await getLotteryLobbyLink(params)
      const testResult = await testApiEndpoints({
        ...res,
        gameType: GameType.Lottery
      })
      lobbyLink.value.lottery = testResult
      return testResult
    } catch (error) {
      return Promise.reject(error)
    }
  }

  return { fetchLotteryLobby, lobbyLink }
})