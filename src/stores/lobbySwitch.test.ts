import { beforeEach, describe, expect, it, vi } from 'vitest'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { getLobbySwitch as getLobbySwitchData } from '@/api/lobbyswitch';
import { nextTick } from 'vue'
import { useLobbySwitchStore } from './lobbySwitch';

vi.mock('vue-i18n', () => ({
  useI18n: vi.fn(() => ({
    t: vi.fn((msg: string) => msg),
  })),
}));

vi.mock('@/api/lobbyswitch', () => ({
  getLobbySwitch: vi.fn()
}))

vi.mock('vue3-cookies', () => ({
  useCookies: vi.fn().mockReturnValue({
    cookies: {
      get: (key: string) => {
        if (key === 'session_id') return 'session_id'
        if (key === 'lang') return 'en'
        return null
      }
    }
  }),
}))

const defaultGameMaintain = {
  maintain: false,
  message: ''
}

const maintainInfo = {
  live: { ...defaultGameMaintain },
  casino: { ...defaultGameMaintain },
  lottery: { ...defaultGameMaintain },
  sport: { ...defaultGameMaintain },
  fish: { ...defaultGameMaintain },
  card: { ...defaultGameMaintain }
}

describe('useLobbySwitchStore', () => {
  const mockResponse = {
    lobbySwitch: {
      3: true,
      5: true,
      12: true
    },
    maintainInfo: JSON.parse(JSON.stringify(maintainInfo))
  }

  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('getLobbySwitch', async () => {
    const store = useLobbySwitchStore()

    vi.mocked(getLobbySwitchData).mockResolvedValue(mockResponse)

    await store.getLobbySwitch()
    await nextTick()

    expect(store.lobbySwitch).toEqual({
      3: true,
      5: true,
      12: true
    })

    expect(store.maintainInfo).toEqual(maintainInfo)

    expect(store.pcMenuList).toEqual([
      {
        gameId: 3,
        title: 'game_3_main',
        link: '/live',
        sub: {
          live: {
            title: 'game_3_sub',
            lobbyLink: {
              isMobile: false,
              enterPage: 'livedealer',
              fnKey: 'live',
              windowName: 'liveInTop'
            },
            nickname: 'BB',
            game_id: 3
          }
        }
      },
      {
        gameId: 5,
        title: 'game_5_main',
        link: '/game',
        sub: { bbcasino: { title: 'game_5_sub', link: '/game/5', nickname: 'BB', game_id: 5 } }
      },
      {
        gameId: 12,
        title: 'game_12_main',
        link: '/ltlottery',
        sub: { ltlottery: { title: 'game_12_sub', link: '/ltlottery', nickname: '', game_id: 12 } }
      }
    ])

    expect(store.mMenuList).toEqual([
      {
        title: 'game_3_main',
        category: 'live',
        list: [{ name: 'game_3_sub', key: 'live', link: '/m/live', code: 3 }]
      },
      {
        title: 'game_5_main',
        category: 'casino',
        list: [{ name: 'game_5_sub', key: 'game', link: '/m/casino', code: 5 }]
      },
      {
        title: 'game_12_main',
        category: 'lottery',
        list: [{ name: 'game_12_sub', key: 'lottery', link: '/m/lottery', code: 12 }]
      }
    ])
  })
})
