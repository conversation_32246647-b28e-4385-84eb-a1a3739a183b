import { defineStore } from 'pinia'
import { getLobbySwitch as getLobbySwitchData } from '@/api/lobbyswitch';
import { useCookies } from "vue3-cookies"
import { INavItem, IMNavItem } from '@/types/navbar'
import { ICashSelectData } from '@/types/cashrecord';
import { ILobbySwitch } from '@/types/lobbyswitch';
import { useI18n } from 'vue-i18n'

export const useLobbySwitchStore = defineStore('lobby_switch', () => {
  const { t } = useI18n()
  const lobbySwitch = ref<ILobbySwitch['lobbySwitch']>({})
  const { cookies } = useCookies()
  const pcMenuList = ref<INavItem[]>([] as INavItem[])
  const mMenuList = ref<IMNavItem[]>([] as IMNavItem[])
  const cashSelectList = ref<ICashSelectData[]>([] as ICashSelectData[])
  const defaultGameMaintain = {
    maintain: false,
    message: ''
  }
  const maintainInfo = ref<ILobbySwitch['maintainInfo']>({
    live: { ...defaultGameMaintain },
    casino: { ...defaultGameMaintain },
    lottery: { ...defaultGameMaintain },
    sport: { ...defaultGameMaintain },
    fish: { ...defaultGameMaintain },
    card: { ...defaultGameMaintain }
  })

  const getLobbySwitch = async () => {
    try {
      const res = await getLobbySwitchData({ 
        hall_id: Number(localStorage.getItem('hallinfo_hallid')),
        session_id: cookies.get('SESSION_ID')
      })

      lobbySwitch.value = res.lobbySwitch
      pcMenuList.value = getPCMenuList(res.lobbySwitch)
      mMenuList.value = getMobileMenuList(res.lobbySwitch)
      cashSelectList.value = getCashSelectList(res.lobbySwitch)
      maintainInfo.value = res.maintainInfo
    }  catch(error) {
      console.error(error)
      return Promise.reject(error)
    }
  }

  const getPCMenuList = (data: {[key: string]: boolean}) => {
    const mainItems: {[key: string]: any} = {
      '3': {
        gameId: 3,
        title: t('game_3_main'),
        link: '/live',
      },
      '5': {
        gameId: 5,
        title: t('game_5_main'),
        link: '/game',
      },
      '12': {
        gameId: 12,
        title: t('game_12_main'),
        link: '/ltlottery',
      },
      '31': {
        gameId: 31,
        title: t('game_109_main'),
        lobbyLink: { isMobile: false, enterPage: '', fnKey: 'sport', windowName: 'nbb' },
      },
      '66': {
        gameId: 66,
        title: t('game_66_main'),
        link: '/card/66',
      }
    }
    const subItems: {[key: string]: any} = {
      '3': {
        live: {
          title: t('game_3_sub'),
          lobbyLink: { isMobile: false, enterPage: 'livedealer', fnKey: 'live', windowName: 'liveInTop' },
          nickname: 'BB',
          game_id: 3
        }
      },
      '5': {
        bbcasino: {
          title: t('game_5_sub'),
          link: '/game/5',
          nickname: 'BB',
          game_id: 5,
        }
      },
      '12': {
        ltlottery: {
          title: t('game_12_sub'),
          link: '/ltlottery',
          nickname: '',
          game_id: 12
        }
      },
      '31': {
        nbb: {
          title: t('game_31_sub_nbb'),
          lobbyLink: { isMobile: false, enterPage: '', fnKey: 'sport', windowName: 'nbb' },
          nickname: '',
          game_id: 31
        },
        enbb: {
          title: t('game_31_sub_enbb'),
          lobbyLink: { isMobile: false, enterPage: 'esports', fnKey: 'sport', windowName: 'esports' },
          nickname: '',
          game_id: 31
        }
      },
      '38': {
        fisharea: {
          title: t('game_38_sub'),
          link: '/game/fisharea',
          nickname: 'Fishing',
          game_id: 38
        }
      },
      '66': {
        bbcard: {
          title: t('game_66_sub'),
          link: '/card/66',
          nickname: 'BB',
          game_id: 66
        }
      }
    }
    const subMapMain: {[key: string]: string} = {
      '38': '5'
    }
    const sortIndex = [31, 3, 5, 12, 66]

    const result = Object.entries(data).reduce((acc, [key, value]) => {
      if (value) {
        const keyVal = subMapMain[key] || key
        if (acc[keyVal]) {
          acc[keyVal].sub = {
            ...acc[keyVal].sub,
            ...subItems[key]
          }
        } else if(mainItems[keyVal]) {
          acc[keyVal] = {
            ...mainItems[keyVal],
            sub: {
              ...subItems[key]
            }
          }
        }
      }
      return acc
    }, {} as any)

    return sortIndex.reduce((acc, index) => {
      const findItem = result[index]
      if (findItem) acc.push(findItem)
      return acc
    }, [] as any) as INavItem[]
  }

  const getMobileMenuList = (data: {[key: string]: boolean}) => {
    const mainItems: {[key: string]: any} = {
      '3': {
        title: t('game_3_main'),
        category: "live"
      },
      '5': {
        title: t('game_5_main'),
        category: "casino",
      },
      '12': {
        title: t('game_12_main'),
        category: "lottery"
      },
      '31': {
        title: t('game_109_main'),
        category: "ball",        
      },
      '66': {
        title: t('game_66_main'),
        category: "card",
      }
    }
    const subItems: {[key: string]: any} = {
      '3': [{
        name: t('game_3_sub'),
        key: "live",
        link: "/m/live",
        code: 3
      }],
      '5': [{
        name: t('game_5_sub'),
        key: "game",
        link: "/m/casino",
        code: 5
      }],
      '12': [{
        name: t('game_12_sub'),
        key: "lottery",
        link: "/m/lottery",
        code: 12
      }],
      '31':[
        {
          name: t('game_31_sub_nbb'),
          key: "bcsport",
          lobbyLink: { enterPage: '' },
          code: 31
        },
        {
          name: t('game_31_sub_enbb'),
          key: "esports",
          lobbyLink: { enterPage: 'esports' },
          code: 31
        }
      ],
      '38': [{
        name: t('game_38_sub'),
        key: "fisharea",
        link: "/m/fishing",
        code: "fisharea"
      }],
      '66': [{
        name: t('game_66_sub'),
        key: "bbcard",
        link: "/m/card",
        code: 66
      }]
    }
    const subMapMain: {[key: string]: string} = {
      '38': '5'
    }
    const sortIndex = [31, 3, 5, 12, 66]

    const result = Object.entries(data).reduce((acc, [key, value]) => {
      if (value) {
        const keyVal = subMapMain[key] || key
        if (acc[keyVal]) {
          acc[keyVal].list = [
            ...acc[keyVal].list,
            ...subItems[key]
          ]
        } else if(mainItems[keyVal]) {
          acc[keyVal] = {
            ...mainItems[keyVal],
            list: [
              ...subItems[key]
            ]
          }
        }
      }
      return acc
    }, {} as any)

    return sortIndex.reduce((acc, index) => {
      const findItem = result[index]
      if (findItem) acc.push(findItem)
      return acc
    }, [] as any) as IMNavItem[]
  }

  const getCashSelectList = (data: {[key: string]: boolean}) => {
    const items: {[key: string]: any} = {
      '0': {
        id: 0,
        name: t('S_ALL'),
        disabled: false
      },
      '3': {
        id: 3,
        name: t('game_3_sub'),
        disabled: false
      },
      '5': {
        id: 5,
        name: t('game_5_sub'),
        disabled: false
      },
      '12': {
        id: 12,
        name: t('game_12_sub'),
        disabled: false
      },
      '31': {
        id: 31,
        name: t('game_31_sub_nbb'),
        disabled: false
      },
      '38': {
        id: 38,
        name: t('game_38_sub'),
        disabled: false
      },
      '66': {
        id: 66,
        name: t('game_66_sub'),
        disabled: false
      }
    }

    const result = Object.entries(data).reduce((acc, [key, value]) => {
      if (value && items[key]) acc.push(items[key])
      return acc
    }, [items['0']] as ICashSelectData[])

    return result
  }

  return { getLobbySwitch, lobbySwitch, pcMenuList, mMenuList, cashSelectList, maintainInfo }
})