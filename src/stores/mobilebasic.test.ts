import { beforeEach,afterEach, describe, expect, it, vi } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { useMobileBasicInfoStore } from '@/stores/mobilebasic'

vi.mock('@/utils/date', () => ({
  getTodayDate: vi.fn(() => '2025-03-17')
}));

vi.mock('axios', () => {
  return {
    default: {
      get: vi.fn(),
    }
  }
})

describe('useMobileBasicInfoStore', () => {
  let store: ReturnType<typeof useMobileBasicInfoStore>

  beforeEach(() => {
    setActivePinia(createPinia())
    store = useMobileBasicInfoStore()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  it('get siteInfo', async () => {
    expect(store.siteInfo).toEqual({ 
      isMobile: true,
      unitTime: new Date('2025-03-17').getTime()
    })
  })
})
