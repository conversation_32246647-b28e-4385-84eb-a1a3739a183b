import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { create<PERSON><PERSON>, setActive<PERSON>inia } from 'pinia'

import type { IGameInfo } from '@/types/game';
import localStorageMock from '@/mocks/localStorage';
import { useRecentlyPlay } from './recentPlay'

vi.mock('@/api/recentplay', () => ({
  useRecentlyPlay: vi.fn(),
}));

describe('useRecentlyPlay', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.stubGlobal('localStorage', localStorageMock);
  })

  afterEach(() => {
    vi.clearAllMocks();
    localStorage.clear();
  })

  it('fetchRecentData', () => {
    const recentGames = [
      { gameId: 1, name: 'Game 1' },
      { gameId: 2, name: 'Game 2' },
    ]
    localStorage.setItem('bascinfo_username','TESTUSER')
    localStorage.setItem(`recentplay-TESTUSER`, JSON.stringify(recentGames))
    const savedData = JSON.parse(localStorage.getItem(`recentplay-TESTUSER`)!)
    const store = useRecentlyPlay()
    const recentData = store.fetchRecentData()
    expect(recentData).toEqual(savedData)
  })

  it('addRecentData', () => {
    const recentGames = [{ gameId: 0, name: 'Game 0' }]
    localStorage.setItem('bascinfo_username','TESTUSER')
    localStorage.setItem(`recentplay-TESTUSER`, JSON.stringify(recentGames))
    const store = useRecentlyPlay()
    store.addRecentData({ gameId: 1, name: 'Game 1' } as IGameInfo)
    store.addRecentData({ gameId: 2, name: 'Game 2' } as IGameInfo)

    const savedData = JSON.parse(localStorage.getItem('recentplay-TESTUSER')!)
    expect(savedData).toEqual([
      { gameId: 2, name: 'Game 2' },
      { gameId: 1, name: 'Game 1' },
      { gameId: 0, name: 'Game 0' }
    ])
  })

  it('addRecentData_duplicate', () => {
    const store = useRecentlyPlay()
    store.addRecentData({ gameId: 1, name: 'Game 1' } as IGameInfo)
    store.addRecentData({ gameId: 2, name: 'Game 2' } as IGameInfo)
    store.addRecentData({ gameId: 1, name: 'Game 1 Updated' } as IGameInfo)

    const recentData = store.fetchRecentData()
    expect(recentData).toEqual([
      { gameId: 1, name: 'Game 1 Updated' },
      { gameId: 2, name: 'Game 2' },
    ])
  })

  it('addRecentData_more_than_25_rows', () => {
    const store = useRecentlyPlay()

    const games = [];

    for (let i = 1; i <= 30; i++) {
      const game = { gameId: i, name: 'Game ' + i } as IGameInfo
      games.unshift({ ...game })
      store.addRecentData(game)
    }

    const recentData = store.fetchRecentData()
    expect(recentData).toEqual(games.slice(0, 25))
  })
}) 