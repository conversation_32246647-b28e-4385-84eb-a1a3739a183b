import type { IGameInfo } from '@/types/game'
import { defineStore } from 'pinia'

export const useRecentlyPlay = defineStore('recent', () => {
  const recentData = ref<IGameInfo[]>([])
  const loadRecentDataFromLocalStorage = (): IGameInfo[] => {
    const username = localStorage.getItem('bascinfo_username') || ''
    const storedData = localStorage.getItem(`recentplay-${username}`)
    return storedData ? JSON.parse(storedData).slice(0, 25) : []
  }

  const saveRecentDataToLocalStorage = () => {
    const username = localStorage.getItem('bascinfo_username') || ''
    recentData.value = recentData.value.slice(0, 25)
    localStorage.setItem(`recentplay-${username}`, JSON.stringify(recentData.value))
  }

  const fetchRecentData = () => {
    recentData.value = loadRecentDataFromLocalStorage()
    return recentData.value
  }

  const addRecentData = (game: IGameInfo) => {
    fetchRecentData()
    recentData.value = recentData.value.filter(recent => recent.gameId !== game.gameId)
    recentData.value.unshift({ ...game })
    saveRecentDataToLocalStorage()
  }

  const recentPlayList = computed(() => {
    return fetchRecentData()
  })
  
  return {
    recentPlayList,
    fetchRecentData,
    addRecentData
  }
})