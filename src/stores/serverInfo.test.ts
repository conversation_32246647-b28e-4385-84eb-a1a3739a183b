import { beforeEach, describe, expect, it, vi } from 'vitest'
import { create<PERSON><PERSON>, setActivePinia } from 'pinia'
import apiRequest from '@/utils/apiRequest'
import { nextTick } from 'vue'
import { useServerInfoStore } from './serverInfo'

vi.mock('@/utils/apiRequest', () => {
  return {
    default: {
      get: vi.fn(),
    }
  }
})

vi.mock('vue3-cookies', () => ({
  useCookies: vi.fn().mockReturnValue({
    cookies: {
      get: (key: string) => {
        if (key === 'session_id') return 'session_id'
        return null
      }
    }
  }),
}))

describe('useServerInfoStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('getServerInfo', async () => {
    const store = useServerInfoStore()


    vi.mocked(apiRequest.get).mockResolvedValue({
      data: {
        data: {
          server_info: {
            static_file_url: ''
          },
          google_analytics: {
            event: 'custom_pageview',
            user_id: '2958L82',
            hall_id: '',
            game_id: ''
          }
        }
      }
    })

    await store.getServerInfo()
    await nextTick();

    expect(store.serverInfo).toEqual({
      serverInfo: {
        staticFileUrl: ''
      },
      googleAnalytics: {
        event: 'custom_pageview',
        userId: '2958L82',
        hallId: '',
        gameId: ''
      }
    })
  })
})
