import { defineStore } from 'pinia'
import { getServerInfo as getServerInfoData } from '@/api/server'
import type { IServerInfo } from '@/types/server'
import { useCookies } from "vue3-cookies"

export const useServerInfoStore = defineStore('server_info', () => {
  const serverInfo = ref<IServerInfo>({} as IServerInfo)
  const { cookies } = useCookies()

  const getServerInfo = async () => {
    try {
      const res = await getServerInfoData({ 
        hall_id: Number(localStorage.getItem('hallinfo_hallid')),
        session_id: cookies.get('SESSION_ID')
      })
      serverInfo.value = res
    } catch(error) {
      console.error(error)
      return Promise.reject(error)
    }
  }

  return { getServerInfo, serverInfo }
})