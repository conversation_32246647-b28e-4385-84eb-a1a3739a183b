export interface ILiveRecord {
  maintain: boolean
  maintainInfo: string
  searchRange: {
    startDate: string
    endDate: string
  }
  wagersList: {
    id: number
    wagersDate: string
    gameType: number
    roundSerial: number
    roundNo: string
    tableCode: string
    betAmount: string
    commissionable: string
    payoff: string
    wagersDetailUrl: string
  }[]
  total: {
    totalCount: number
    totalBetAmount: string
    totalCommissionable: string
    totalPayoff: string
  }
  pagination: {
    currentPage: number
    pageLimit: number
    total: number
    totalPage: number
  }
}

export interface IGetLiveDateRecordParam {
  hall_id: number
  session_id: string
  start_date: string
  end_date: string
  lang?: string
  page?: number
  page_limit?: number
  scheme: string
}

export interface IGetLiveRoundSerialRecordParam {
  hall_id: number
  session_id: string
  roundserial: number
  lang?: string
  page?: number
  page_limit?: number
  scheme: string
}

export interface IGetCasinoDateRecordParam {
  hall_id: number
  session_id: string
  game_id: number
  start_date: string
  end_date: string
  lang?: string
  page?: number
  page_limit?: number
  scheme: string
}

export interface ICasinoDateRecord {
  maintain: boolean
  maintainInfo: string
  searchRange: {
    startDate: string
    endDate: string
  }
  wagersList: {
    id: number
    wagersDate: string
    gameType: number
    betAmount: string
    payoff: string
    jpAmount: string
    wagersDetailUrl: string
  }[]
  total: {
    totalCount: number
    totalBetAmount: string
    totalPayoff: string
    totalJpAmount: string
  }
  pagination: {
    currentPage: number
    pageLimit: number
    total: number
    totalPage: number
  }
}

export interface IGetCasinoComboRecordParam {
  hall_id: number
  session_id: string
  game_id: number
  start_date: string
  end_date: string
  times: number
  operator?: string
  page?: number
  page_limit?: number
}

export interface ICasinoComboRecord {
  maintain: boolean
  maintainInfo: string
  searchRange: {
    startDate: string
    endDate: string
  }
  wagersList: {
    id: number
    wagersDate: string
    gameType: number
    times: number
  }[]
  pagination: {
    currentPage: number
    pageLimit: number
    total: number
    totalPage: number
  }
}

export interface ICasinoRecord {
  totalData: {
    sum: string
    totalcount: string
    totalbet: string
    totalpayoff: string
    totaljackpot: string
  }[]
  allData: {
    platform: string
    portal: number
    wagersDate: string
    roundSerial: number
    gametype: number
    gameId: string
    betAmount: string
    payoff: string
    wagersId: number
    combo?: number
  }[]
  maintain: boolean
  maintainInfo: string
  totalPage: number
  qPage: number
}

export interface IGetFishingRecordParams {
  hall_id: number
  session_id: string
  game_id: number
  start_date: string
  end_date: string
  lang?: string
  page?: number
  page_limit?: number
}

export interface IFishRecord {
  wagersList: {
    id: number
    wagersDate: string
    gameType: number
    betAmount: string
    payoff: string
    wagersDetailUrl: string
  }[]
  total: {
    totalBetAmount: string
    totalPayoff: string
  }
  maintain: boolean
  maintainInfo: string
  pagination: {
    currentPage: number
    pageLimit: number
    total: number
    totalPage: number
  }
}

export interface IGetCardRecordParams {
  hall_id: number
  session_id: string
  game_id: number
  start_date: string
  end_date: string
  lang?: string
  page?: number
  page_limit?: number
}

export interface ICardRecord {
  wagersList: {
    id: number
    wagersDate: string
    gameType: number
    resultStatus: string
    betAmount: string
    payoff: string
    wagersDetailUrl: string
  }[]
  total: {
    totalBetAmount: string
    totalPayoff: string
  }
  maintain: boolean
  maintainInfo: string
  pagination: {
    currentPage: number
    pageLimit: number
    total: number
    totalPage: number
  }
}

export interface IlotteryCompDialog {
  totalData: {
    sum: string
    gold: string
    valid: string
    wingold: string
  }[]
  allData: {
    adddate: string,
    addtime: string,
    wid: string,
    amount: string,
    wtype: string,
    content: string,
    valid_gold: string,
    wingold: string
  }[]
  gameName: {[key: string]: string}
  wType: {
    id: string
    name: string
  }[]
  maintain: boolean
  maintainInfo: string
}

export interface IlotteryUncompDialog {
  totalData: {
    sum: string
    allgold: string
    twinold: string
  }[]
  allData: {
    adddate: string,
    addtime: string,
    wid: string,
    amount: string,
    wtype: string,
    content: string,
    valid_gold: string,
    wingold: string
  }[]
  gameName: {[key: string]: string}
  wType: {
    id: string
    name: string
  }[]
  maintain: boolean
  maintainInfo: string
}

export interface IGetLotteryUncompleteParams {
  session_id: string
  hall_id: number
  lang?: string
}

export interface ILotteryUncomplete {
  list: {
    gameType: string
    gameName: string
    count: number
    betAmount: string
  }[]
  total: {
    totalCount: number
    totalBetAmount: string
  }
  maintain: boolean
  maintainInfo: string
}

export interface IGetLotteryUnCompleteDetailParams {
  hall_id: number
  session_id: string
  lang?: string
  game_id: string
  win_type_id?: string
}

export interface ILotteryUnCompleteDetail {
  winTypeList: {
    id: string
    name: string
  }[]
  wagersList: {
    id: number
    wagersDate: string
    gameType: string
    winType: string
    betDetail: string
    betAmount: string
    maxPayoff: string
  }[]
  total: {
    totalBetAmount: string
    totalMaxPayoff: string
  }
  pagination: {
    currentPage: number
    totalPage: number
  }
  maintain: boolean
  maintainInfo: string
}

export interface IGetLotteryCompleteParams{
  hall_id: number
  session_id: string
}

export interface ILotteryComplete {
  list: {
    roundDate: string
    betAmount: string
    commissionable: string
    payoff: string
  }[]
  total: {
    totalBetAmount: string
    totalCommissionable: string
    totalPayoff: string
  }
  maintain: boolean
  maintainInfo: string
}

export interface IGetLotteryCompleteByGameParams {
  session_id: string
  hall_id: number
  rounddate: string
  lang?: string
}

export interface ILotteryCompleteByGame {
  list: {
    gameType: string
    gameName: string
    betAmount: string
    commissionable: string
    payoff: string
  }[]
  maintain: boolean
  maintainInfo: string
}

export interface IGetLotteryCompleteDetailParams {
  session_id: string
  hall_id: number
  date: string
  game_id: string
  win_type_id?: string
  lang?: string
}

export interface ILotteryCompleteDetail {
  winTypeList: {
    id: string
    name: string
  }[]
  wagersList: {
    id: number
    wagersDate: string
    gameType: string
    winType: string
    betDetail: string
    betAmount: string
    commissionable: string
    payoff: string
  }[]
  total: {
    totalBetAmount: string
    totalCommissionable: string
    totalPayoff: string
  },
  pagination: {
    currentPage: number
    totalPage: number
  }
  maintain: boolean
  maintainInfo: string
}

export interface IGetSportUncompleteParams {
  session_id: string
  hall_id: number
  lang: string
}

export interface ISportUncomplete {
  statisList: {
    id: number
    name: string
    count: number
    betAmount: string
  }[]
  total: {
    count: number
    betAmount: string
  }
  maintain: boolean
  maintainInfo: string
}

export interface IGetSportUnCompleteDetailParams {
  session_id: string
  hall_id: number
  lang: string
  game_id: number
}

export interface ISportUnCompleteDetail {
  gameList: {
    id: number
    name: string
  }[]
  wagers: {
    wagersId: number
    sportName: string
    addDate: string
    oddType: string
    betState: string
    betAmount: string
  }[]
  total: {
    count: number
    betAmount: string
  }
  maintain: boolean
  maintainInfo: string
}

export interface IGetSportCompleteParams {
  session_id: string
  hall_id: number
  lang: string
}

export interface ISportComplete {
  wagers: {
    date: string
    weekday: string
    statisList: {
      id: number
      name: string
      count: number
      betAmount: string
      payoff: string
      commissionable: string
    }[]
    subtotal: {
      betAmount: string
      payoff: string
      commissionable: string
    }
  }[]
  total: {
    betAmount: string
    payoff: string
    commissionable: string
  }
  maintain: boolean
  maintainInfo: string
}

export interface IGetSportCompleteDetailParams {
  session_id: string
  hall_id: number
  lang: string
  date: string
  game_id: number
}

export interface ISportCompleteDetail {
  gameList: {
    id: number
    name: string
  }[]
  wagers: {
    wagersId: number
    sportName: string
    addDate: string
    oddType: string
    betState: string
    betAmount: string
    payoff: string
    commissionable: string
  }[]
  total: {
    betAmount: string
    payoff: string
    commissionable: string
  }
  maintain: boolean
  maintainInfo: string
}

export interface IGetSportCompleteContentParams {
  session_id: string
  hall_id: number
  lang: string
  wagers_id: number
}

export interface ISportCompleteContent {
  content: string
  maintain: boolean
  maintainInfo: string
}

export interface IGetCasinoGameParams {
  session_id: string
  hall_id: number
  lang?: string
}

export interface ICasinoGame {
  searchRange: {
    startDate: string
    endDate: string
  }
  gameList: {
    bbRecord: {
      id: number,
      name: string
    }[]
    bbComboRecord: {
      id: number,
      name: string
    }[]
  }
  maintain: boolean
  maintainInfo: string
}