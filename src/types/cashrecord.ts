import { IMaintainInfoData } from './maintain';

export interface ITransactionRecordNote {
  refId: string
  result: string
}

export interface IPagination {
  page: number
  pageLimit: number
  totalNumber: number
  totalPage: number
}

export interface ITransactionRecord {
  amount: string
  balance: string
  createTime: string
  opCode: number
  note: ITransactionRecordNote
}

export interface ICashRecordData {
  transactionRecord: ITransactionRecord[]
  pagination: IPagination
  total: { amount: string }
  limitDate: string
  maintainInfo: IMaintainInfoData[]
}

export interface IGetCashRecordParams {
  hall_id: number
  session_id: string
  game_kind?: number
  type?: string
  start_date: string
  end_date: string
  sort: string
  page: number
}

export interface ICashSelectData {
  id: number
  name: string
  disabled: boolean
}