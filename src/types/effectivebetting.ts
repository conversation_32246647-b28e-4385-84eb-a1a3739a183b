import { IMaintainInfoData } from "./maintain"

export interface IGameBetList {
  gameKind: number
  bet: number
  payoff: number
}

export interface IEffectiveBetting {
  gameBetList: IGameBetList[]
  betTotal: number
  payoffTotal: number
  isMaintain: boolean
  maintainInfo: IMaintainInfoData[]
}

export interface IGetEffectiveBettingParams {
  session_id: string
  hall_id: number
  start_date: string
  end_date: string
}