export interface GameLimit {
  [key: string]: boolean
}

export interface IGameInfo {
  gameKind: number
  gameId: number
  name: string
  link: string
  demoLink?: string
  icon: string
  externalId?: string
  jpImg?: string
  jpAmount?: number
  nickname?: string
  ruleLink?: string
  isShowJp?: boolean
}

export interface IGameMenu {
  id: number
  sort: number
  name: string
  games: number[]
  gameCount: number
  lower?: IGameMenu[]
}

export interface IGameIntro {
  feature: string[]
  gameId: number
  ratio: string[]
  video: boolean
}

export enum GameType {
  BBlive = 3,
  Lottery = 12,
  Casino = 5,
  Fishhunter = 30,
  BCsport = 31,
  Fishmaster = 38,
  Battle = 66
}

export interface jpData { 
  gameType: string
  poolAmount: number
  jpImg?: string
}

export interface rankData { 
  name: string
  score: number
}

export interface IGetFavoriteGameListParams {
  session_id: string
  hall_id: number
  game_kind: number
}

export interface IFavoriteParams {
  hall_id: number
  session_id: string
  game_kind: number
  game_id: number
}

export interface IGetGameMenuParams {
  session_id: string
  is_mobile?: boolean
  hall_id?: number
  lang: string
}

export interface IGetGameListParams {
  session_id: string
  is_mobile?: boolean
  lang: string
  hall_id?: number
}

export interface IGetGameList {
  gameList: IGameInfo[]
  lang?: string
  lobbyEntry?: string
  jackpotSwitch?: boolean
}

export interface IGetSportLobbyLinkParams {
  session_id?: string
  hall_id: number
  domain_url: string
  lang?: string
  is_mobile: boolean
  enter_page?: string
}

export interface ILobbyLink {
  link: string
  maintain: boolean
  maintainInfo: string
}

export interface IGetCardLobbyLinkParams {
  session_id: string
  hall_id: number
  lang?: string
}