export interface GameNewsData {
  dateTime: string
  content: string
}

export interface ILiveGameNewsData {
  dateTime: string
  content: string,
  gameId: number,
  tableCode: number 
}

export interface GameNews {
  data?: GameNewsData[]
  gameCodeList?: Record<string, string>
  gameTypeList?: Record<string, string>
  dateList?: string[]
  status: string
  message: string
  maintain?: boolean
  maintainInfo?: string
  timeSelect?: string
  gameCodeSelect?: string
  gameTypeSelect?: string
  newsType?: string
}

export interface ILotteryNews {
  data: GameNewsData[],
  maintain: boolean,
  maintainInfo: string
}

export enum ILotteryNewsCategory {
  General = 2,
  System = 3
}
export interface IGetLotteryNewsParams {
  session_id: string
  hall_id: number
  lang: string
  category: number
}

export interface ICasinoNews {
  startTime: string
  data: GameNewsData[],
  maintain: boolean,
  maintainInfo: string
}

export interface IGetCasinoNewsParams {
  session_id: string
  hall_id: number
  lang: string
  date?: string
}

export interface ILiveGameCodeList {
  gameId: { [key: string]: number }
  tableCode: { [key: string]: { [key: string]: string } }
}

export interface IGetLiveGameCodeParams {
  hall_id: number
}

export interface ILiveNews {
  startTime: string
  data: ILiveGameNewsData[],
  maintain: boolean,
  maintainInfo: string
}

export interface IGetLiveNewsParams {
  session_id: string
  hall_id: number
  lang: string
  date?: string
  game_id?: number
  table_code?: number
}