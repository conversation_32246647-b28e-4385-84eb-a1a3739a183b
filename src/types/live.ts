export interface ILiveGameRule {
  id: number,
  name: string
}

export interface ILiveGameMenu {
  id: number,
  name: string
}

export interface ILiveMainSetting {
  platformMenu: ILiveGameMenu[]
  gameRuleList: ILiveGameRule[]
}

export interface IGetLiveLobbyLinkParams {
  hall_id: number,
  session_id: string,
  domain_url: string,
  lang?: string,
  is_mobile: boolean,
  enter_page: string
}

export interface IGetLiveGameListParams {
  hall_id: number,
  lang?: string
}

export interface IGetLiveContentListParams {
  hall_id: number,
  session_id?: string,
  lang: string
}