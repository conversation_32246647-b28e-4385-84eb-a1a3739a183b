export interface IGameMaintain {
  maintain: boolean,
  message: string
}

export interface ILobbySwitch {
 lobbySwitch: {
  [key: string]: boolean
 },
 maintainInfo: {
  live: IGameMaintain,
  casino: IGameMaintain,
  lottery: IGameMaintain,
  sport: IGameMaintain,
  fish: <PERSON>GameMaintain,
  card: IGameMaintain
 }
}

 export interface IGetLobbySwitchParams {
  hall_id: number
  session_id: string
}