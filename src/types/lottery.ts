export interface ILotteryTimer {
  time: string
  status: boolean
}

export interface ILotteryLeaderBoard {
  originProfit: string
  userName: string
  gameName: string
  link: string
}

export interface IGetLotteryListParams {
  session_id?: string
  hall_id: number
  lang: string
  filter?: string
}

export interface ILotteryGamePanel {
  name: string
  openTimestamp: number
  closeTimestamp: number
  link: string
  prevNum: string
  prevResult: {
    key: string
    value: number
  }[]
  resultGroup: string
  ruleLink?: string
}

export interface ILotteryLikeGuess {
  platformName: string
  platformKey: string
  name: string
  link: string
}

export interface ILotteryOfficialGames {
  name: string
  gameId: string
  link: string
  isRecommand?: boolean
}
export interface ILotteryOfficial {
  groupName: string
  groupKey: string
  groupLink: string
  games: ILotteryOfficialGames[]
  gameName: string
  link: string
}

export interface ILotteryTradition {
  num: string
  gameId: string
  name: string
  link: string
  openTimestamp: number
  closeTimestamp: number
  group: string
  tag: string
  ruleLink?: string
}

export interface ILotteryList {
  gamePanel: ILotteryGamePanel
  likeGuess: ILotteryLikeGuess[]
  official: ILotteryOfficial[]
  tradition: ILotteryTradition[]
  officialOn: string
  traditionEntrance: string
  serverTimestamp: number
  timezone: string
  ltCdn: string
  domainPath: string
  leaderboard: ILotteryLeaderBoard[]
}

export interface IGetLotteryLobbyLinkParams {
  hall_id: number,
  session_id: string,
  domain_url: string,
  lang?: string,
  is_mobile: boolean
}

export interface IGetLotteryGameListParams {
  hall_id: number,
  lang?: string,
  list_type: string
}