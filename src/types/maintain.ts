export interface MaintainInfo {
  begin_at_gmt8: string
  end_at_gmt8: string
  begin_at: string
  end_at: string
}

export interface IMaintainInfo {
  maintain: boolean
  message: string
}

export interface IMaintainInfoData {
  gameKind: number
  startTime: string
  endTime: string
  message: string
}

export interface IMaintainInfoTextData {
  game_kind: number
  start_time: string
  end_time: string
  message: string
  isHtml?: boolean
}