export interface maintainInfo { 
  begin_at_gmt8: string
  end_at_gmt8: string
  begin_at: string
  end_at: string
  message: string
}

export interface tableContent {
  Datetime: string
  Contents: string
}

export interface lotteryTableContent {
  datetime: string
  contents: string
}

export interface IGameType {
  id: string
  name: string
}

export interface IGetSportBetInfoParams {
  hall_id: number
  session_id: string
  lang: string,
  category_id: number
}

export interface ISportBetInfo {
  categoryList: {
    id: number
    name: string
  }[]
  gameLimitList: {
    groupName: string
    betLimit: number
    gameLimit: number
  }[]
  maintain: boolean
  maintainInfo: string
}

export interface IBetLimit {
  name: string
  value: number
}

export interface ILiveBetLimit {
  groupName: string
  betLimit: IBetLimit[]
}

export interface IGetLiveBetInfoParams {
  hall_id: number
  session_id: string
  lang: string
}

export interface ILiveBetInfo {
  gameLimitList: {
    name: string
    gamePlayLimitList: {
      limitName: string
      limitValue: number
    }[]
  }[]
  maintain: boolean
  maintainInfo: string
}

export interface IGetLotteryBetInfoParams {
  hall_id: number
  session_id: string
  game_id: string
  lang: string
}

export interface ILotteryBetInfo {
  gameList: {
    id: string
    name: string
  }[]
  gameLimitList: {
    title: string
    singleCredit: number
    singleOdds: number
  }[]
  maintain: boolean
  maintainInfo: string
}

export interface BetRecordParam {
  searchType: string
  STime: string
  ETime: string
  SNum: number
  page: number
}

export interface BetSportUnComDetailParam {
  type: string
}