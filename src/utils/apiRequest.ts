import axios from 'axios'
import { NewApiResponse } from '@/types/api'
import { AxiosResponse } from 'axios'
import router from '@/router'
import i18n from '@/language/i18n'
import { useConfigStore } from '@/stores/config'
import { useCookies } from 'vue3-cookies'
import { ResCode } from '@/types/api'

const configStore = useConfigStore()
const { cookies } = useCookies()
const request = axios.create()

request.interceptors.request.use(
  (config) => {
    if (configStore.apiUrl) {
      config.baseURL = configStore.apiUrl
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

request.interceptors.response.use(
  (response: AxiosResponse<any, any>): Promise<AxiosResponse<any, any>> => {
    if (!response.data) {
      return Promise.reject(new Error(i18n.global.t('S_SYSTEM_ERROR')))
    }

    const responseData = response.data as NewApiResponse

    if (responseData.code !== 0) {
      // 廳維護
      if (responseData.code == ResCode.Hall_Maintain) {
        router.push('/upup')
      }

      // 遊戲維護
      if (responseData.code == ResCode.Game_Maintain) {
        return Promise.resolve(response)
      }

      // session id 失敗
      if (responseData.code == ResCode.SessionID_Failed) {
        cookies.set('SESSION_ID', '')
        window.location.reload()
      }

      let errorMessage = response.data.message

      if (responseData.code) {
        // session id 失敗
        if (responseData.code == ResCode.SessionID_Failed) errorMessage = i18n.global.t('S_LOGOUT2')
        // 帳號停權
        if (responseData.code == ResCode.Account_Suspended) errorMessage = i18n.global.t('S_POWER_STOP')

        errorMessage += '(' + response.data.code + ')'
      }

      throw new Error(errorMessage || i18n.global.t('S_SYSTEM_ERROR'))
    }

    return Promise.resolve(response)
  },
  (error) => {
    return Promise.reject(new Error(error.message || i18n.global.t('S_SYSTEM_ERROR')))
  }
)

export default request
