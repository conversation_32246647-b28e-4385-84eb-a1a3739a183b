import { describe, expect, it } from 'vitest'
import {
  getTodayDate,
  getYesterdayDate,
  getDayBefore,
  getTodayRange,
  getYesterdayRange,
  getDayBeforeRange,
  getTodayWithFormat
} from '@/utils/date'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

dayjs.extend(timezone)
dayjs.extend(utc)

describe('date utils', () => {
  it('should get today date in YYYY-MM-DD format', () => {
    const today = getTodayDate()
    expect(today).toMatch(/^\d{4}-\d{2}-\d{2}$/)
  })

  it('should get yesterday date in YYYY-MM-DD format', () => {
    const yesterday = getYesterdayDate()
    expect(yesterday).toMatch(/^\d{4}-\d{2}-\d{2}$/)
  })

  it('should get day before specified days', () => {
    const dayBefore = getDayBefore(3)
    expect(dayBefore).toMatch(/^\d{4}-\d{2}-\d{2}$/)
  })

  it('should get today range', () => {
    const [start, end] = getTodayRange()
    expect(start).toEqual(end)
    expect(start).toMatch(/^\d{4}-\d{2}-\d{2}$/)
  })

  it('should get yesterday range', () => {
    const [start, end] = getYesterdayRange()
    expect(start).toEqual(end)
    expect(start).toMatch(/^\d{4}-\d{2}-\d{2}$/)
  })

  it('should get day before range', () => {
    const [start, end] = getDayBeforeRange(3)
    expect(start).toMatch(/^\d{4}-\d{2}-\d{2}$/)
    expect(end).toMatch(/^\d{4}-\d{2}-\d{2}$/)
  })

  it('should get today with custom format', () => {
    const today = getTodayWithFormat('YYYY/MM/DD HH:mm:ss')
    expect(today).toMatch(/^\d{4}\/\d{2}\/\d{2} \d{2}:\d{2}:\d{2}$/)
  })
})