import dayjs from 'dayjs'

export const getTodayDate = () => {
  return dayjs.tz(new Date(), 'Etc/GMT+4').format('YYYY-MM-DD')
}

export const getYesterdayDate = () => {
  return dayjs.tz(new Date(), 'Etc/GMT+4').subtract(1, 'day').format('YYYY-MM-DD')
}

export const getDayBefore = (days: number) => {
  return dayjs.tz(new Date(), 'Etc/GMT+4').subtract(days, 'day').format('YYYY-MM-DD')
}

export const getTodayRange = () => {
  return [getTodayDate(), getTodayDate()]
}

export const getYesterdayRange = () => {
  return [getYesterdayDate(), getYesterdayDate()]
}

export const getDayBeforeRange = (days: number) => {
  return [getDayBefore(days), getTodayDate()]
}

export const getTodayWithFormat = (format: string) => {
  return dayjs.tz(new Date(), 'Etc/GMT+4').format(format)
}
