import { IMaintainInfoTextData } from '@/types/maintain';
import dayjs from 'dayjs'

export const maintainText = (maintainInfo : IMaintainInfoTextData = { 
  game_kind: -1,
  start_time: '',
  end_time: '',
  message: ''
}) : string => {
  const {
    message,
    isHtml = true
  } = maintainInfo
  
  if (maintainInfo.start_time && maintainInfo.end_time) {
    const start_time = dayjs(maintainInfo.start_time).tz('Etc/GMT+4').format('YYYY-MM-DD HH:mm:ss')
    const end_time = dayjs(maintainInfo.end_time).tz('Etc/GMT+4').format('YYYY-MM-DD HH:mm:ss')

    return isHtml ?
    `
      <div>
        ${start_time} - ${end_time} <br />
        ${message}
      </div>
    `
    :
    `
      ${start_time} - ${end_time}
      ${message}
    `
  } else {
    return ''
  }
}