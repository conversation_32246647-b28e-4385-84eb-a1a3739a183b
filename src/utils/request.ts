import axios from 'axios'
import { ApiResponse } from '@/types/api'
import { AxiosResponse } from 'axios'
import router from '@/router'
import i18n from '@/language/i18n'
import { useConfigStore } from '@/stores/config'

const configStore = useConfigStore()
const request = axios.create()

request.interceptors.request.use(
  (config) => {
    config.headers['X-Requested-With'] = 'XMLHttpRequest';

    if (configStore.apiUrl) {
      config.baseURL = configStore.apiUrl
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

request.interceptors.response.use(
  (response: AxiosResponse<any, any>): Promise<AxiosResponse<any, any>> => {
    if (!response.data) {
      return Promise.reject(new Error(i18n.global.t('S_SYSTEM_ERROR')))
    }

    const responseData = response.data as ApiResponse

    if (responseData.status !== 'Y') {
      let errorMessage = response.data.message

      if (responseData.code) {
        errorMessage += '(' + response.data.code + ')'
      }

      // 廳維護
      if (responseData.code == '111031204') {
        router.push('/upup')
        errorMessage = responseData.data.UPUP_CONTENT
      }

      throw new Error(errorMessage || i18n.global.t('S_SYSTEM_ERROR'))
    }

    return Promise.resolve(response)
  },
  (error) => {
    return Promise.reject(new Error(error.message || i18n.global.t('S_SYSTEM_ERROR')))
  }
)

export default request
