import axios from 'axios'
import { describe, it, expect, vi } from 'vitest'
import { testApiEndpoints } from './testApiEndpoints'

vi.mock('axios')

vi.mock('@/language/i18n', () => ({
  default: {
    global: {
      t: (key: string) => key
    }
  }
}))


describe('testApiEndpoints utils', () => {
  it('should return link', async () => {
    const axiosGet = vi.mocked(axios.get)
    axiosGet
      .mockRejectedValueOnce(new Error('fail a.com'))
      .mockResolvedValueOnce({ data: 'ok' })

    const res = await testApiEndpoints({
      domain: ['a.com', 'b.com'],
      link: '/test.json'
    })
    expect(res).toEqual({
      domain: 'https://b.com',
      domainPath: 'b.com',
      url: 'https://b.com/test.json',
    })
  })

  it('should throw error if all domain fails', async () => {
    const axiosGet = vi.mocked(axios.get)
    axiosGet
      .mockRejectedValueOnce(new Error('fail a.com'))
      .mockRejectedValueOnce(new Error('fail b.com'))

    await expect(testApiEndpoints({
      domain: ['https://a.com', 'https://b.com'],
      link: '/test.json'
    })).rejects.toThrow('S_SYSTEM_ERROR')
  })
})