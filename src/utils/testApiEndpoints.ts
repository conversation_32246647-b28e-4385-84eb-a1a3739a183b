import axios from 'axios'
import i18n from '@/language/i18n'
import { GameType } from "@/types/game"

const speedPath = {
  [GameType.BBlive]: '/ipl/speed-test.php',
  [GameType.Casino]: ' /ipl/speed-test.php',
  [GameType.Lottery]: '/balv/bv/fathom',
  [GameType.Fishhunter]: '/ipl/speed-test.php',
  [GameType.Fishmaster]: '/elibom5/speed.php',
  [GameType.Battle]: '/games/speed.php',
}

type SpeedPathNumeric = keyof typeof speedPath;

export const testApiEndpoints = async ({ domain = [], link = '', gameType } : { domain: string[], link: string, gameType?: number }) => {
  const gameSpeedPath = speedPath[gameType as SpeedPathNumeric] || ''
  const TEST_TIMEOUT = 3000
  let selDomain = ''
  let selDomainPath = ''
  let result = ''

  try {
    await Promise.any(
      domain.map(async (domainUrl: string) => {
        const url = `https://${domainUrl}`

        try {
          await axios.get(`${url}${gameSpeedPath}`, { timeout: TEST_TIMEOUT })
          selDomain = url
          selDomainPath = domainUrl
          result = `${url}${link}`
          return result
        } catch (error) {
          if ((error as any).response) {
            selDomain = url
            selDomainPath = domainUrl
            result = `${url}${link}`
            return result
          }
          throw new Error(`Failed to connect to ${url}`)
        }
      })
    )
  } catch {
    // 所有測試都失敗
    return Promise.reject(new Error(i18n.global.t('S_SYSTEM_ERROR')))
  }

  return { domain: selDomain, url: result, domainPath: selDomainPath }
}