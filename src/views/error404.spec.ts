import { mount } from '@vue/test-utils'
import { describe, expect, it } from 'vitest'
import { createI18n } from 'vue-i18n'
import Error404 from '@/views/error404.vue'

const i18n = createI18n({
  locale: 'en',
  messages: {
    en: {
      '404Error': 'Page not found',
    },
  },
})

describe('Error404', () => {
  it('renders correctly', () => {
    const wrapper = mount(Error404, {
      global: {
        plugins: [i18n],
      },
    })

    expect(wrapper.text()).toContain('Page not found')

    const img = wrapper.find('img')
    expect(img.exists()).toBe(true)
    expect(img.attributes('src')).toBe('/client/static/image/common/loading.gif')
  })
})
