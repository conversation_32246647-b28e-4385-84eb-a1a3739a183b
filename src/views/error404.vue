<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
</script>

<template>
  <div class="error-container">
    <p>
      {{ t('404Error') }}
    </p>
    <div class="loading-mask">
      <img :src="'/client/static/image/common/loading.gif'" alt="">
    </div>
  </div>
</template>

<style lang="scss" scoped>
.error-container {
  height: 100%;
  text-align: center;
  background: #fff;
  font-size: 1.5rem;
  font-family: 'Microsoft JhengHei', '微軟正黑體', arial, helvetica,
  sans-serif;
  color: #aaa;
  padding: 20px;
}

.loading-mask {
  position: relative;
  width: 200px;
  height: 200px;
  margin: 250px auto;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  img {
    position: absolute;
    display: block;
    width: 80px;
    height: 80px;
    top: 50%;
    left: 50%;
    margin: -40px 0 0 -40px;
  }
}
</style>
