import { mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import { nextTick } from 'vue'
import { useLobbySwitchStore } from '@/stores/lobbySwitch'
import { GameType } from '@/types/game'
import Bet from '@/views/mcenter/bet.vue'

vi.mock('@/stores/lobbySwitch', () => ({
  useLobbySwitchStore: vi.fn()
}))

vi.mock('@/components/mcenter/GameMaintainTooltip.vue', () => ({
  default: { template: '<div class="mock-maintain-tooltip"></div>' }
}))

vi.mock('@/components/betrecord/LiveIndex.vue', () => ({
  default: { template: '<div class="mock-betrecord-live"></div>' }
}))

vi.mock('@/components/betrecord/CasinoIndex.vue', () => ({
  default: { template: '<div class="mock-betrecord-casino"></div>' }
}))

vi.mock('@/components/betrecord/LotteryIndex.vue', () => ({
  default: { template: '<div class="mock-betrecord-lottery"></div>' }
}))

vi.mock('@/components/betrecord/SportIndex.vue', () => ({
  default: { template: '<div class="mock-betrecord-sport"></div>' }
}))

vi.mock('@/components/betrecord/FishingIndex.vue', () => ({
  default: { template: '<div class="mock-betrecord-fishing"></div>' }
}))

vi.mock('@/components/betrecord/CardIndex.vue', () => ({
  default: { template: '<div class="mock-betrecord-card"></div>' }
}))

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      'S_BB_LIVE': 'BB Live',
      'S_BBCASINO': 'BB Casino',
      'S_GAME_GROUP_BY_KENO': 'BB Lottery',
      'S_GAME_GROUP_BY_NBB': 'BB Sport',
      'S_BB_FISHING': 'BB Fish',
      'S_BBCARD': 'BB Battle'
    },
  },
})

const defaultGameMaintain = {
  maintain: false,
  message: ''
}

const maintainInfo = {
  live: { ...defaultGameMaintain },
  casino: { ...defaultGameMaintain },
  lottery: { ...defaultGameMaintain },
  sport: { ...defaultGameMaintain },
  fish: { ...defaultGameMaintain },
  card: { ...defaultGameMaintain }
}

describe('Bet Component', () => {
  it('renders all game categories correctly', async () => {
    vi.mocked(useLobbySwitchStore).mockReturnValue({
      lobbySwitch: ref({
        [GameType.BBlive]: true,
        [GameType.Casino]: true,
        [GameType.Lottery]: false,
        [GameType.BCsport]: true,
        [GameType.Fishmaster]: true,
        [GameType.Battle]: true
      }),
      maintainInfo: ref(JSON.parse(JSON.stringify(maintainInfo)))
    } as any)
    
    const wrapper = mount(Bet, {
      global: {
        plugins: [i18n],
      }
    })

    const collapseItems = wrapper.findAll('.el-collapse-item')
    expect(collapseItems.length).toBe(6)
  })

  it('renders all game categories correctly without gameSwitch', async () => {
    vi.mocked(useLobbySwitchStore).mockReturnValue({
      lobbySwitch: ref({}),
      maintainInfo: ref(JSON.parse(JSON.stringify(maintainInfo)))
    } as any)

    const wrapper = mount(Bet, {
      global: {
        plugins: [i18n],
      }
    })

    const vm = wrapper.vm as any;

    expect(vm.lobbySwitch).toEqual({})
  })

  it('displays maintenance icon when maintenance is active', async () => {
    vi.mocked(useLobbySwitchStore).mockReturnValue({
      lobbySwitch: ref({
        [GameType.BBlive]: true,
        [GameType.Casino]: true,
        [GameType.Lottery]: false,
        [GameType.BCsport]: true,
        [GameType.Fishmaster]: true,
        [GameType.Battle]: true
      }),
      maintainInfo: ref({
        ...JSON.parse(JSON.stringify(maintainInfo)),
        live: { maintain: true, message: 'Maintenance in progress' }
      })
    } as any)

    const wrapper = mount(Bet, {
      global: {
        plugins: [i18n],
      }
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    const maintainIcons = wrapper.findAll('.mock-maintain-tooltip')
    expect(maintainIcons.length).toBe(1)
  })

  it('controls content visibility based on gameSwitch', async () => {
    vi.mocked(useLobbySwitchStore).mockReturnValue({
      lobbySwitch: ref({
        [GameType.BBlive]: true,
        [GameType.Casino]: true,
        [GameType.Lottery]: true,
        [GameType.BCsport]: true,
        [GameType.Fishmaster]: true,
        [GameType.Battle]: true
      }),
      maintainInfo: ref(JSON.parse(JSON.stringify(maintainInfo)))
    } as any)

    const wrapper = mount(Bet, {
      global: {
        plugins: [i18n],
      }
    })

    expect(wrapper.find('.mock-betrecord-live').exists()).toBe(false)
    expect(wrapper.find('.mock-betrecord-casino').exists()).toBe(false)
    expect(wrapper.find('.mock-betrecord-lottery').exists()).toBe(false)
    expect(wrapper.find('.mock-betrecord-sport').exists()).toBe(false)
    expect(wrapper.find('.mock-betrecord-fishing').exists()).toBe(false)
    expect(wrapper.find('.mock-betrecord-card').exists()).toBe(false)
    
    const vm = wrapper.vm as any;
    const elCollapseItems = wrapper.findAll('.el-collapse-item')

    vm.activeCollapseNames = 'live';
    await elCollapseItems[0].trigger('click')
    expect(wrapper.find('.mock-betrecord-live').exists()).toBe(true)

    vm.activeCollapseNames = 'casino';
    await elCollapseItems[1].trigger('click')
    expect(wrapper.find('.mock-betrecord-casino').exists()).toBe(true)

    vm.activeCollapseNames = 'lottery';
    await elCollapseItems[2].trigger('click')
    expect(wrapper.find('.mock-betrecord-lottery').exists()).toBe(true)

    vm.activeCollapseNames = 'sport';
    await elCollapseItems[3].trigger('click')
    expect(wrapper.find('.mock-betrecord-sport').exists()).toBe(true)

    vm.activeCollapseNames = 'fishing';
    await elCollapseItems[4].trigger('click')
    expect(wrapper.find('.mock-betrecord-fishing').exists()).toBe(true)

    vm.activeCollapseNames = 'card';
    await elCollapseItems[5].trigger('click')
    expect(wrapper.find('.mock-betrecord-card').exists()).toBe(true)
  })
})
