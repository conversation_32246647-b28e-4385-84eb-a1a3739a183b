<script setup lang="ts">
import { useLobbySwitchStore } from '@/stores/lobbySwitch'
import { useI18n } from 'vue-i18n'
import { GameType } from '@/types/game'
import GameMaintainTooltip from '@/components/mcenter/GameMaintainTooltip.vue'
import { storeToRefs } from 'pinia'

const { t } = useI18n()
const lobbySwitchStore = useLobbySwitchStore()
const { lobbySwitch, maintainInfo } = storeToRefs(lobbySwitchStore)
const activeCollapseNames = ref('')
</script>

<template>
  <div class="bet-container">
    <el-collapse v-model="activeCollapseNames" accordion >
      <!-- Live  -->
      <el-collapse-item
        :title="t('S_BB_LIVE')"
        name="live"
        :disabled="maintainInfo.live && maintainInfo.live.maintain"
      >
        <template v-if="maintainInfo.live && maintainInfo.live.maintain" #icon>
          <GameMaintainTooltip
            :maintain-message="maintainInfo.live.message"
          />
        </template>
        <BetrecordLiveIndex
          v-if="lobbySwitch[GameType.BBlive] && activeCollapseNames === 'live'"
        />
      </el-collapse-item>
      <!-- Casino -->
      <el-collapse-item
        :title="t('S_BBCASINO')"
        name="casino"
        :disabled="maintainInfo.casino && maintainInfo.casino.maintain"
      >
        <template v-if="maintainInfo.casino && maintainInfo.casino.maintain" #icon>
          <GameMaintainTooltip
            :maintain-message="maintainInfo.casino.message"
          />
        </template>
        <BetrecordCasinoIndex
          v-if="lobbySwitch[GameType.Casino] && activeCollapseNames === 'casino'"
        />
      </el-collapse-item>
      <!-- Lottery -->
      <el-collapse-item
        :title="t('S_GAME_GROUP_BY_KENO')"
        name="lottery"
        :disabled="maintainInfo.lottery && maintainInfo.lottery.maintain"
      >
        <template v-if="maintainInfo.lottery && maintainInfo.lottery.maintain" #icon>
          <GameMaintainTooltip
            :maintain-message="maintainInfo.lottery.message"
          />
        </template>
        <BetrecordLotteryIndex
          v-if="lobbySwitch[GameType.Lottery] && activeCollapseNames === 'lottery'"
        />
      </el-collapse-item>
      <!-- Sport  -->
      <el-collapse-item
        :title="t('S_GAME_GROUP_BY_NBB')"
        name="sport"
        :disabled="maintainInfo.sport && maintainInfo.sport.maintain"
      >
        <template v-if="maintainInfo.sport && maintainInfo.sport.maintain" #icon>
          <GameMaintainTooltip
            :maintain-message="maintainInfo.sport.message"
          />
        </template>
        <BetrecordSportIndex
          v-if="lobbySwitch[GameType.BCsport] && activeCollapseNames === 'sport'"
          />
      </el-collapse-item>
      <!-- Fishing  -->
      <el-collapse-item
        :title="t('S_BB_FISHING')"
        name="fishing"
        :disabled="maintainInfo.fish && maintainInfo.fish.maintain"
      >
        <template v-if="maintainInfo.fish && maintainInfo.fish.maintain" #icon>
          <GameMaintainTooltip
            :maintain-message="maintainInfo.fish.message"
          />
        </template>
        <BetrecordFishingIndex
          v-if="lobbySwitch[GameType.Fishmaster] && activeCollapseNames === 'fishing'"
        />
      </el-collapse-item>
      <!-- Card  -->
      <el-collapse-item
        :title="t('S_BBCARD')"
        name="card"
        :disabled="maintainInfo.card && maintainInfo.card.maintain"
      >
        <template v-if="maintainInfo.card && maintainInfo.card.maintain" #icon>
          <GameMaintainTooltip
            :maintain-message="maintainInfo.card.message"
          />
        </template>
        <BetrecordCardIndex
          v-if="lobbySwitch[GameType.Battle] && activeCollapseNames === 'card'"
        />
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<style lang="scss" scoped>
.bet-container {
  padding: 20px;

  :deep(.el-collapse-item__header) {
    font-size: 16px;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    color: var(--mcenter-collapse-item-text);
  }

  .search-bar {
    display: flex;
    justify-content: center;
  }

  .result-content {
    width: 100%;
    height: 500px;
  }
}
</style>
