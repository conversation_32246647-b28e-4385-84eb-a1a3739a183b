import { mount } from '@vue/test-utils'
import { describe, it, expect, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import { useLobbySwitchStore } from '@/stores/lobbySwitch'
import { GameType } from '@/types/game'
import Betinfo from '@/views/mcenter/betinfo.vue'

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      'S_BB_LIVE': 'BB Live',
      'S_GAME_GROUP_BY_KENO': 'BB Lottery',
      'S_GAME_GROUP_BY_NBB': 'BB Sport',
    },
  },
})

vi.mock('@/stores/lobbySwitch', () => ({
  useLobbySwitchStore: vi.fn()
}))

vi.mock('@/components/mcenter/GameMaintainTooltip.vue', () => ({
  default: { template: '<div class="mock-maintain-tooltip"></div>' }
}))

vi.mock('@/components/betinfo/live.vue', () => ({
  default: { template: '<div class="mock-betinfo-live"></div>' }
}))

vi.mock('@/components/betinfo/bbsport.vue', () => ({
  default: { template: '<div class="mock-betinfo-bbsport"></div>' }
}))

vi.mock('@/components/betinfo/lottery.vue', () => ({
  default: { template: '<div class="mock-betinfo-lottery"></div>' }
}))

const defaultGameMaintain = {
  maintain: false,
  message: ''
}

const maintainInfo = {
  live: { ...defaultGameMaintain },
  casino: { ...defaultGameMaintain },
  lottery: { ...defaultGameMaintain },
  sport: { ...defaultGameMaintain },
  fish: { ...defaultGameMaintain },
  card: { ...defaultGameMaintain }
}

describe('Betinfo.vue', () => {
  it('renders component correctly', async () => {
    vi.mocked(useLobbySwitchStore).mockReturnValue({
      lobbySwitch: ref({
        [GameType.BBlive]: true,
        [GameType.Casino]: true,
        [GameType.Lottery]: false,
        [GameType.BCsport]: true,
        [GameType.Fishmaster]: true,
        [GameType.Battle]: true
      }),
      maintainInfo: ref(JSON.parse(JSON.stringify(maintainInfo)))
    } as any)

    const wrapper = mount(Betinfo, {
      global: {
        plugins: [i18n]
      }
    })
    expect(wrapper.find('.betinfo-container').exists()).toBe(true)
  })

  it('renders all game categories correctly without gameSwitch', async () => {
    vi.mocked(useLobbySwitchStore).mockReturnValue({
      lobbySwitch: ref({}),
      maintainInfo: ref(JSON.parse(JSON.stringify(maintainInfo)))
    } as any)

    const wrapper = mount(Betinfo, {
      global: {
        plugins: [i18n],
      }
    })

    const vm = wrapper.vm as any;

    expect(vm.lobbySwitch).toEqual({})
  })

  it('displays correct game component based on gameSwitch', async () => {
    vi.mocked(useLobbySwitchStore).mockReturnValue({
      lobbySwitch: ref({
        [GameType.BBlive]: true,
        [GameType.Casino]: true,
        [GameType.Lottery]: true,
        [GameType.BCsport]: true,
        [GameType.Fishmaster]: true,
        [GameType.Battle]: true
      }),
      maintainInfo: ref(JSON.parse(JSON.stringify(maintainInfo)))
    } as any)

    const wrapper = mount(Betinfo, {
      global: {
        plugins: [i18n]
      }
    })

    expect(wrapper.find('.mock-betinfo-live').exists()).toBe(false)
    expect(wrapper.find('.mock-betinfo-lottery').exists()).toBe(false)
    expect(wrapper.find('.mock-betinfo-bbsport').exists()).toBe(false)

    const vm = wrapper.vm as any;
    const elCollapseItems = wrapper.findAll('.el-collapse-item')

    vm.activeNames = 'live';
    await elCollapseItems[0].trigger('click')
    expect(wrapper.find('.mock-betinfo-live').exists()).toBe(true)

    vm.activeNames = 'lottery';
    await elCollapseItems[1].trigger('click')
    expect(wrapper.find('.mock-betinfo-lottery').exists()).toBe(true)

    vm.activeNames = 'bbsport';
    await elCollapseItems[2].trigger('click')
    expect(wrapper.find('.mock-betinfo-bbsport').exists()).toBe(true)
  })

  it('disables game tab when under maintenance', async () => {
    vi.mocked(useLobbySwitchStore).mockReturnValue({
      lobbySwitch: ref({
        [GameType.BBlive]: false,
        [GameType.Casino]: true,
        [GameType.Lottery]: true,
        [GameType.BCsport]: true,
        [GameType.Fishmaster]: true,
        [GameType.Battle]: true
      }),
      maintainInfo: ref({
        ...JSON.parse(JSON.stringify(maintainInfo)),
        live: { maintain: true, message: 'Maintenance in progress' }
      })
    } as any)

    const wrapper = mount(Betinfo, {
      global: {
        plugins: [i18n]
      }
    })

    await wrapper.vm.$nextTick()
    await nextTick()

    const liveCollapseItem = wrapper.findComponent({ name: 'el-collapse-item' })
    expect(liveCollapseItem.props('disabled')).toBeTruthy()
  })
})