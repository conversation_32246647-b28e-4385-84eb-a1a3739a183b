<script setup lang="ts">
import { useLobbySwitchStore } from '@/stores/lobbySwitch'
import { GameType } from '@/types/game'
import { useI18n } from 'vue-i18n'
import { storeToRefs } from 'pinia'
import GameMaintainTooltip from '@/components/mcenter/GameMaintainTooltip.vue'

const { t } = useI18n()
const lobbySwitchStore = useLobbySwitchStore()
const { lobbySwitch, maintainInfo } = storeToRefs(lobbySwitchStore)
const activeNames = ref('')
</script>

<template>
  <div class="betinfo-container">
    <el-collapse v-model="activeNames" accordion>
      <!-- Live  -->
      <el-collapse-item
        :title="t('S_BB_LIVE')"
        name="live"
        :disabled="maintainInfo.live && maintainInfo.live.maintain"
      >
        <template v-if="maintainInfo.live && maintainInfo.live.maintain" #icon>
          <GameMaintainTooltip
            :maintain-message="maintainInfo.live.message"
          />
        </template>
        <BetinfoLive
          v-if="lobbySwitch[GameType.BBlive] && activeNames === 'live'"
        />
      </el-collapse-item>
      <!-- Sport  -->
      <el-collapse-item
        :title="t('S_GAME_GROUP_BY_NBB')"
        name="bbsport"
        :disabled="maintainInfo.sport && maintainInfo.sport.maintain"
      >
        <template v-if="maintainInfo.sport && maintainInfo.sport.maintain" #icon>
          <GameMaintainTooltip
            :maintain-message="maintainInfo.sport.message"
          />
        </template>
        <BetinfoBbsport
          v-if="lobbySwitch[GameType.BCsport] && activeNames === 'bbsport'"
        />
      </el-collapse-item>
      <!-- Lottery  -->
      <el-collapse-item
        :title="t('S_GAME_GROUP_BY_KENO')"
        name="lottery"
        :disabled="maintainInfo.lottery && maintainInfo.lottery.maintain"
      >
        <template v-if="maintainInfo.lottery && maintainInfo.lottery.maintain" #icon>
          <GameMaintainTooltip
            :maintain-message="maintainInfo.lottery.message"
          />
        </template>
        <BetinfoLottery
          v-if="lobbySwitch[GameType.Lottery] && activeNames === 'lottery'"
        />
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<style lang="scss" scoped>
.betinfo-container {
  padding: 50px;

  :deep(.el-collapse-item__header) {
    font-size: 16px;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
  }
}

.table-container {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;

  .el-table {
    margin: 15px;

    :deep(.el-table__cell) > .cell {
      text-align: center;
    }
  }
}

.el-select {
  width: 30%;
}

:deep(.el-collapse-item__header) {
  color: var(--mcenter-collapse-item-text);
}
</style>
