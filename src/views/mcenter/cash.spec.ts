import { mount } from '@vue/test-utils'
import { afterEach, describe, expect, it, vi } from 'vitest'
import { getCashRecord } from '@/api/cashrecord'
import Cash from '@/views/mcenter/cash.vue'
import { useAccountInfoStore } from '@/stores/account'

vi.mock('@/api/cashrecord', () => ({
  getCashRecord: vi.fn(),
}))

vi.mock('vue-i18n', () => ({
  useI18n: vi.fn(() => ({
    t: vi.fn((msg: string) => msg),
  })),
}));

vi.mock('vue3-cookies', () => ({
  useCookies: vi.fn().mockReturnValue({
    cookies: {
      get: (key: string) => {
        if (key === 'SESSION_ID') return 'session_id'
        return null
      }
    }
  }),
}))

vi.mock('@/stores/account', () => ({
  useAccountInfoStore: vi.fn()
}))

vi.mocked(useAccountInfoStore).mockReturnValue({
  accountInfo: ref({
    alias: 'testUser',
    balance: 100,
    bankrupt: false,
    block: false,
    currency: 'CNY',
    enable: true,
    test: false,
    username: 'testUser'
  })
} as any)

const mockDate = '2024/12/11 - 10:00:00';

vi.mock('@/utils/date', () => ({
  getTodayRange: vi.fn(() => [mockDate, mockDate]),
}))

vi.mock('@/composables/useImportTheme', () => ({
  useImportTheme: vi.fn(() => ({
    importStyle: vi.fn(),
  })),
}))

vi.mock('@/components/mcenter/PaginationComponent.vue', () => ({
  default: {
    name: 'PaginationComponent',
    template: '<div class="mock-pagination-component">Mock PaginationComponent</div>',
  },
}))

vi.mock('@/components/cash/CashSearch.vue', () => ({
  default: {
    name: 'CashSearch',
    template: '<div class="mock-cash-search">Mock CashSearch</div>',
  },
}))

vi.mock('@/components/cash/CashTable.vue', () => ({
  default: {
    name: 'CashTable',
    template: '<div class="mock-cash-table">Mock CashTable</div>',
  },
}))

const mockCashRecord = {
  transactionRecord: [
    {
      amount: '-50.00',
      balance: '2010258461.68',
      createTime: '2024-11-08 01:44:31',
      opCode: 40000,
      note: {
        refId: '520000093878',
        result: ''
      }
    },
    {
      amount: '-50.00',
      balance: '2010258511.68',
      createTime: '2024-11-08 01:44:13',
      opCode: 40000,
      note: {
        refId: '520000093877',
        result: ''
      }
    }
  ],
  pagination: {
    page: 1,
    pageLimit: 30,
    totalNumber: 2,
    totalPage: 1
  },
  total: {
    amount: '-100.00'
  },
  limitDate: '2021-08-01',
  maintainInfo: [
    {
      gameKind: 38,
      startTime: '2024-11-20 22:40:00',
      endTime: '2024-11-20 22:45:00',
      message: 'hanna test'
    }
  ]
}

describe('Cash', () => {
  afterEach(() => {
    vi.clearAllMocks()
  })

  it('renders correctly', async () => {
    vi.mocked(getCashRecord).mockResolvedValue(mockCashRecord)

    const wrapper = mount(Cash)
    await wrapper.vm.$nextTick()
    expect(wrapper.html()).toContain('Mock CashSearch')
    expect(wrapper.html()).toContain('Mock CashTable')
    expect(wrapper.html()).toContain('Mock PaginationComponent')

    expect(getCashRecord).toHaveBeenCalledWith({
      hall_id: 0,
      session_id: 'session_id',
      game_kind: undefined,
      type: undefined,
      start_date: '2024/12/11 - 10:00:00',
      end_date: '2024/12/11 - 10:00:00',
      sort: 'desc',
      page: 1
    })
  })

  it('handles search', async () => {
    vi.mocked(getCashRecord).mockResolvedValue(mockCashRecord)
    const wrapper = mount(Cash)
    const wrapperInstance = wrapper.vm as any
    wrapperInstance.gameKindVal = 38

    await wrapper.vm.$nextTick()

    const search = wrapper.findComponent({ name: 'CashSearch' })
    const searchInstance = search.vm as any
    await searchInstance.$emit('search')

    expect(getCashRecord).toHaveBeenLastCalledWith({
      hall_id: 0,
      session_id: 'session_id',
      game_kind: 38,
      type: undefined,
      start_date:  '2024/12/11 - 10:00:00',
      end_date: '2024/12/11 - 10:00:00',
      sort: 'desc',
      page: 1
    })

    expect(wrapperInstance.topTableData).toEqual([{
      sum: 'S_TOTAL',
      amount: '-100.00',
      balance: '100',
    }])

    expect(wrapperInstance.tableData).toEqual([
      {
        amount: '-50.00',
        balance: '2010258461.68',
        createTime: '2024-11-08 01:44:31',
        opCode: 40000,
        note: {
          refId: '520000093878',
          result: ''
        }
      },
      {
        amount: '-50.00',
        balance: '2010258511.68',
        createTime: '2024-11-08 01:44:13',
        opCode: 40000,
        note: {
          refId: '520000093877',
          result: ''
        }
      }
    ])
  })

  it('handles error', async () => {
    const mockError = new Error('Failed to fetch cash record data')
    vi.mocked(getCashRecord).mockRejectedValue(mockError)
    vi.spyOn(console, 'log').mockImplementation(vi.fn())
    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())

    const wrapper = mount(Cash)
    await wrapper.vm.$nextTick()

    expect(console.log).toHaveBeenCalledWith(mockError)
    expect(ElMessage.error).toHaveBeenCalledWith(mockError.message)
  })
})
