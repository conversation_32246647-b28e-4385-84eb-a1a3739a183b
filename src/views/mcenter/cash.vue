<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { getCashRecord } from '@/api/cashrecord'
import { useImportTheme } from '@/composables/useImportTheme'
import PaginationComponent from '@/components/mcenter/PaginationComponent.vue'
import { getTodayRange } from '@/utils/date'
import { ICashRecordData, ITransactionRecord } from '@/types/cashrecord'
import CashSearch from '@/components/cash/CashSearch.vue'
import CashTable from '@/components/cash/CashTable.vue'
import { useAccountInfoStore } from '@/stores/account'
import { storeToRefs } from 'pinia'
import { useCookies } from 'vue3-cookies'

const { t } = useI18n()
const { cookies } = useCookies()
const accountInfoStore = useAccountInfoStore()
const { accountInfo } = storeToRefs(accountInfoStore)
const dateVal = ref<any[]>(getTodayRange())
const gameKindVal = ref(0)
const typeVal = ref('ALL')
const sortVal = ref('desc')
const topTableData = ref([
  {
    sum: t('S_TOTAL'),
    amount: '--',
    balance: '--'
  }
])
const tableData = ref<ITransactionRecord[]>([])
const qPage = ref(1)
const totalPage = ref(1)
const isLoading = ref(false)
const isShowSkeleton = ref(true)
const cashRecordDatas = ref<ICashRecordData>({} as ICashRecordData)

const { importStyle } = useImportTheme()

const search = () => {
  qPage.value = 1
  fetchEffectiveBetting()
}

const fetchEffectiveBetting = async () => {
  isLoading.value = true

  try {
    const param = {
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
      session_id: cookies.get('SESSION_ID'),
      game_kind: gameKindVal.value === 0 ? undefined : gameKindVal.value,
      type: typeVal.value === 'ALL' ? undefined : typeVal.value,
      start_date: dateVal.value[0],
      end_date: dateVal.value[1],
      sort: sortVal.value,
      page: qPage.value
    }
    
    cashRecordDatas.value = await getCashRecord(param)

    topTableData.value = [{
      sum: t('S_TOTAL'),
      amount: cashRecordDatas.value.total.amount || '--',
      balance: accountInfo.value.balance.toString() || '--'
    }]

    tableData.value = cashRecordDatas.value.transactionRecord
    totalPage.value = cashRecordDatas.value.pagination.totalPage
  } catch (error) {
    ElMessage.error((error as Error).message)
    console.log(error)
  } finally {
    isLoading.value = false
    isShowSkeleton.value = false
  }
}

fetchEffectiveBetting()

importStyle('mcenter/cash')
</script>

<template>
  <div>
    <el-skeleton v-if="isShowSkeleton" class="skeleton" :rows="5" animated />
    <div 
      class="cash-container"
      v-else
      v-loading="isLoading"
    >
      <CashSearch
        v-model:date-val = "dateVal"
        v-model:game-kind-val = "gameKindVal"
        v-model:type-val = "typeVal"
        :maintain-info = "cashRecordDatas.maintainInfo || []"
        @search="search"
      />
      <CashTable
        v-model:sort-val = "sortVal"
        :top-table-data="topTableData"
        :table-data="tableData"
        @sort="fetchEffectiveBetting"
      />
      <PaginationComponent
        v-model:current-page="qPage"
        :page-count="totalPage"
        @current-change="fetchEffectiveBetting"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.cash-container {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  padding: 15px;
}

.skeleton {
  padding: 15px;
}
</style>
