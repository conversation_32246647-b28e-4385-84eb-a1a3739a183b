import { mount } from '@vue/test-utils'
import { afterEach, describe, expect, it, vi } from 'vitest'
import { getEffectiveBetting } from '@/api/effectivebetting'
import EffectiveBetting from '@/views/mcenter/effectivebetting.vue'

vi.mock('@/api/effectivebetting', () => ({
  getEffectiveBetting: vi.fn(),
}))

vi.mock('vue-i18n', () => ({
  useI18n: vi.fn(() => ({
    t: vi.fn((msg: string) => msg),
  })),
}));

vi.mock('vue3-cookies', () => ({
  useCookies: vi.fn().mockReturnValue({
    cookies: {
      get: (key: string) => {
        if (key === 'session_id') return 'session_id'
        return null
      }
    }
  }),
}))

vi.mock('@/components/effectivebetting/EffectiveBettingSearch.vue', () => ({
  default: {
    name: 'EffectiveBettingSearch',
    template: '<div class="mock-effective-betting-search">Mock EffectiveBettingSearch</div>',
  },
}))

vi.mock('@/components/effectivebetting/EffectiveBettingTable.vue', () => ({
  default: {
    name: 'EffectiveBettingTable',
    template: '<div class="mock-effective-betting-table">Mock EffectiveBettingTable</div>',
  },
}))

describe('EffectiveBetting', () => {
  afterEach(() => {
    vi.clearAllMocks()
  })

  it('renders correctly', () => {
    const wrapper = mount(EffectiveBetting)
    expect(wrapper.html()).toContain('Mock EffectiveBettingSearch')
    expect(wrapper.html()).toContain('Mock EffectiveBettingTable')
  })

  it('fetches effective betting data', async () => {
    vi.mocked(getEffectiveBetting).mockResolvedValue({
      betTotal: 1000,
      gameBetList: [
        {
          bet: 120.00,
          gameKind: 3,
          payoff: 129.00
        }
      ],
      payoffTotal: 500,
      isMaintain: false,
      maintainInfo: []
    })

    const wrapper = mount(EffectiveBetting)
    await wrapper.vm.$nextTick()

    const search = wrapper.findComponent({ name: 'EffectiveBettingSearch' })
    const searchInstance = search.vm as any
    await searchInstance.$emit('search')

    const wrapperInstance = wrapper.vm as any

    expect(getEffectiveBetting).toHaveBeenCalled()
    expect(wrapperInstance.topTableData).toEqual([{
      sum: 'S_TOTAL',
      betting: '1000',
      jackpot: '500',
    }])

    expect(wrapperInstance.ebTableData).toEqual([{
      bet: 120.00,
      gameKind: 3,
      payoff: 129.00
    }])
  })

  it('handles error', async () => {
    const mockError = new Error('Failed to fetch effective betting data')
    vi.mocked(getEffectiveBetting).mockRejectedValue(mockError)
    vi.spyOn(console, 'log').mockImplementation(vi.fn())
    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())

    const wrapper = mount(EffectiveBetting)
    await wrapper.vm.$nextTick()

    const search = wrapper.findComponent({ name: 'EffectiveBettingSearch' })
    const searchInstance = search.vm as any
    await searchInstance.$emit('search')

    expect(getEffectiveBetting).toHaveBeenCalled()
    expect(console.log).toHaveBeenCalledWith(mockError)
    expect(ElMessage.error).toHaveBeenCalledWith(mockError.message)
  })
})
