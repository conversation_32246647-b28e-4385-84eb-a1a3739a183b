<script setup lang="ts">

import { getEffectiveBetting } from '@/api/effectivebetting'
import { useI18n } from 'vue-i18n'
import { IEffectiveBetting } from '@/types/effectivebetting'
import EffectiveBettingSearch from '@/components/effectivebetting/EffectiveBettingSearch.vue'
import EffectiveBettingTable from '@/components/effectivebetting/EffectiveBettingTable.vue'
import { useCookies } from 'vue3-cookies'

const { t } = useI18n()
const { cookies } = useCookies()
const isLoading = ref(false)
const dateVal = ref([])
const topTableData = ref([
  {
    sum: t('S_TOTAL'),
    betting: '--',
    jackpot: '--'
  }
])
const ebTableData = ref<IEffectiveBetting['gameBetList']>([])
const effectiveBettingData = ref<IEffectiveBetting>({} as IEffectiveBetting)

const fetchEffectiveBetting = async () => {
  isLoading.value = true

  try {
    const param = {
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
      session_id: cookies.get('SESSION_ID'),
      start_date: dateVal.value[0],
      end_date:  dateVal.value[1]
    }
    effectiveBettingData.value = await getEffectiveBetting(param)

    topTableData.value = [{
      sum: t('S_TOTAL'),
      betting: effectiveBettingData.value.betTotal.toString() || '--',
      jackpot: effectiveBettingData.value.payoffTotal.toString() || '--',
    }]
    ebTableData.value = effectiveBettingData.value.gameBetList.filter(item => Number(item.bet) > 0)
  } catch (error) {
    ElMessage.error((error as Error).message)
    console.log(error)
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <div class="effectivebetting-container" v-loading="isLoading">
    <EffectiveBettingSearch
      v-model:date-val="dateVal"
      @search="fetchEffectiveBetting"
    />
    <EffectiveBettingTable
      :top-table-data="topTableData"
      :eb-table-data="ebTableData"
    />
  </div>
</template>

<style lang="scss" scoped>
.effectivebetting-container {
  width: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
</style>
