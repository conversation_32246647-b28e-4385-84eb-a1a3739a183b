import { mount } from '@vue/test-utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import Gamenews from '@/views/mcenter/gamenews.vue'
import { useLobbySwitchStore } from '@/stores/lobbySwitch'
import { GameType } from '@/types/game'
import { createPinia, setActivePinia } from 'pinia'

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      'S_BB_LIVE': 'BB Live',
      'S_BBCASINO': 'BB Casino',
      'S_GAME_GROUP_BY_KENO': 'BB Lottery',
      'S_GAME_MAINTAIN': 'Game Maintain'
    },
  },
})

vi.mock('@/stores/lobbySwitch', () => ({
  useLobbySwitchStore: vi.fn()
}))

vi.mock('@/components/gamenews/live.vue', () => ({
  default: {
    name: 'GamenewsLive',
    template: '<div class="mock-gamenews-live">Mock GamenewsLive</div>',
  },
}))

vi.mock('@/components/gamenews/casino.vue', () => ({
  default: {
    name: 'GamenewsCasino',
    template: '<div class="mock-gamenews-casino">Mock GamenewsCasino</div>',
  },
}))

vi.mock('@/components/gamenews/lottery.vue', () => ({
  default: {
    name: 'GamenewsLottery',
    template: '<div class="mock-gamenews-lottery">Mock GamenewsLottery</div>',
  },
}))

const defaultGameMaintain = {
  maintain: false,
  message: ''
}

const maintainInfo = {
  live: { ...defaultGameMaintain },
  casino: { ...defaultGameMaintain },
  lottery: { ...defaultGameMaintain },
  sport: { ...defaultGameMaintain },
  fish: { ...defaultGameMaintain },
  card: { ...defaultGameMaintain }
}

describe('Gamenews', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.resetAllMocks()
  })

  it('renders correctly when not in maintenance', async () => {
    vi.mocked(useLobbySwitchStore).mockReturnValue({
      lobbySwitch: ref({
        [GameType.BBlive]: true,
        [GameType.Casino]: true,
        [GameType.Lottery]: true,
        [GameType.BCsport]: true,
        [GameType.Fishmaster]: true,
        [GameType.Battle]: true
      }),
      maintainInfo: ref(JSON.parse(JSON.stringify(maintainInfo)))
    } as any)

    const wrapper = mount(Gamenews, {
      global: {
        plugins: [i18n],
      },
    })

    const elCollapseItems = wrapper.findAll('.el-collapse-item')
    expect(elCollapseItems[0].html()).toContain('BB Live')
    expect(elCollapseItems[1].html()).toContain('BB Casino')
    expect(elCollapseItems[2].html()).toContain('BB Lottery')
    expect(wrapper.findAll('.el-tooltip').length).toEqual(0)
  })

  it('does not render game components when in maintenance', async () => {
    vi.mocked(useLobbySwitchStore).mockReturnValue({
      lobbySwitch: ref({
        [GameType.BBlive]: true,
        [GameType.Casino]: true,
        [GameType.Lottery]: true,
        [GameType.BCsport]: true,
        [GameType.Fishmaster]: true,
        [GameType.Battle]: true
      }),
      maintainInfo: ref({
        ...JSON.parse(JSON.stringify(maintainInfo)),
        live: { maintain: true, message: 'Maintenance in progress' }
      })
    } as any)

    const wrapper = mount(Gamenews, {
      global: {
        plugins: [i18n],
      },
      liveMaintain: true,
    })

    const elCollapseItems = wrapper.findAll('.el-collapse-item')
    expect(elCollapseItems[0].html()).toContain('Game Maintain')
    expect(elCollapseItems[1].html()).toContain('BB Casino')
    expect(elCollapseItems[2].html()).toContain('BB Lottery')
  })

  it('renders game components based on gameSwitch', async () => {
    vi.mocked(useLobbySwitchStore).mockReturnValue({
      lobbySwitch: ref({
        [GameType.BBlive]: true,
        [GameType.Casino]: false,
        [GameType.Lottery]: true,
        [GameType.BCsport]: true,
        [GameType.Fishmaster]: true,
        [GameType.Battle]: true
      }),
      maintainInfo: ref(JSON.parse(JSON.stringify(maintainInfo)))
    } as any)

    const wrapper = mount(Gamenews, {
      global: {
        plugins: [i18n],
      },
    })

    const vm = wrapper.vm as any;
    const elCollapseItems = wrapper.findAll('.el-collapse-item')

    vm.activeCollapseNames = 'live';
    await elCollapseItems[0].trigger('click')
    expect(wrapper.findComponent({ name: 'GamenewsLive' }).exists()).toBe(true)
    expect(wrapper.findComponent({ name: 'GamenewsCasino' }).exists()).toBe(false)
    expect(wrapper.findComponent({ name: 'GamenewsLottery' }).exists()).toBe(false)

    vm.activeCollapseNames = 'casino';
    await elCollapseItems[1].trigger('click')
    expect(wrapper.findComponent({ name: 'GamenewsLive' }).exists()).toBe(false)
    expect(wrapper.findComponent({ name: 'GamenewsCasino' }).exists()).toBe(false)
    expect(wrapper.findComponent({ name: 'GamenewsLottery' }).exists()).toBe(false)

    vm.activeCollapseNames = 'lottery';
    await elCollapseItems[2].trigger('click')
    expect(wrapper.findComponent({ name: 'GamenewsLive' }).exists()).toBe(false)
    expect(wrapper.findComponent({ name: 'GamenewsCasino' }).exists()).toBe(false)
    expect(wrapper.findComponent({ name: 'GamenewsLottery' }).exists()).toBe(true)
  })
})
