<script setup lang="ts">
import { useLobbySwitchStore } from '@/stores/lobbySwitch'
import { GameType } from '@/types/game'
import { useI18n } from 'vue-i18n'
import { storeToRefs } from 'pinia'

const { t } = useI18n()
const lobbySwitchStore = useLobbySwitchStore()
const { lobbySwitch, maintainInfo } = storeToRefs(lobbySwitchStore)
const activeCollapseNames = ref('')
</script>

<template>
  <div class="news-container">
    <el-collapse v-model="activeCollapseNames" accordion>
      <!-- Live News -->
      <el-collapse-item
        :title="t('S_BB_LIVE')"
        name="live"
        :disabled="maintainInfo.live && maintainInfo.live.maintain"
      >
        <template v-if="maintainInfo.live && maintainInfo.live.maintain" #icon>
          <el-tooltip placement="top">
            <template #content>
              <div v-html="maintainInfo.live.message"></div>
            </template>
            <span class="maintain-text">{{ t('S_GAME_MAINTAIN') }}</span>
          </el-tooltip>
        </template>
        <GamenewsLive 
          v-if="lobbySwitch[GameType.BBlive] && activeCollapseNames === 'live'"
        />
      </el-collapse-item>
      <!-- Casino News -->
      <el-collapse-item
        :title="t('S_BBCASINO')"
        name="casino"
        :disabled="maintainInfo.casino && maintainInfo.casino.maintain"
      >
        <template v-if="maintainInfo.casino && maintainInfo.casino.maintain" #icon>
          <el-tooltip placement="top">
            <template #content>
              <div v-html="maintainInfo.casino.message"></div>
            </template>
            <span class="maintain-text">{{ t('S_GAME_MAINTAIN') }}</span>
          </el-tooltip>
        </template>
        <GamenewsCasino
          v-if="lobbySwitch[GameType.Casino] && activeCollapseNames === 'casino'"
        />
      </el-collapse-item>
      <!-- Lottery News -->
      <el-collapse-item
        :title="t('S_GAME_GROUP_BY_KENO')"
        name="lottery"
        :disabled="maintainInfo.lottery && maintainInfo.lottery.maintain"
      >
        <template v-if="maintainInfo.lottery && maintainInfo.lottery.maintain" #icon>
          <el-tooltip placement="top">
            <template #content>
              <div v-html="maintainInfo.lottery.message"></div>
            </template>
            <span class="maintain-text">{{ t('S_GAME_MAINTAIN') }}</span>
          </el-tooltip>
        </template>
        <GamenewsLottery
          v-if="lobbySwitch[GameType.Lottery] && activeCollapseNames === 'lottery'"
        />
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<style lang="scss" scoped>
  .news-container {
    padding: 50px;

    :deep(.el-collapse-item__header) {
      font-size: 16px;
      font-weight: bold;
      color: var(--mcenter-collapse-item-text);
      display: flex;
      justify-content: space-between;
    }

    .maintain-text {
      color: #ff5656;
      &:before {
        content: '!';
        width: 15px;
        height: 15px;
        border-radius: 50%;
        background: #ff5656;
        color: #fff;
        display: inline-block;
        text-align: center;
        line-height: 15px;
        margin-right: 5px;
      }
    }

  }
</style>
