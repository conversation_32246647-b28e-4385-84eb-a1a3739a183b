import { mount } from '@vue/test-utils'
import { afterEach, beforeEach, describe, it, expect, vi } from 'vitest'
import Card from '@/views/mobile/card.vue'
import { useCardGameStore } from "@/stores/cardGame"
import { ElMessage } from 'element-plus'
import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', name: 'home', component: { template: '<div>Home</div>' } },
    { path: '/m/main', name: 'mobile_menu', component: { template: '<div>Menu</div>' } },
    { path: '/m/card', name: 'mcard', component: { template: '<div>Card</div>' } }
  ]
})

vi.mock('@/stores/cardGame', () => ({
  useCardGameStore: vi.fn()
}))

vi.mock('@/components/TopGameMenu.vue', () => ({
  default: {
    name: 'TopGameMenu',
    template: '<div class="mock-top-game-menu">Mock TopGameMenu</div>',
  },
}))

vi.mock('@/components/mobile/SearchBox.vue', () => ({
  default: {
    name: 'SearchBox',
    template: '<div class="mock-search-box">Mock SearchBox</div>',
  },
}))

vi.mock('@/components/mobile/GameCenter.vue', () => ({
  default: {
    name: 'GameCenter',
    template: '<div class="mock-game-center">Mock GameCenter</div>',
  },
}))

describe('Card', () => {

  beforeEach(() => {
    vi.mocked(useCardGameStore).mockReturnValue({
      fetchCardData: vi.fn().mockResolvedValue(undefined),
      getAllGameMenu: vi.fn().mockReturnValue([{ id: 1, name: 'Top Game' }, { id: 84, name: 'All Game' }]),
      getGameListByMenuId: vi.fn().mockReturnValue([{ id: 11, name: 'Game 1' }, { id: 12, name: 'Game 2' }]),
      searchGame: vi.fn().mockReturnValue([{ id: 11, name: 'Game 1' }]),
    } as any)
  })

  afterEach(() => {
    vi.resetAllMocks();
  })

  it('should load user info and game menu on mount', async () => {
    const wrapper = mount(Card, {
      global: {
        plugins: [router],
      }
    })
    const vm = wrapper.vm as any

    await nextTick()
    await wrapper.vm.$nextTick();
    
    expect(vm.isLoading).toBe(false)
    expect(vm.gameMenu).toEqual([{ id: 1, name: 'Top Game' }, { id: 84, name: 'All Game' }])
  })

  it('should set top menu and show game list', async () => {
    const wrapper = mount(Card, {
      global: {
        plugins: [router],
      }
    })
    const vm = wrapper.vm as any

    await nextTick()
    await wrapper.vm.$nextTick();

    expect(vm.inputSearchGame).toBe('')
    expect(vm.currentCardList).toEqual([{ id: 11, name: 'Game 1' }, { id: 12, name: 'Game 2' }])
    expect(vm.topMenuId).toBe(84)
  })

  it('should handle game search', async () => {
    const wrapper = mount(Card, {
      global: {
        plugins: [router],
      }
    })

    await nextTick()
    await wrapper.vm.$nextTick();

    const vm = wrapper.vm as any
    vm.inputSearchGame = 'Game 1'

    await wrapper.vm.$nextTick();

    expect(vm.currentCardList).toEqual([{ id: 11, name: 'Game 1' }])
  })

  it('should show all games when search input is empty', async () => {
    const wrapper = mount(Card, {
      global: {
        plugins: [router],
      }
    })

    const vm = wrapper.vm as any
    vm.inputSearchGame = ''
    await vm.handleGameSearch()

    expect(vm.currentCardList).toEqual([{ id: 11, name: 'Game 1' }, { id: 12, name: 'Game 2' }])
  })

  it('should show error message on error', async () => {
    const errorMessage = 'Error fetching data'

    vi.mocked(useCardGameStore).mockReturnValue({
      fetchCardData: vi.fn().mockRejectedValue(new Error(errorMessage))
    } as any)

    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const wrapper = mount(Card, {
      global: {
        plugins: [router],
      }
    })
    await wrapper.vm.$nextTick()

    expect(ElMessage.error).toHaveBeenCalledWith(errorMessage)
    expect(console.error).toHaveBeenCalledWith(new Error(errorMessage))
  })
})
