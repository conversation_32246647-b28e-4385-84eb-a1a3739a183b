<script setup lang="ts">
import { useCardGameStore } from "@/stores/cardGame"
import type { IGameInfo, IGameMenu } from '@/types/game'
import TopGameMenu from '@/components/TopGameMenu.vue'
import SearchBox from '@/components/mobile/SearchBox.vue'
import { ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import GameCenter from '@/components/mobile/GameCenter.vue'

const router = useRouter()
const cardGameStore = useCardGameStore()
const gameMenu = ref<IGameMenu[]>([])
const currentCardList = ref<IGameInfo[]>([])
const inputSearchGame = ref('')
const topMenuId = ref<number>(84)
const isLoading = ref(false)
const maintainStr = ['维护时间', '維護時間', 'Maintenance time']

provide('nowPage','card')

const setTopMenu = () => {
  inputSearchGame.value = ''
  showGameList(topMenuId.value)
}

const showGameList = (menuItemID: number) => {
  const gameList = cardGameStore.getGameListByMenuId(menuItemID)
  currentCardList.value = gameList.filter((game): game is IGameInfo => game !== undefined)
}

const handleGameSearch = () => {
  topMenuId.value = 84

  if (inputSearchGame.value) {
    currentCardList.value = cardGameStore.searchGame(inputSearchGame.value)
  } else {
    showGameList(topMenuId.value)
  }
}

watch(inputSearchGame, () => {
  handleGameSearch()
})

onMounted(async () => {
  try {
    isLoading.value = true
    await cardGameStore.fetchCardData(true)
    gameMenu.value = cardGameStore.getAllGameMenu()
    setTopMenu()
  } catch (error) {
    const isMaintain = maintainStr.some(str => (error as Error).message.includes(str))

    if (isMaintain) {
      ElMessageBox.alert(
        (error as Error).message?.replaceAll(`\\n`, '<br/>'), 
        '', 
        {
          center: true,
          showClose: false,
          dangerouslyUseHTMLString: true,
          callback: (action: string) => {
            if (action === 'confirm') router.push('/m/main')
          }
        }
      )
    } else {
      ElMessage.error((error as Error).message)
    }
    
    console.error(error)
  } finally {
    isLoading.value = false
  }
})
</script>

<template>
  <div
    v-loading="isLoading"
    class="card-container"
  >
    <div class="menu-bar">
      <TopGameMenu
        v-model:top-menu-id="topMenuId"
        :game-menu="gameMenu"
        @click="setTopMenu"
      />
      <div class="search-bar">
        <SearchBox
          v-model="inputSearchGame"
          class="search-box"
        />
      </div>
    </div>
    <div class="content">
      <GameCenter :game-list="currentCardList" />
    </div>
  </div>
</template>

<style lang="scss" scoped>

.card-container {
  position: relative;
  width: 100%;
  min-height: 70vh;
  margin-top: 40px;
}

.menu-bar {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  text-align: center;
  border-top: 1px solid var(--mobile-menu-bar-border);
  border-bottom: 1px solid var(--mobile-menu-bar-border);
  background: var(--mobile-menu-bar-bg);

  .search-bar {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin: 0 auto;

    .search-box {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      margin: 0 auto;
    }

    :deep(.search-input) {
      width: 70%;
      margin: 3px 5px;
    }
  }
}

</style>
