<script setup lang="ts">
import { useCasinoGameStore } from '@/stores/casinoGame'
import type { IGameInfo, IGameMenu } from '@/types/game'
import { storeToRefs } from 'pinia'
import { useEjpStore } from '@/stores/ejpremote'
import TopGameMenu from '@/components/TopGameMenu.vue'
import SearchBox from '@/components/mobile/SearchBox.vue'
import SubGameMenu from '@/components/SubGameMenu.vue'
import { ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import GameCenter from '@/components/mobile/GameCenter.vue'

provide('nowPage','casino')
const casinoGameStore = useCasinoGameStore()
const ejpStore = useEjpStore()
const router = useRouter()
const { jpList } = storeToRefs(ejpStore)
const { gameList } = storeToRefs(casinoGameStore)
const currentGameList = ref<IGameInfo[]>([])
const inputSearchGame = ref('')
const mainMenu = ref<IGameMenu[]>([])
const subMenu = ref<IGameMenu[]>([])
const showLower = ref(false)
const topMenuId = ref<number>(2)
const subMenuId = ref<number>(84)
const lowerMenuId = ref<number>(0)
const isLoading = ref(false)
const maintainStr = ['维护时间', '維護時間', 'Maintenance time']

const setTopMenu = () => {
  inputSearchGame.value = ''

  if (topMenuId.value === 84) {
    subMenuId.value = topMenuId.value
  }

  showGameList(topMenuId.value)
}

const setSubMenu = () => {
  inputSearchGame.value = ''

  if (lowerMenuId.value > 0) {
    showGameList(lowerMenuId.value)
  } else {
    showGameList(subMenuId.value)
  }
}

const showGameList = (menuItemID: number) => {
  const gameList = casinoGameStore.getGameListByMenuId(menuItemID)
  currentGameList.value = gameList.filter((game): game is IGameInfo => game !== undefined)
}

const handleGameSearch = () => {
  topMenuId.value = 84
  subMenuId.value = 84

  if (inputSearchGame.value) {
    currentGameList.value = casinoGameStore.searchGame(inputSearchGame.value)
  } else {
    showGameList(topMenuId.value)
  }
}

const showJpInfo = () => {
  // 彩金條 jpAmount / jpImg
  if( jpList.value.length > 0 && gameList.value.length > 0) {
    gameList.value.forEach(game => {
      const foundItem = jpList.value.find(jp => jp.gameType == game.gameId.toString())
      game.isShowJp = false

      if (foundItem) {
        game.jpAmount = foundItem.poolAmount || 0
        game.jpImg = foundItem.jpImg || ''
        game.isShowJp = true
      }
    })
  }
}

watch(inputSearchGame, () => {
  handleGameSearch()
})

watch(ejpStore.jpList, () => {
  showJpInfo()
})

onMounted( async() => {
  try {
    isLoading.value = true
    await casinoGameStore.fetchCasinoData(true)
    mainMenu.value = casinoGameStore.getTopGameMenu()
    subMenu.value = casinoGameStore.getSubGameMenu()
    ejpStore.connectWebSocket()

    if(mainMenu.value.length > 0) {
      setTopMenu()
    }
  } catch (error) {
    const isMaintain = maintainStr.some(str => (error as Error).message.includes(str))

    if (isMaintain) {
      ElMessageBox.alert(
        (error as Error).message?.replaceAll(`\\n`, '<br/>'), 
        '', 
        {
          center: true,
          showClose: false,
          dangerouslyUseHTMLString: true,
          callback: (action: string) => {
            if (action === 'confirm') router.push('/m/main')
          }
        }
      )
    } else {
      ElMessage.error((error as Error).message)
    }
    
    console.error(error)
  } finally {
    isLoading.value = false
  }
})

onBeforeUnmount(() => {
  ejpStore.disconnectWebSocket()
})
</script>

<template>
  <div
    v-loading="isLoading"
    class="casino-container"
  >
    <div class="casino-content">
      <div class="menu-bar">
        <TopGameMenu
          v-model:top-menu-id="topMenuId"
          :game-menu="mainMenu"
          @click="setTopMenu"
        />
        <template v-if="topMenuId == 84">
          <SubGameMenu
            v-model:sub-menu-id="subMenuId"
            v-model:lower-menu-id="lowerMenuId"
            v-model:show-lower="showLower"
            :sub-menu="subMenu"
            lower-type="static"
            @click="setSubMenu"
          />
        </template>
        <div class="search-bar">
          <SearchBox
            v-model="inputSearchGame"
            class="search-box"
          />
        </div>
      </div>
      <div class="content" :class="showLower? 'show-lower': ''">
        <GameCenter :game-list="currentGameList" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.casino-container {
  position: relative;
  width: 100%;
  min-height: 70vh;
  display: flex;
  justify-content: space-between;
  text-align: center;
}

.casino-content {
  width: 100%;
}

.content {
  &.show-lower {
    padding-top : 6vh;
  }
}

.menu-bar {
  width: 100%;
  display: flex;
  flex-direction: column;
  background: var(--mobile-menu-bar-bg);
  border-top: 1px solid var(--mobile-menu-bar-border);
}

.search-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin: 0 auto;

  .search-box {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin: 0 auto;
  }

  :deep(.search-input) {
    width: 70%;
    margin: 3px 5px;
  }
}

.ele-casino-menu-top-item {
  position: relative;
  float: left;
  width: 90px;
  padding: 0 5px;
  height: 36px;
  line-height: 36px;
  color: #555;
  text-align: center;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  &.ele-casino-menu-event {
    color: #DF6969;
  }
  &:hover,
  &.now {
    background: var(--main);
  }
  &.ele-casino-menu-top-item-all.now::after {
      content: '';
      position: absolute;
      left: 0;
      bottom: -1px;
      width: 100%;
      height: 1px;
      background: var(--main);
  }
}
</style>
