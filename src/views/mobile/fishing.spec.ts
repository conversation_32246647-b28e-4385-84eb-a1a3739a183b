import { mount } from '@vue/test-utils'
import { afterEach, describe, expect, it, vi } from 'vitest'
import Fishing from './fishing.vue'
import { useFishingGameStore } from '@/stores/fishingGame'
import { ElMessage } from 'element-plus'

vi.mock('@/stores/fishingGame', () => ({
  useFishingGameStore: vi.fn()
}))

vi.mock('@/components/mobile/SearchBox.vue', () => ({
  default: {
    name: 'SearchBox',
    template: '<div class="mock-search-box">Mock SearchBox</div>',
  },
}))

vi.mock('@/components/mobile/GameCenter.vue', () => ({
  default: {
    name: 'GameCenter',
    template: '<div class="mock-game-center">Mock GameCenter</div>',
  },
}))

describe('Fishing.vue', () => {
  afterEach(() => {
    vi.clearAllMocks();
  })

  it('displays loading state while fetching data', async () => {
    vi.mocked(useFishingGameStore).mockReturnValue({
      fetchFishingData: vi.fn().mockResolvedValueOnce(undefined),
      getGameList: vi.fn().mockResolvedValueOnce([]),
    } as any)

    const wrapper = mount(Fishing)
    const vm = wrapper.vm as any

    expect(vm.isLoading).toBe(true)
    await wrapper.vm.$nextTick()
    expect(vm.isLoading).toBe(false)
  })

  it('fetches game list on mount', async () => {
    const gameList = [{ id: 1, name: 'Fishing Game 1' }, { id: 2, name: 'Fishing Game 2' }]

    vi.mocked(useFishingGameStore).mockReturnValue({
      fetchFishingData: vi.fn().mockResolvedValueOnce(undefined),
      getGameList: vi.fn().mockReturnValueOnce(gameList),
    } as any)

    const wrapper = mount(Fishing)
    const vm = wrapper.vm as any

    await wrapper.vm.$nextTick()

    expect(vm.currentGameList).toEqual(gameList)
  })

  it('should handle game search', async () => {
    const gameList = [{ id: 1, name: 'Fishing Game 1' }, { id: 2, name: 'Fishing Game 2' }]

    vi.mocked(useFishingGameStore).mockReturnValue({
      fetchFishingData: vi.fn().mockResolvedValueOnce(undefined),
      getGameList: vi.fn().mockReturnValue(gameList),
      searchGame: vi.fn().mockReturnValueOnce([{ id: 2, name: 'Fishing Game 2' }])
    } as any)

    const wrapper = mount(Fishing)
    await wrapper.vm.$nextTick()

    const vm = wrapper.vm as any
    vm.inputSearchGame = 'Fishing Game 2'

    await wrapper.vm.$nextTick()

    expect(vm.currentGameList).toEqual([{ id: 2, name: 'Fishing Game 2' }])

    vm.inputSearchGame = ''

    await wrapper.vm.$nextTick()
    await nextTick()

    expect(vm.currentGameList).toEqual(gameList)
  })

  it('shows error message when fetch fails', async () => {
    const errorMessage = 'Failed to fetch data'

    vi.mocked(useFishingGameStore).mockReturnValue({
      fetchFishingData: vi.fn().mockRejectedValueOnce(new Error(errorMessage)),
    } as any)

    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const wrapper = mount(Fishing)
    await wrapper.vm.$nextTick()

    expect(ElMessage.error).toHaveBeenCalledWith(errorMessage)
    expect(console.error).toHaveBeenCalledWith(new Error(errorMessage))
  })
})
