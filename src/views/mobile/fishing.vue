<script setup lang="ts">
import { useFishingGameStore } from '@/stores/fishingGame'
import type { IGameInfo } from '@/types/game'
import SearchBox from '@/components/mobile/SearchBox.vue'
import GameCenter from '@/components/mobile/GameCenter.vue'

const fishingGameStore = useFishingGameStore()
const currentGameList = ref<IGameInfo[]>([])
const inputSearchGame = ref('')
const isLoading = ref(false)
provide('nowPage','fishing')

const handleGameSearch = async () => {
  if (inputSearchGame.value) {
    currentGameList.value = fishingGameStore.searchGame(inputSearchGame.value)
  } else {
    currentGameList.value = fishingGameStore.getGameList()
  }
}

watch(inputSearchGame, () => {
  handleGameSearch()
})

onMounted(async () => {
  try {
    isLoading.value = true
    await fishingGameStore.fetchFishingData(true)
    currentGameList.value = fishingGameStore.getGameList()
  } catch (error) {
    ElMessage.error((error as Error).message)
    console.error(error)
  } finally {
    isLoading.value = false
  }
})
</script>

<template>
  <div
    v-loading="isLoading"
    class="fishing-container"
  >
    <div class="fishing-content">
      <div class="menu-bar">
        <div class="search-bar">
          <SearchBox
            v-model="inputSearchGame"
            class="search-box"
          />
        </div>
      </div>
      <GameCenter
        v-if="currentGameList.length > 0"
        :game-list="currentGameList"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.fishing-container {
  position: relative;
  width: 100%;
  min-height: 70vh;
  display: flex;
  
  .no-data {
    padding: 20px;
  }
}

.fishing-content {
  width: 100%;
}

.menu-bar {
  width: 100%;
  height: 40px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  border-top: 1px solid var(--mobile-menu-bar-border);
  border-bottom: 1px solid var(--mobile-menu-bar-border);
  background: var(--mobile-menu-bar-bg);

  .search-bar {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin: 0 auto;

    .search-box {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      margin: 0 auto;
    }

    :deep(.search-input) {
      width: 70%;
      margin: 3px 5px;
    }
  }
}
</style>
