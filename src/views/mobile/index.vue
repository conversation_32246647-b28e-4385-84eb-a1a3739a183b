<script setup lang="ts">
import { useMobileBasicInfoStore } from "@/stores/mobilebasic"
import { useConfigStore } from '@/stores/config'
import { useAccountInfoStore } from "@/stores/account"
import { useLobbySwitchStore } from '@/stores/lobbySwitch'
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { useCookies } from 'vue3-cookies'
import { storeToRefs } from 'pinia'
import { useI18n } from "vue-i18n"
import { getSportLobbyLink } from "@/api/game"

const { cookies } = useCookies()
const router = useRouter()
const mobileBasicStore = useMobileBasicInfoStore()
const configStore = useConfigStore()
const accountInfoStore = useAccountInfoStore()
const lobbySwitchStore = useLobbySwitchStore()
const { t } = useI18n()
const { siteInfo } = storeToRefs(mobileBasicStore)
const { accountInfo } = storeToRefs(accountInfoStore)
const { cdnUrl } = storeToRefs(configStore)
const { mMenuList, maintainInfo } = storeToRefs(lobbySwitchStore)
const exitOptionSort = [
  '/m/live',
  '/m/lottery',
  '/m/xbblive',
  '/m/xbbsports',
  '/m/main',
  '/m/bbdigitaltrend'
] // BB、XBB遊戲在url串exit_option
const gameKindNameMap = {
  3: 'live',
  5: 'casino',
  12: 'lottery',
  31: 'sport',
  38: 'fish',
  66: 'card'
} as const;

type GameKindNumeric = keyof typeof gameKindNameMap;


const loadedImages = ref(0)

const imagesLeng = computed(() => {
  const sub = mMenuList.value.map(item => item.list)
  if (sub.length > 0) {
    return countKeys(sub)
  }
  return 0
})

const allLoaded = computed(() => { 
  return loadedImages.value === imagesLeng.value
})

const handleImgLoad = () => {
  loadedImages.value++
}

const countKeys = (data: any) => {
  let keyArr = []
  for (const innerList of data) {
    for (const obj of innerList) {
      keyArr.push(obj.key)
    }
  }
  return keyArr.length
}


const gameLobbyUrl = (link: string) => {
    let exitOptionURI = '' // BB、XBB遊戲在url串exit_option

    if (exitOptionSort.includes(link)) {
        // AIO || PWA
        if (
          exitOptionURI ||
          ('standalone' in window.navigator &&
            window.navigator.standalone)
        ) {
            exitOptionURI = '3'
        } else {
            exitOptionURI = '2'
        }
    }

    // 其他遊戲登入後 開啟遊戲
    router.push({path: link, query: {exit_option: exitOptionURI}})
}

const openSport = async (enter_page: string) => {
  try {
    const params = {
      session_id: cookies.get('SESSION_ID'),
      lang: cookies.get('lang'),
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
      domain_url: location.hostname,
      is_mobile: true,
      enter_page: enter_page || undefined
    }

    const res = await getSportLobbyLink(params)

    if (res.maintain) {
      ElMessage.error({
        dangerouslyUseHTMLString: true,
        message: res.maintainInfo
      })
      return
    }

    window.location.href = res.link
  } catch(error) {
    ElMessage.error((error as Error).message)
    console.error(error)
  }
}


const openGame = (category: string, subItem: any) => {
  const {
    key,
    link,
    lobbyLink,
    code
  } = subItem

  // 維護中遊戲不得開啟 & 顯示維護資訊
  const maintainItem = maintainInfo.value[gameKindNameMap[code as GameKindNumeric]]
  if (maintainItem?.maintain) {
    ElMessageBox.alert(
      maintainItem.message?.replaceAll(`\\n`, '<br/>'), 
      '', 
      {
        center: true,
        showClose: false,
        dangerouslyUseHTMLString: true
      }
    )
    return
  }

  if (accountInfo.value.bankrupt) {
    return ElMessageBox.alert(
      t('S_POWER_STOP'), 
      '', 
      { center: true, showClose: false}
    )
  }

  if (['game', 'fisharea', 'bbcard'].includes(key)) {
    router.push(link)
    return
  } 

  if (!accountInfo.value.isLogin) {
    ElMessageBox.alert(
      t('S_LOGIN_TIPS'),
      '',
      { center: true, showClose: false}
    )
    return
  }

  if (category === 'ball') {
    openSport(lobbyLink.enter_page)
  } else {
    gameLobbyUrl(link)
  }
}

onMounted(async () => {
  try {
    const page_site_ck = cookies.get('page_site')
    if (page_site_ck) {
      const page_site = page_site_ck
      const page_site_ss = sessionStorage.getItem('page_site')
  
      if (page_site_ss !== page_site) {
        switch (page_site) {
          case 'game':
            router.push({name: 'mcasino'})
            break
          case 'fisharea':
            router.push({name: 'mfishing'})
            break
          case 'xbbcasino':
            router.push({name: 'mcasino'})
            break
          case 'bbpcasino':
            router.push({name: 'mcasino'})
            break
          case 'bbcard':
            router.push({name: 'mcard'})
            break
          case 'bbpcard':
            router.push({name: 'mcard'})
            break
        }
      }
      sessionStorage.setItem('page_site', page_site);
    }
  } catch (error) {
    ElMessage.error((error as Error).message)
    console.error(error)
  }
})
</script>

<template>
  <div
    v-loading="!allLoaded"
    class="mobile-menu"
  >
      <template v-for="menu in mMenuList" :key="menu.category">
        <template v-for="sub in menu.list" :key="sub.key">
          <div class="menu-item" @click="openGame(menu.category, sub)">
            <span class="name" :class="allLoaded ? 'show':''">{{ sub.name }}</span>
            <el-image
              :src="`${cdnUrl}/m/new/img/page/home/<USER>/${menu.category}/cards/short/${sub.code}.png?v=${siteInfo.unitTime}`"
              lazy
              @load="handleImgLoad()"
            />
          </div>
        </template>
      </template>
  </div>
</template>

<style lang="scss" scoped>
  .mobile-menu {
    position: relative;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    margin: 50px 0;

    .menu-item {
      position: relative;
      margin: 3px;
      text-decoration: none;
      cursor: pointer;
      border: 2px solid #282828;

      .name {
        position: absolute;
        color: #fff;
        z-index: 1;
        padding: 10px;
        opacity: 0;

        &.show {
          opacity: 1;
          transition: all 0.3s;
        }
      }
      
      img {
        width: 100%;
        height: auto;
      }

    }
  }
</style>