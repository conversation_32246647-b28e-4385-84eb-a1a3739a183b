import { mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import Live from './live.vue'
import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', name: 'home', component: { template: '<div>Home</div>' } },
    { path: '/m/main', name: 'mobile_menu', component: { template: '<div>Menu</div>' } },
    { path: '/m/live', name: 'mcard', component: { template: '<div>Card</div>' } }
  ]
})

const i18n = createI18n({
  legacy: false,
  locale: 'en',
})

vi.mock('@/api/game', () => ({
  getLiveLobbyLink: vi.fn(() => ({
    link: 'test123.com',
    maintain: false,
    maintainInfo: ''
  })),
}))

vi.mock('vue3-cookies', () => ({
  useCookies: vi.fn().mockReturnValue({
    cookies: {
      get: (key: string) => {
        if (key === 'SESSION_ID') return 'session_id'
        if (key === 'lang') return 'en'
        return null
      }
    }
  })
}))

describe('Live.vue', () => {
  it('should render correctly', () => {
    window.location = { href: '' } as Location

    const wrapper = mount(Live, {
      global: {
        plugins: [i18n, router],
      },
    })

    expect(wrapper.exists()).toBe(true)
  })

  it('should redirect on mounted', async () => {
    window.location = { href: '' } as Location

    const wrapper = mount(Live, {
      global: {
        plugins: [i18n, router],
      },
    })

    await wrapper.vm.$nextTick()
    expect(window.location.href).toBe(`test123.com`)
  })
})