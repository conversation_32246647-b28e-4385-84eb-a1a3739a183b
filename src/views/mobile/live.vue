<script setup lang="ts">
import { getLiveLobbyLink } from '@/api/game'
import { useCookies } from 'vue3-cookies'
import { ElMessageBox } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

const { t } = useI18n()
const { cookies } = useCookies()
const router = useRouter()

const redirect = async () => {
  if (!cookies.get('SESSION_ID')) {
    ElMessageBox.alert(
      t('S_LOGIN_TIPS'), 
      '', 
      {
        center: true,
        showClose: false,
        dangerouslyUseHTMLString: true,
        callback: (action: string) => {
          if (action === 'confirm') router.push('/m/main')
        }
      }
    )
    return
  }

  try {
    const params = {
      session_id: cookies.get('SESSION_ID'),
      lang: cookies.get('lang'),
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
      domain_url: location.hostname,
      is_mobile: true,
      enter_page: 'ultimate' // 旗艦廳
    }

    const res = await getLiveLobbyLink(params)

    if (res.maintain) {
      ElMessage.error({
        dangerouslyUseHTMLString: true,
        message: res.maintainInfo
      })
      return
    }

    window.location.href = res.link
  } catch(error) {
    ElMessage.error((error as Error).message)
    console.error(error)
  }
}

redirect()
</script>

<template>
  <div class="mobile-live">
  </div>
</template>

<style lang="scss" scoped>
.mobile-live {
  margin-top: 100px;
}
</style>

