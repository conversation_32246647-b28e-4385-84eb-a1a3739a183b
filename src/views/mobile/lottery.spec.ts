import { describe, expect, it, vi } from 'vitest'
import Lottery from './lottery.vue'
import { getLotteryList } from '@/api/game'
import { ElMessage } from 'element-plus'
import { flushPromises, mount } from '@vue/test-utils'
import { ILotteryList } from '@/types/lottery'
import { useLobbyLinkStore } from '@/stores/lobbyLink'

vi.mock('@/api/game', () => ({
  getLotteryList: vi.fn()
}))

vi.mock('@/stores/lobbyLink', () => ({
  useLobbyLinkStore: vi.fn()
}))

vi.mocked(useLobbyLinkStore).mockReturnValue({
  fetchLotteryLobby: vi.fn(),
  lobbyLink: ref({ 
    lottery: {
      domain: 'test.com',
      url: 'https://test.com/a.json'
    }
  })
} as any)

vi.mock('vue3-cookies', () => ({
  useCookies: vi.fn().mockReturnValue({
    cookies: {
      get: (key: string) => {
        if (key === 'SESSION_ID') return 'session_id'
        if (key === 'lang') return 'en'
        return null
      }
    }
  }),
}))

describe('lottery.vue', () => {
  it('calls getLotteryList on mount', async () => {
    const mockResponse = {
      traditionEntrance: '/a.json'
    } as ILotteryList

    vi.mocked(getLotteryList).mockResolvedValueOnce(mockResponse)

    window.location = { href: '' } as Location

    const wrapper = mount(Lottery)
    await wrapper.vm.$nextTick()
    
    expect(getLotteryList).toHaveBeenCalled()
  })

  it('redirects to the correct URL when traditionEntrance is present', async () => {
    const mockResponse = {
      traditionEntrance: '/a.json'
    } as ILotteryList

    vi.mocked(getLotteryList).mockResolvedValueOnce(mockResponse)

    window.location = { href: '' } as Location

    mount(Lottery)
    await flushPromises()

    expect(window.location.href).toBe(`test.com/a.json`)
  })

  it('shows error message when API call fails', async () => {
    const errorMessage = 'Failed to fetch lottery data'
    vi.mocked(getLotteryList).mockRejectedValueOnce(new Error(errorMessage))

    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())
    vi.spyOn(console, 'error').mockImplementation(vi.fn())
    mount(Lottery)

    await flushPromises()

    expect(ElMessage.error).toHaveBeenCalledWith(errorMessage)
    expect(console.error).toHaveBeenCalledWith(new Error(errorMessage))
  })
})