<script setup lang="ts">
import { getLotteryList } from '@/api/game'
import { useCookies } from 'vue3-cookies'
import { useLobbyLinkStore } from '@/stores/lobbyLink'
import { storeToRefs } from 'pinia'

const lobbyLinkStore = useLobbyLinkStore()
const { lobbyLink } = storeToRefs(lobbyLinkStore)
const { cookies } = useCookies()
const getLotteryData = async () => {
  try {
    const sessionId = cookies.get('SESSION_ID')

    const params = {
      session_id: sessionId,
      lang: cookies.get('lang'),
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
      filter: ''
    }

    if (sessionId) {
      await lobbyLinkStore.fetchLotteryLobby({
        session_id: sessionId,
        lang: cookies.get('lang'),
        hall_id: Number(localStorage.getItem('hallinfo_hallid')),
        is_mobile: true,
        domain_url: location.hostname
      })

      const res = await getLotteryList(params)
      
      if(res.traditionEntrance) {
        window.location.href = `${lobbyLink.value.lottery.domain}${res.traditionEntrance}`
      }
    }
  } catch (error) {
    ElMessage.error((error as Error).message)
    console.error(error)
  }
}

onMounted(async () => {
  getLotteryData()
})
</script>

<template>
  <div class="mobile-lottery">
  </div>
</template>

<style lang="scss" scoped>
.mobile-lottery {
  margin-top: 100px;
}
</style>
