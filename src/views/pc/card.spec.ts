import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import Card from './card.vue'
import { useCardGameStore } from '@/stores/cardGame'
import { ref } from 'vue'

vi.mock('vue-i18n', () => ({
  createI18n: vi.fn(),
  useI18n: () => ({
    t: (key: string) => key
  })
}))

vi.mock('@/composables/useImportTheme', () => ({
  useImportTheme: () => ({
    importStyle: vi.fn()
  })
}))

vi.mock('@/stores/basicInfo', () => ({
  useBasicInfoStore: vi.fn(() => ({
    siteInfo: ref({ hallId: '123' }),
  }))
}))

vi.mock('@/stores/account', () => ({
  useAccountInfoStore: vi.fn(() => ({
    accountInfo: ref({}),
  }))
}))

vi.mock('@/stores/cardGame', () => ({
  useCardGameStore: vi.fn(() => ({
    fetchLobbyLink: vi.fn(),
    fetchCardData: vi.fn(),
    getAllGameMenu: vi.fn(),
    getGameListByMenuId: vi.fn(),
    searchGame: vi.fn()
  }))
}))

vi.mock('@/components/TopGameMenu.vue', () => ({
  default: {
    template: '<div class="top-game-menu"></div>'
  }
}))

vi.mock('@/components/pc/SearchBox.vue', () => ({
  default: {
    template: '<div class="search-box"></div>'
  }
}))

vi.mock('@/components/pc/EnterCardLobby.vue', () => ({
  default: {
    template: '<div class="enter-card-lobby"></div>'
  }
}))

vi.mock('@/components/MaintainDialog.vue', () => ({
  default: {
    template: '<div class="maintain-dialog"></div>'
  }
}))

vi.mock('@/components/pc/GameCenter.vue', () => ({
  default: {
    template: '<div class="game-center"></div>'
  }
}))

describe('Card.vue', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should set correct initial state', () => {
    vi.mocked(useCardGameStore).mockReturnValue({
      fetchLobbyLink: vi.fn().mockResolvedValue(undefined),
      fetchCardData: vi.fn().mockResolvedValue(undefined),
      getAllGameMenu: vi.fn().mockReturnValue([{ id: 1, name: 'Top Game' }, { id: 84, name: 'All Game' }]),
      getGameListByMenuId: vi.fn().mockReturnValue([{ id: 11, name: 'Game 1' }, { id: 12, name: 'Game 2' }]),
      searchGame: vi.fn().mockReturnValue([{ id: 11, name: 'Game 1' }]),
    } as any)

    const wrapper = mount(Card)

    expect(wrapper.find('.enter-card-lobby').exists()).toBe(true)
    expect(wrapper.find('.top-game-menu').exists()).toBe(true)
    expect(wrapper.find('.search-box').exists()).toBe(true)
    expect(wrapper.find('.pc-games').exists()).toBe(false)
    expect(wrapper.find('.no-data').exists()).toBe(true)
    expect(wrapper.find('.maintain-dialog').exists()).toBe(false)
  })

  it('should load game list data correctly', async () => {
    const mockGameList = [{ id: 11, name: 'Game 1' }, { id: 12, name: 'Game 2' }]
    vi.mocked(useCardGameStore).mockReturnValue({
      fetchLobbyLink: vi.fn().mockResolvedValue(undefined),
      fetchCardData: vi.fn().mockResolvedValue(undefined),
      getAllGameMenu: vi.fn().mockReturnValue([{ id: 1, name: 'Top Game' }, { id: 84, name: 'All Game' }]),
      getGameListByMenuId: vi.fn().mockReturnValue(mockGameList),
      searchGame: vi.fn().mockReturnValue([{ id: 11, name: 'Game 1' }]),
    } as any)

    const wrapper = mount(Card)
    const vm = wrapper.vm as any

    await vm.$nextTick()
    await nextTick()

    expect(vm.gameMenu).toHaveLength(2)
    expect(vm.currentCardList).toEqual(mockGameList)
    expect(wrapper.find('.game-center').exists()).toBe(true)
  })

  it('search function should work correctly', async () => {
    const mockGameList = [{ id: 11, name: 'Game 1' }, { id: 12, name: 'Game 2' }]
    vi.mocked(useCardGameStore).mockReturnValue({
      fetchLobbyLink: vi.fn().mockResolvedValue(undefined),
      fetchCardData: vi.fn().mockResolvedValue(undefined),
      getAllGameMenu: vi.fn().mockReturnValue([{ id: 1, name: 'Top Game' }, { id: 84, name: 'All Game' }]),
      getGameListByMenuId: vi.fn().mockReturnValue(mockGameList),
      searchGame: vi.fn().mockReturnValue([{ id: 12, name: 'Game 2' }]),
    } as any)

    const wrapper = mount(Card)
    const vm = wrapper.vm as any

    await wrapper.vm.$nextTick()
  
    vm.inputSearchGame = 'Game 2'

    await wrapper.vm.$nextTick()

    expect(vm.currentCardList).toEqual([mockGameList[1]])

    vm.inputSearchGame = ''

    await wrapper.vm.$nextTick()

    expect(vm.currentCardList).toEqual(mockGameList)
  })

  it('should display maintenance dialog when API returns maintenance info', async () => {
    const maintainError = new Error('MAINTAIN')
    vi.mocked(useCardGameStore).mockReturnValue({
      fetchLobbyLink: vi.fn().mockResolvedValue(undefined),
      fetchCardData: vi.fn().mockRejectedValue(maintainError),
      getAllGameMenu: vi.fn().mockReturnValue(undefined),
      getGameListByMenuId: vi.fn().mockReturnValue(undefined),
      searchGame: vi.fn().mockReturnValue(undefined),
    } as any)

    const wrapper = mount(Card)

    await wrapper.vm.$nextTick()
    await nextTick()
    const vm = wrapper.vm as any

    expect(vm.dialogVisible).toBe(true)
    expect(vm.maintainInfo).toBe(maintainError.message)
  })

  it('should reset search state and update game list when switching top menu', async () => {
    const mockGameList = [{ id: 11, name: 'Game 1' }, { id: 12, name: 'Game 2' }]
    vi.mocked(useCardGameStore).mockReturnValue({
      fetchLobbyLink: vi.fn().mockResolvedValue(undefined),
      fetchCardData: vi.fn().mockResolvedValue(undefined),
      getAllGameMenu: vi.fn().mockReturnValue([{ id: 1, name: 'Top Game' }, { id: 84, name: 'All Game' }]),
      getGameListByMenuId: vi.fn().mockReturnValue(mockGameList),
      searchGame: vi.fn().mockReturnValue([{ id: 12, name: 'Game 2' }]),
    } as any)

    const wrapper = mount(Card)
    const vm = wrapper.vm as any

    vm.inputSearchGame = 'Game 2'

    await wrapper.vm.$nextTick()
    await wrapper.find('.top-game-menu').trigger('click')
    await wrapper.vm.$nextTick()

    expect(vm.showSearch).toBe(false)
    expect(vm.inputSearchGame).toBe('')
    expect(vm.currentCardList).toEqual(mockGameList)
  })

  it('should show error message on error', async () => {
    const errorMessage = 'Error fetching data'

    vi.mocked(useCardGameStore).mockReturnValue({
      fetchLobbyLink: vi.fn().mockResolvedValue(undefined),
      fetchCardData: vi.fn().mockRejectedValue(new Error(errorMessage))
    } as any)

    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const wrapper = mount(Card)
    await wrapper.vm.$nextTick()

    expect(ElMessage.error).toHaveBeenCalledWith(errorMessage)
    expect(console.error).toHaveBeenCalledWith(new Error(errorMessage))
  })
})