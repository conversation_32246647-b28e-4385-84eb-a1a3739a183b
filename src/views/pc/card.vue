<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useCardGameStore } from "@/stores/cardGame"
import type { IGameInfo, IGameMenu } from '@/types/game'
import { useAccountInfoStore } from '@/stores/account'
import TopGameMenu from '@/components/TopGameMenu.vue'
import SearchBox from '@/components/pc/SearchBox.vue'
import EnterCardLobby from '@/components/pc/EnterCardLobby.vue'
import { useImportTheme } from '@/composables/useImportTheme'
import MaintainDialog from '@/components/MaintainDialog.vue'
import { storeToRefs } from 'pinia'
import GameCenter from '@/components/pc/GameCenter.vue'

const { t } = useI18n()
const cardGameStore = useCardGameStore()
const accountInfoStore = useAccountInfoStore()
const { accountInfo } = storeToRefs(accountInfoStore)
const gameMenu = ref<IGameMenu[]>([])
const currentCardList = ref<IGameInfo[]>([])
const listMode = ref(false)
const showSearch = ref(false)
const inputSearchGame = ref('')
const topMenuId = ref<number>(84)
const isLoading = ref(false)
const { importStyle } = useImportTheme()
const dialogVisible = ref(false)
const maintainInfo = ref('')
const isMaintain = ref(false)
const hailId = ref(Number(localStorage.getItem('hallinfo_hallid')))

provide('nowPage','card')

const setTopMenu = () => {
  showSearch.value = false
  inputSearchGame.value = ''

  showGameList(topMenuId.value)
}

const showGameList = (menuItemID: number) => {
  const gameList = cardGameStore.getGameListByMenuId(menuItemID)
  currentCardList.value = gameList.filter((game): game is IGameInfo => game !== undefined)
}

const handleGameSearch = () => {
  topMenuId.value = 84

  if (inputSearchGame.value) {
    currentCardList.value = cardGameStore.searchGame(inputSearchGame.value)
  } else {
    showGameList(topMenuId.value)
  }
}

const fetchLobbyLink = async () => {
  try {
    await cardGameStore.fetchLobbyLink()
  } catch (error) {
    ElMessage.error((error as Error).message)
    console.error(error)
  }
}

watch(inputSearchGame, () => {
  handleGameSearch()
})

onMounted(async () => {
  try {
    isLoading.value = true
    await cardGameStore.fetchCardData(false)
    gameMenu.value = cardGameStore.getAllGameMenu()
    setTopMenu()
  } catch (error) {
    if ((error as Error).message.includes(t('MAINTAIN'))) {
      dialogVisible.value = true
      isMaintain.value = true
      maintainInfo.value = (error as Error).message.replace(
        /\\n/g,
        '<br/>'
      )
      return
    }
    ElMessage.error((error as Error).message)
    console.error(error)
  } finally {
    isLoading.value = false
  }
})

fetchLobbyLink()
importStyle('pc/card')
</script>

<template>
  <EnterCardLobby
    v-if="!isMaintain"
    :is-login="accountInfo.isLogin"
    :hall-id="hailId"
    :lobby-url="cardGameStore.lobbyUrl"
  />
  <div
    v-if="!isMaintain"
    v-loading="isLoading" 
    class="card-container"
  >
    <div class="menu-bar">
      <TopGameMenu
        v-model:top-menu-id="topMenuId"
        :game-menu="gameMenu"
        @click="setTopMenu"
      />
      <div class="search-bar">
        <SearchBox
          v-model:show="showSearch"
          v-model:data="inputSearchGame"
          class="search-box"
        />
      </div>
    </div>
    <div class="content">
      <GameCenter
        v-if="currentCardList.length > 0"
        :game-list="currentCardList"
        :list-mode="listMode"
      />
      <div v-else class="no-data">
        <span>{{ t('S_NO_DATA_CL')}}</span>
      </div>
    </div>
  </div>
  <!-- Maintain Dialog -->
  <MaintainDialog
    v-if="dialogVisible"
    v-model="dialogVisible"
    :maintain-info="maintainInfo"
  />
</template>

<style lang="scss" scoped>

.card-container {
  position: relative;
  width: 100%;
  min-height: 50vh;
  padding-bottom: 80px;
}

.menu-bar {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  text-align: center;
  border-top: 1px solid var(--main);
  border-bottom: 1px solid var(--main);
  background: var(--menu-bar-bg);
}

.search-bar {
  margin-left: auto;
  display: flex;
  align-items: center;

  .search-box {
    display: flex;
    align-items: center;
    width: 100%;
  }

  :deep(.i-icon) {
    height: 40px;
    margin: 0 auto;
    padding: 8px;
    border-left: 1px solid var(--main);
    cursor: pointer;
  }

  :deep(.search-input) {
    width: 0;
    &.active {
      width: 200px;
      margin: 3px 5px;
    }
  }
}

.no-data {
  position: relative;
  height: 45vh;
  color: var(--main);
  font-weight: bold;
  span {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
