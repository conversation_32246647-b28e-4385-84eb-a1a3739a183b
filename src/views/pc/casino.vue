<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useCasinoGameStore } from '@/stores/casinoGame'
import type { IGameInfo, IGameMenu } from '@/types/game'
import { useAccountInfoStore } from '@/stores/account'
import { storeToRefs } from 'pinia'
import ButtonListMode from '@/components/buttons/ListMode.vue'
import ButtonFavoriteMode from '@/components/buttons/FavoriteMode.vue'
import { useEjpStore } from '@/stores/ejpremote'
import bbSwitchBoard from '@/components/pc/bbSwitchBoard.vue'
import historyBoard from '@/components/pc/historyBoard.vue'
import TopGameMenu from '@/components/TopGameMenu.vue'
import SearchBox from '@/components/pc/SearchBox.vue'
import SubGameMenu from '@/components/SubGameMenu.vue'
import MaintainDialog from '@/components/MaintainDialog.vue'
import MenuBoard from '@/components/pc/MenuBoard.vue'
import GameCenter from '@/components/pc/GameCenter.vue'

provide('nowPage','casino')
const { t } = useI18n()
const casinoGameStore = useCasinoGameStore()
const ejpStore = useEjpStore()
const accountInfoStore = useAccountInfoStore()
const { jpList, isShowJackpot } = storeToRefs(ejpStore)
const { gameList, favoriteGameList } = storeToRefs(casinoGameStore)
const currentGameList = ref<IGameInfo[]>([])
const { accountInfo } = storeToRefs(accountInfoStore)
const inputSearchGame = ref('')
const showFavs = ref(false)
const listMode = ref(false)
const showSearch = ref(false)
const mainMenu = ref<IGameMenu[]>([])
const subMenu = ref<IGameMenu[]>([])
const showLower = ref(false)
const topMenuId = ref<number>(2)
const subMenuId = ref<number>(84)
const lowerMenuId = ref<number>(0)
const isLoading = ref(false)
const dialogVisible = ref(false)
const maintainInfo = ref('')

const setTopMenu = () => {
  showFavs.value = false
  showSearch.value = false
  inputSearchGame.value = ''

  if (topMenuId.value === 84) {
    subMenuId.value = topMenuId.value
  }

  showGameList(topMenuId.value)
}

const setSubMenu = () => {
  showFavs.value = false
  showSearch.value = false
  inputSearchGame.value = ''

  if (lowerMenuId.value > 0) {
    showGameList(lowerMenuId.value)
  } else {
    showGameList(subMenuId.value)
  }
}

const showGameList = (menuItemID: number) => {
  const gameList = casinoGameStore.getGameListByMenuId(menuItemID)
  currentGameList.value = gameList.filter((game): game is IGameInfo => game !== undefined)
}

const getFavDetail = async () => {
  if (!showFavs.value) {
    return
  }

  showLower.value = false
  showSearch.value = false
  topMenuId.value = 0
  subMenuId.value = 0
  inputSearchGame.value = ''

  if( favoriteGameList.value.length > 0) {
    currentGameList.value = favoriteGameList.value
  }
}

const handleGameSearch = () => {
  topMenuId.value = 84
  subMenuId.value = 84
  showFavs.value = false

  if (inputSearchGame.value) {
    currentGameList.value = casinoGameStore.searchGame(inputSearchGame.value)
  } else {
    showGameList(topMenuId.value)
  }
}

const showJpInfo = () => {
  // 彩金條 jpAmount / jpImg
  if( jpList.value.length > 0 && gameList.value.length > 0) {
    gameList.value.forEach(game => {
      const foundItem = jpList.value.find(jp => jp.gameType == game.gameId.toString())
      game.isShowJp = false
      
      if (foundItem) {
        game.jpAmount = foundItem.poolAmount || 0
        game.jpImg = foundItem.jpImg || ''
        game.isShowJp = true
      }
    })
  }
}

onMounted( async() => {
  try {
    isLoading.value = true
    await casinoGameStore.fetchCasinoData(false)
    mainMenu.value = casinoGameStore.getTopGameMenu()
    subMenu.value = casinoGameStore.getSubGameMenu()
    ejpStore.connectWebSocket()

    if(mainMenu.value.length > 0) {
      setTopMenu()
    }
  } catch (error) {
    if ((error as Error).message.includes(t('MAINTAIN'))) {
      dialogVisible.value = true
      maintainInfo.value = (error as Error).message.replace(
        /\\n/g,
        '<br/>'
      )
      return
    }
    ElMessage.error((error as Error).message)
    console.error(error)
  } finally {
    isLoading.value = false
  }
})

onBeforeUnmount(() => {
  ejpStore.disconnectWebSocket()
})

watch(favoriteGameList, (newValue) => {
  if (showFavs.value) {
    currentGameList.value = newValue
  }
})

watch(ejpStore.jpList, () => {
  showJpInfo()
})

watch(inputSearchGame, () => {
  handleGameSearch()
})
</script>

<template>
  <div class="bbgp-main">
    <div class="top-boards">
      <rankBoard v-if="isShowJackpot" />
      <MenuBoard>
        <bbSwitchBoard />
        <div class="menu-bar">
          <TopGameMenu
            v-model:top-menu-id="topMenuId"
            :game-menu="mainMenu"
            @click="setTopMenu"
          />
          <div class="search-bar">
            <ButtonFavoriteMode
              v-if="accountInfo.isLogin"
              v-model="showFavs"
              @click="getFavDetail"
            />
            <ButtonListMode
              v-model="listMode"
            />
            <SearchBox
              v-model:show="showSearch"
              v-model:data="inputSearchGame"
              class="search-box"
            />
          </div>
        </div>
        <template v-if="topMenuId == 84">
          <SubGameMenu
            v-model:sub-menu-id="subMenuId"
            v-model:lower-menu-id="lowerMenuId"
            v-model:show-lower="showLower"
            :sub-menu="subMenu"
            @click="setSubMenu"
            @mouseleave="showLower = false"
            class="sub"
          />
        </template>
      </MenuBoard>
    </div>
  </div>
  <div
    v-loading="isLoading"
    class="casino-container"
  >
    <div class="casino-content">
      <div class="content">
        <div v-if="showFavs && !accountInfo.isLogin">
          {{ t('S_PleaseLogin') }}
        </div>
        <GameCenter
          v-else-if="currentGameList.length > 0"
          :game-list="currentGameList"
          :favorite-game-list="favoriteGameList"
          :list-mode="listMode"
        />
        <div v-else class="no-data">
          <span>{{ t('S_NO_DATA_CL') }}</span>
        </div>
      </div>
    </div>
  </div>
  <historyBoard v-if="accountInfo.isLogin" />
  <!-- Maintain Dialog -->
  <MaintainDialog
    v-if="dialogVisible"
    v-model="dialogVisible"
    :maintain-info="maintainInfo"
  />
</template>

<style lang="scss" scoped>
.bbgp-main {
  position: relative;
  overflow: visible;
}

.casino-container {
  position: relative;
  width: 100%;
  min-height: 50vh;
  display: flex;
  justify-content: space-between;
  text-align: center;
}

.casino-content {
  width: 100%;
}

.menu-bar {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  text-align: center;
  border-top: 1px solid var(--main);
  border-bottom: 1px solid var(--main);
  background: var(--menu-bar-bg);
}

.search-bar {
  margin-left: auto;
  display: flex;
  align-items: center;

  .search-box {
    display: flex;
    align-items: center;
    width: 100%;
  }

  :deep(.i-icon) {
    height: 40px;
    margin: 0 auto;
    padding: 8px;
    border-left: 1px solid var(--main);
    cursor: pointer;
  }

  :deep(.search-input) {
    width: 0;
    &.active {
      width: 200px;
      margin: 3px 5px;
    }
  }
}

.no-data {
  position: relative;
  height: 45vh;
  color: var(--main);
  font-weight: bold;
  span {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
