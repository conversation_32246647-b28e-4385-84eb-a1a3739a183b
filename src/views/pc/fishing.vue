<script setup lang="ts">
import { useFishingGameStore } from '@/stores/fishingGame'
import { useBasicInfoStore } from "@/stores/basicInfo"
import { useAccountInfoStore } from '@/stores/account'
import { useI18n } from 'vue-i18n'
import type { IGameInfo } from '@/types/game'
import ButtonListMode from '@/components/buttons/ListMode.vue'
import bbSwitchBoard from '@/components/pc/bbSwitchBoard.vue'
import historyBoard from '@/components/pc/historyBoard.vue'
import SearchBox from '@/components/pc/SearchBox.vue'
import { storeToRefs } from 'pinia'
import { useRoute } from 'vue-router'
import { useConfigStore } from '@/stores/config'
import MenuBoard from '@/components/pc/MenuBoard.vue'
import GameCenter from '@/components/pc/GameCenter.vue'

const { t } = useI18n()
const route = useRoute()
const basicInfoStore = useBasicInfoStore()
const configStore = useConfigStore()
const fishingGameStore = useFishingGameStore()
const accountInfoStore = useAccountInfoStore()
const { siteInfo } = storeToRefs(basicInfoStore)
const { cdnUrl } = storeToRefs(configStore)
const { accountInfo } = storeToRefs(accountInfoStore)
const currentGameList = ref<IGameInfo[]>([])
const inputSearchGame = ref('')
const listMode = ref(false)
const showSearch = ref(false)
const isLoading = ref(false)

provide('nowPage','fishing')

const handleGameSearch = async () => {
  if (inputSearchGame.value) {
    currentGameList.value = fishingGameStore.searchGame(inputSearchGame.value)
  } else {
    currentGameList.value = fishingGameStore.getGameList()
  }
}

watch(inputSearchGame, () => {
  handleGameSearch()
})

onMounted(async () => {
  try {
    isLoading.value = true
    await fishingGameStore.fetchFishingData(false)
    currentGameList.value = fishingGameStore.getGameList()
  } catch (error) {
    ElMessage.error((error as Error).message)
    console.error(error)
  } finally {
    isLoading.value = false
  }
})

</script>

<template>
  <div class="bbgp-main">
    <div class="top-boards">
      <MenuBoard>
        <bbSwitchBoard />
        <div class="menu-bar">
          <div class="search-bar">
            <ButtonListMode
              v-model="listMode"
            />
            <SearchBox
              v-model:show="showSearch"
              v-model:data="inputSearchGame"
              class="search-box"
            />
          </div>
        </div>
      </MenuBoard>
    </div>
  </div>
  <div 
    v-loading="isLoading"
    class="fishing-container"
  >
    <div class="fishing-content">
        <!-- 遊戲介紹 -->
      <FishIntro
        v-if="route.query.to === 'intro'"
        :fish-game-list="currentGameList"
        :cdn-url="cdnUrl"
        :is-login="accountInfo.isLogin"
        :unix-time="siteInfo.unixTime"
      />
      <GameCenter
        v-else-if="currentGameList.length > 0"
        :game-list="currentGameList"
        :list-mode="listMode"
      />
      <div v-else class="no-data">
        <span>{{ t('S_NO_DATA_CL') }}</span>
      </div>
    </div>
  </div>
  <historyBoard v-if="accountInfo.isLogin"/>
</template>

<style lang="scss" scoped>
.bbgp-main {
  position: relative;
  overflow: visible;
}

.fishing-container {
  position: relative;
  width: 100%;
  min-height: 50vh;
  padding-bottom: 80px;
  display: flex;

  .no-data {
    padding: 20px;
  }
}

.fishing-content {
  width: 100%;
}

.menu-bar {
  width: 100%;
  height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid var(--main);
  border-bottom: 1px solid var(--main);
  background: var(--menu-bar-bg);
}

.search-bar {
  margin-left: auto;
  display: flex;
  align-items: center;

  .search-box {
    display: flex;
    align-items: center;
    width: 100%;
  }

  :deep(.i-icon) {
    height: 40px;
    margin: 0 auto;
    padding: 8px;
    border-left: 1px solid var(--main);
    cursor: pointer;
  }

  :deep(.search-input) {
    width: 0;
    &.active {
      width: 200px;
      margin: 3px 5px;
    }
  }
}

.no-data {
  position: relative;
  height: 45vh;
  color: var(--main);
  font-weight: bold;
  span {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

</style>
