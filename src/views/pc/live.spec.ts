import { beforeEach, describe, expect, it, vi } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'

import LiveContainer from './live.vue' // 根據實際路徑調整
import LiveMenu from '@/components/LiveMenu.vue'
import LiveRule from '@/components/LiveRule.vue'
import { createI18n } from 'vue-i18n'
import { getLiveContentList } from '@/api/game'
import { mount } from '@vue/test-utils'
import { useAccountInfoStore } from '@/stores/account'
import { ElMessage } from 'element-plus'
import type { ILiveMainSetting } from '@/types/live'

vi.mock('@/api/game', () => ({
  getLiveContentList: vi.fn(),
}))
vi.mock('@/stores/account', () => ({
  useAccountInfoStore: vi.fn()
}))

const i18n = createI18n({
  locale: 'en',
  messages: {
    'zh-tw': { S_GAME_RULE: '規則' },
    'zh-cn': { S_GAME_RULE: '规则' },
    'en': { S_GAME_RULE: 'Rules' },
  },
})

const mockData: ILiveMainSetting = {
  gameRuleList:  [{ id: 101, name: 'Rule 1' }],
  platformMenu: [{ id: 1, name: 'Game 1' }],
}

describe('LiveContainer.vue', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('valid_data', async () => {
    vi.mocked(getLiveContentList).mockResolvedValue(mockData)
    vi.mocked(useAccountInfoStore).mockReturnValue({
      accountInfo: ref({
        username: 'testUser',
        balance: 100
      })
    } as any)

    const wrapper = mount(LiveContainer, {
      global: {
        plugins: [i18n],
      },
    })

    await nextTick()
    await wrapper.vm.$nextTick()
 
    expect(wrapper.vm.gameMenuList).toEqual(mockData.platformMenu)
    expect(wrapper.vm.gameRuleList).toEqual(mockData.gameRuleList)
    expect(wrapper.vm.accountInfo).toEqual({
      username: 'testUser',
      balance: 100
    })
  })
  
  it('getLiveContentList_error', async () => {
    vi.mocked(getLiveContentList).mockRejectedValue(new Error('Error fetching data'))
    vi.mocked(useAccountInfoStore).mockReturnValue({
      accountInfo: ref({
        username: 'testUser',
        balance: 100
      })
    } as any)
    vi.spyOn(ElMessage, 'error').mockImplementation(vi.fn())
    const consoleMock = vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const wrapper = mount(LiveContainer, {
      global: {
        plugins: [i18n],
      },
    })

    await wrapper.vm.$nextTick()

    expect(wrapper.vm.gameMenuList).toEqual([])
    expect(wrapper.vm.gameRuleList).toEqual([])
    expect(ElMessage.error).toHaveBeenCalledWith('Error fetching data')
    expect(consoleMock).toHaveBeenCalledWith(new Error('Error fetching data'))
  })

  it('showRule_false', async () => {
    vi.mocked(getLiveContentList).mockResolvedValue(mockData)
    vi.mocked(useAccountInfoStore).mockReturnValue({
      accountInfo: ref({
        isLogin: true,
        username: 'testUser',
        balance: 100
      })
    } as any)

    const wrapper = mount(LiveContainer, {
      global: {
        plugins: [i18n],
      },
      props: {
        showRule: false
      }
    })

    await nextTick()
    await wrapper.vm.$nextTick()

    expect(wrapper.vm.accountInfo.username).toBe('testUser')
    expect(wrapper.findComponent(LiveMenu).exists()).toBe(true)
    expect(wrapper.findComponent(LiveRule).exists()).toBe(false)
  })

  it('showRule_true', async () => {
    vi.mocked(getLiveContentList).mockResolvedValue(mockData)
    vi.mocked(useAccountInfoStore).mockReturnValue({
      accountInfo: ref({
        username: 'testUser',
        balance: 100
      })
    } as any)

    const wrapper = mount(LiveContainer, {
      global: {
        plugins: [i18n],
      },
      props: {
        showRule: true
      }
    })

    await nextTick()
    await wrapper.vm.$nextTick()
    expect(wrapper.vm.accountInfo.username).toBe('testUser')
    
    await wrapper.find('.rule-btn').trigger('click')
    expect(wrapper.findComponent(LiveRule).exists()).toBe(true)
    expect(wrapper.findComponent(LiveMenu).exists()).toBe(false)
  })

  it('back_btn_click', async () => {
    vi.mocked(getLiveContentList).mockResolvedValue(mockData)
    vi.mocked(useAccountInfoStore).mockReturnValue({
      accountInfo: ref({
        username: 'testUser',
        balance: 100
      })
    } as any)

    const wrapper = mount(LiveContainer, {
      global: {
        plugins: [i18n],
      },
    })

    await nextTick()
    await wrapper.vm.$nextTick()
    expect(wrapper.vm.accountInfo.username).toBe('testUser')
    
    await wrapper.find('.rule-btn').trigger('click')
    await wrapper.find('.back-btn').trigger('click')

    expect(wrapper.findComponent(LiveMenu).exists()).toBe(true)
    expect(wrapper.findComponent(LiveRule).exists()).toBe(false)
  })
})
