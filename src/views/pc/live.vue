<script setup lang="ts">
import { useAccountInfoStore } from '@/stores/account'
import { getLiveContentList } from '@/api/game'
import LiveRule from '@/components/LiveRule.vue'
import LiveMenu from '@/components/LiveMenu.vue'
import type { ILiveGameRule, ILiveGameMenu } from '@/types/live'
import { storeToRefs } from 'pinia'
import { useCookies } from 'vue3-cookies'

const { cookies } = useCookies()
const accountInfoStore = useAccountInfoStore()
const { accountInfo } = storeToRefs(accountInfoStore)
const showRule = ref(false)
const gameMenuList = ref<ILiveGameMenu[]>([])
const gameRuleList = ref<ILiveGameRule[]>([])
const isLoading = ref(false)

defineExpose({
  gameMenuList,
  gameRuleList,
  accountInfo
})

onMounted(async () => {
  try {
    const params = {
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
      session_id: cookies.get('SESSION_ID'),
      lang: cookies.get('lang')
    }

    isLoading.value = true
    const res = await getLiveContentList(params)
    if (res) {
      gameMenuList.value = res.platformMenu
      gameRuleList.value = res.gameRuleList
    }
  } catch (error) {
    ElMessage.error((error as Error).message)
    console.error(error)
  } finally {
    isLoading.value = false
  }
})
</script>

<template>
  <div 
    v-loading="isLoading"  
    class="live-container"
  >
    <div class="live-content">
      <div v-if="showRule" class="back-btn" @click="showRule = false"></div>
      <div v-else class="rule-btn" @click="showRule = true"></div>
      <!-- 規則說明 -->
      <LiveRule v-if="showRule" :rule-list="gameRuleList" />
      <!-- 大廳Menu -->
      <LiveMenu v-else :menu-list="gameMenuList" :is-login="accountInfo.isLogin"/>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.live-container {
  position: relative;
  min-height: 50vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.live-content {
  position: relative;
  margin: 30px 0;
  background: url(/client/static/image/hall/live/live_bbin_bg_l.jpg) 100% 0
    no-repeat;
  background-size: cover;
  width: 100%;
  max-width: 55rem;
  height: 36rem;
}

.rule-btn {
  display: block;
  position: absolute;
  top: 6.5rem;
  right: 2.75rem;
  left: auto;
  width: 2.4375rem;
  height: 2.4375rem;
  background: url(/client/static/image/hall/live/btn_rule_open.png) top
    no-repeat;
  background-size: 100%;
  transition: background 0.3s ease;
  cursor: pointer;
  &:hover {
    background: url(/client/static/image/hall/live/btn_rule_open_over.png) top
      no-repeat;
    background-size: 100%;
  }
}

.back-btn {
  display: block;
  position: absolute;
  top: 6.5rem;
  right: 2.75rem;
  left: auto;
  width: 2.4375rem;
  height: 2.4375rem;
  background: url(/client/static/image/hall/live/btn_rule_back_over.png) top
    no-repeat;
  background-size: 100%;
  cursor: pointer;
}
</style>
