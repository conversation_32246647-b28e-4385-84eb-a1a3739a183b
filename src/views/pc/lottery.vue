<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useBasicInfoStore } from "@/stores/basicInfo"
import { useConfigStore } from '@/stores/config'
import { useAccountInfoStore } from '@/stores/account'
import { useLobbyLinkStore } from '@/stores/lobbyLink'
import  type { 
  ILotteryLeaderBoard,
  ILotteryLikeGuess,
  ILotteryGamePanel,
  ILotteryOfficial,
  ILotteryTradition,
  ILotteryTimer
} from '@/types/lottery'
import { getLotteryList } from '@/api/game'
import dayjs from 'dayjs'
import MaintainDialog from '@/components/MaintainDialog.vue'
import { storeToRefs } from 'pinia'
import { useCookies } from 'vue3-cookies'

const { t } = useI18n()
const basicInfoStore = useBasicInfoStore()
const configStore = useConfigStore()
const accountInfoStore = useAccountInfoStore()
const lobbyLinkStore = useLobbyLinkStore()
const { cookies } = useCookies()
const alreadyFetch = ref(false)
const leaderboardData = ref<ILotteryLeaderBoard[]>([])
const guessLikeList = ref<ILotteryLikeGuess[]>([])
const traditionList = ref<ILotteryTradition[]>([])
const gamePanelList = ref<ILotteryGamePanel[]>([])
const traditionLink = ref<string>("/balv/bv/pilot?lang=zh-cn&referer_url=%2Fpantheon%2Favengers%2Favengers%2Ffashion")
const officialOnData = ref<string>("1")
const officialData = ref<ILotteryOfficial[]>([])
const serverTime = ref(0)
const setServerTimer = ref()
const setFetchApiTimer = ref()
const tranditionTimer = ref<ILotteryTimer[]>([])
const tranditionTimerData = ref<ILotteryTimer>({
  time: '00:00:00',
  status: false
})
const gamePanelTimer = ref<ILotteryTimer>({
  time: '',
  status: true
})
const isLoading = ref(false)
const dialogVisible = ref(false)
const maintainInfo = ref('')
const isMaintain = ref(false)
const { siteInfo } = storeToRefs(basicInfoStore)
const { accountInfo } = storeToRefs(accountInfoStore)
const { cdnUrl } = storeToRefs(configStore)

// 官方版是否為顯示(true/false判斷)
const officialOn = computed(()=>{
  return officialOnData.value === '1'
})

// gameTime - 遊戲開盤或關盤時間
// servertime - 系統時間
// numtsatus - 目前遊戲倒數(1),下期開盤倒數(0)
const gameTimeFormat = (gameTime: number, servertime: number, numtsatus: number) => {
  const gapTime = Math.floor(gameTime - servertime)
  return {
    time: gapTime > 0 ? dayjs.duration(gapTime, "seconds").format('HH:mm:ss') :  '00:00:00',
    status: gapTime > 0 && numtsatus === 1
  }
}

/* 期數狀態邏輯 */
const  numlogic = (open_timestamp: number, close_timestamp: number, servertime: number) => {
  const timerData =
    open_timestamp > servertime
      ? gameTimeFormat(open_timestamp, servertime, 0) // 下一期開盤倒數
      : gameTimeFormat(close_timestamp, servertime, 1); // 遊戲關盤倒數
  return timerData;
}

/* 傳統版遊戲倒數計時器 */
const tranditionTimerCalc = (tranditioData: ILotteryTradition[], servertime: number) => {
  tranditionTimer.value = []
  for (let i = 0; i < tranditioData.length; i += 1) {
    const checknum =
      tranditioData[i].num === '' &&
      tranditioData[i].openTimestamp === 0 &&
      tranditioData[i].closeTimestamp === 0

    // 無期數且開盤,關盤時間為0 時，顯示oo:oo:oo
    if (checknum) {
      tranditionTimerData.value = {
        time: '00:00:00',
        status: false
      }
    } else {
        tranditionTimerData.value = numlogic(
          tranditioData[i].openTimestamp,
          tranditioData[i].closeTimestamp,
          servertime
        )
    }
    tranditionTimer.value.push(tranditionTimerData.value);
  }
}

const getLotteryData = async() => {
  try {
    const params = {
      session_id: cookies.get('SESSION_ID'),
      lang: cookies.get('lang'),
      hall_id: Number(localStorage.getItem('hallinfo_hallid')),
      filter: alreadyFetch.value ? 'game_panel,tradition,leaderboard' : ''
    }

    const res = await getLotteryList(params)

    if(res) {
      clearInterval(setServerTimer.value)

      const {
        serverTimestamp: resServerTime,
        officialOn,
        traditionEntrance,
        gamePanel,
        tradition,
        official,
        likeGuess,
        leaderboard
      } = res
      
      leaderboardData.value = leaderboard
      gamePanelList.value[0] = gamePanel
      traditionList.value = tradition

       // 首次進入頁面才取的資料
      if (!alreadyFetch.value) {
        officialData.value = official
        guessLikeList.value = likeGuess
        officialOnData.value = officialOn;
        traditionLink.value = traditionEntrance;
      }

      serverTime.value = resServerTime + 1 // 為了跟倒數器時間一樣
      setServerTimer.value = setInterval(() => {
        // 六合彩的倒數器
        gamePanelTimer.value = numlogic(
          gamePanelList.value[0].openTimestamp,
          gamePanelList.value[0].closeTimestamp,
          serverTime.value
        )
        // 傳統遊戲的倒數器
        tranditionTimerCalc(
            tradition,
            serverTime.value
        )
        serverTime.value += 1
      }, 1000)

      alreadyFetch.value = true;
    } 
  } catch (error) {
    if ((error as Error).message.includes(t('MAINTAIN'))) {
      dialogVisible.value = true
      isMaintain.value = true
      maintainInfo.value = (error as Error).message.replace(
        /\\n/g,
        '<br/>'
      )
      clearInterval(setFetchApiTimer.value)
      clearInterval(setServerTimer.value)
      return
    }
    ElMessage.error((error as Error).message)
    console.error(error)
  }
}

onBeforeUnmount(() => {
  clearInterval(setFetchApiTimer.value);
  clearInterval(setServerTimer.value);
})

onMounted( async() => {
  try {
    const sessionId = cookies.get('SESSION_ID')
    isLoading.value = true

    if (sessionId) {
      await lobbyLinkStore.fetchLotteryLobby({
        session_id: sessionId,
        lang: cookies.get('lamg'),
        hall_id: Number(localStorage.getItem('hallinfo_hallid')),
        is_mobile: false,
        domain_url: location.hostname
      })
    }
    
    await getLotteryData()
  } catch (error) {
      isMaintain.value = true
      ElMessage.error((error as Error).message)
      console.error(error)
    } finally {
      isLoading.value = false
    }
})

watch(() => accountInfo.value, () => {
  // 登入後每20秒取API
  if (accountInfo.value.isLogin) {
    clearInterval(setFetchApiTimer.value);
    setFetchApiTimer.value = setInterval(() => {
      getLotteryData()
    }, 20000);
  }
}, { immediate: true })

</script>

<template>
  <div 
    v-if="!isMaintain" 
    v-loading="isLoading"
    class="lottery-container"
  >
    <div class="lottery-title">{{t('gamekind.bblottery')}}</div>
    <!-- 猜你喜歡 -->
    <LotteryGuessLike
      :guess-like-list="guessLikeList"
      :cdn-url="cdnUrl"
      :is-login="accountInfo.isLogin"
      :unix-time="siteInfo.unixTime"
    />
    <!-- 英雄榜 or 試手氣 -->
    <LotteryHeroBoard
      :leaderboard-data="leaderboardData"
      :cdn-url="cdnUrl"
      :tradition-link="traditionLink"
      :is-login="accountInfo.isLogin"
      :unix-time="siteInfo.unixTime"
    />
    <div class="middle-content">
      <!-- Game Panel -->
      <LotteryGamePanel
        :game-panel-list="gamePanelList"
        :game-panel-timer="gamePanelTimer"
        :server-time="serverTime"
        :is-login="accountInfo.isLogin"
        :class="traditionList.length > 10 ? 'full' : ''"
      />
      <!-- Tradition -->
      <LotteryTraditionBoard
        :tradition-list="traditionList"
        :trandition-timer="tranditionTimer"
        :server-time="serverTime"
        :is-login="accountInfo.isLogin"
        :unix-time="siteInfo.unixTime"
      />
    </div>
    <!-- 官方版 -->
    <LotteryOfficialBoard
      v-if="officialOn"
      :is-login="accountInfo.isLogin"
      :official-data="officialData"
    />
  </div>
  <!-- Maintain Dialog -->
  <MaintainDialog
    v-if="dialogVisible"
    v-model="dialogVisible"
    :maintain-info="maintainInfo"
  />
</template>

<style lang="scss" scoped>
.lottery-container {
  position: relative;
  width: 1170px;
  display: flex;
  flex-direction: column;
  margin: 0 auto;
}

.lottery-title {
  background: #444;
  color: #eee;
  font-size: 1.2rem;
  padding: 10px 50px;
}

.middle-content {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row-reverse;
  background: #fff;
}
</style>
