import { flushPromises, mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'
import { createI18n } from 'vue-i18n'
import { createRouter, createWebHistory } from 'vue-router'
import Upup from '@/views/upup.vue'
import { upUp } from '@/api/upup'

vi.mock('@/api/upup', () => ({
  upUp: vi.fn()
}))

vi.mock('@/stores/hallInfo', () => ({
  useHallInfoStore: vi.fn(() => ({
    getHallInfo: vi.fn(),
    hallInfo: ref({}),
  }))
}))

vi.mock('@/utils/apiUrl', () => ({
  useApiUrl: vi.fn(() => ref('')),
}));

vi.mock('gsap', () => ({
  gsap: {
    to: vi.fn(), // Mock the 'to' method used by the component
  },
}));

vi.mock('@/composables/useImportTheme', () => ({
  useImportTheme: () => ({
    importTheme: vi.fn(),
    importStyle: vi.fn(),
  }),
}));

const i18n = createI18n({
  locale: 'en',
  messages: {
    en: {
      'S_UPUP_NOTICE': 'S_UPUP_NOTICE',
      'S_UPUP_CONTENT': 'S_UPUP_CONTENT',
      'expected': 'Estimated time for completion is ',
      'upup_finish': 'Upup finish'
    },
  },
})

const router = createRouter({
    history: createWebHistory(),
    routes: [
      { path: '/', name: 'home', component: { template: '<div>Home</div>' } },
      { path: '/index', name: 'index', component: { template: '<div>Index</div>' } }
    ]
  })

describe('Upup', () => {
  it('renders correctly', async () => {
    vi.mocked(upUp).mockResolvedValue({
      isMaintain: true,
      upupEndTime: '2025-04-07T23:00:00-04:00',
      customerServiceOnline: 'S_CUSTOMER_SERVICE_ONLINE',
      webUpup: 'S_WEB_UPUP',
      upupNotice: 'S_UPUP_NOTICE',
      upupContent: 'S_UPUP_CONTENT',
      hours: 12
    })

    const wrapper = mount(Upup, {
      global: {
        plugins: [i18n, router],
      },
    })

    await flushPromises()

    expect(wrapper.text()).toContain('S_UPUP_NOTICE')
    expect(wrapper.text()).toContain('S_UPUP_CONTENT')
    expect(wrapper.text()).toContain('2025-04-07T23:00:00-04:00')
  })
})
