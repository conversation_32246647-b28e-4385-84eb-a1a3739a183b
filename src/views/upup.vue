<script setup lang="ts">
import { gsap } from "gsap";
import { getSiteConfig } from '@/config/siteConfig'
import { useImportTheme } from '@/composables/useImportTheme'
import { upUp } from '@/api/upup'
import { IUpup } from '@/types/upup'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useHallInfoStore } from '@/stores/hallInfo'

const { importTheme, importStyle } = useImportTheme()
const { t } = useI18n()
const router = useRouter()
const route = useRoute()
const hallInfoStore = useHallInfoStore()
const upupData = ref<IUpup>({} as IUpup)
const isActive = ref<boolean[]>(Array(12).fill(false))
const isDone = ref<boolean[]>( Array(12).fill(false))
const clockNums = Array.from({ length: 12 }, (_, i) => i + 1)
let hours = 0
const isTest = route.query.test === 'true'
let animaTimeCounter = -1

const fetchUpup = async () => {
  try {
    const params = {
      hall_id: Number(localStorage.getItem('hallinfo_hallid'))
    }

    const res = await upUp(params)

    hours = res.hours
    const doneIndex = hours === 12 ? 0 : hours
    isActive.value.fill(true, 0, hours)
    isDone.value[doneIndex] = true

    if (res.isMaintain) {
      upupData.value = res
      document.title = res.webUpup
    } else {
      if(!isTest) router.push('/')
    }
  } catch (error: any) {
    console.log(error)
    ElMessage.error(error.message)
  }
}

const clockAnimation = () => {
  const imagePath = '/client/static/image/upup/'
 
    gsap.to('.clock-pointer', {
        delay: 0.17,
        duration: hours * 0.17,
        rotation: hours * 30,
        ease: 'none'
    });

    gsap.to('.active', {
        delay: 0.17,
        duration: 0.17,
        opacity: 0,
        backgroundImage: `url(${imagePath}clock_nums_over.png)`,
        ease: 'none',
        stagger: {
            each: 0.17,
            from: 'start'
        }
    });
    gsap.to('.active', {
        delay: 0.34,
        duration: 0.34,
        opacity: 1,
        backgroundImage: `url(${imagePath}clock_nums.png)`,
        ease: 'none',
        stagger: {
            each: 0.17,
            from: 'start'
        }
    })

    gsap.to('.done', {
        delay: (hours + 1) * 0.17,
        backgroundImage: `url(${imagePath}clock_nums_over.png)`,
        ease: 'none'
    })
}

const setTheme = () => {
  const siteConfig = getSiteConfig(localStorage.getItem('hallinfo_website') || 'default')
  const theme = siteConfig.theme
  document.documentElement.setAttribute('tpl-theme', theme)
  importTheme(theme)
  importStyle('pc/index')
}

onMounted(async () => {
  try {
    await setTheme()
    await hallInfoStore.getHallInfo()
    await fetchUpup()
    clockAnimation()
    animaTimeCounter = window.setInterval(() => {
      clockAnimation()
    }, 2100)
  } catch (error: any) {
    console.log(error)
    ElMessage.error(error.message)
  }
})

onBeforeUnmount(() => {
  clearInterval(animaTimeCounter)
})
</script>

<template>
  <div class="upup-container">
    <div class="upup-clock">
      <div
          v-for="(clockNum, index) in clockNums"
          :key="index"
          :class="[
              'clock-num',
              `clock-${clockNum}`,
              {
                done: isDone[index],
                active: isActive[index]
              }
          ]"
      />
      <div class="clock-pointer" />
      <div class="clock-tool" />
    </div>
    <div v-if="upupData.upupNotice" class="text">
      <h1>{{ t(upupData.upupNotice) }}</h1>
      <p>{{ t(upupData.upupContent) }}</p>
    </div>
    <br />
    <br />
    <div v-if="upupData.upupEndTime" class="upup-time">
      {{ t('expected') }}
      <h3>{{ upupData.upupEndTime }}</h3>
      {{ t('upup_finish') }}
    </div>
  </div>
</template>

<style lang="scss" scoped>

.upup-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgb(126, 132, 155);

  .text {
    padding: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;


    h1 {
      margin: auto 0;
      color: #fff;
      font-size: 1.5rem;
    }

    p {
      margin-top: 10px;
      color: #222;
      font-size: 1.2rem;
      font-weight: bold;
    }
  }

  .upup-clock {
    position: relative;
    margin: 0 auto;
    width: 400px;
    height: 400px;
  }

  .clock-num {
    position: absolute;
    background-image: url('/client/static/image/upup/clock_nums.png');
    z-index: 1;
    width: 62px;
    height: 62px;
  }

  .upup-clock .clock-1 {
    top: 0;
    left: 169px;
    background-position: -682px 0;
  }

  .upup-clock .clock-2 {
    top: 22px;
    left: 254px;
    background-position: 0 0;
  }

  .upup-clock .clock-3 {
    top: 84px;
    left: 316px;
    background-position: -62px 0;
  }

  .upup-clock .clock-4 {
    top: 169px;
    left: 338px;
    background-position: -124px 0;
  }

  .upup-clock .clock-5 {
    top: 254px;
    left: 316px;
    background-position: -186px 0;
  }

  .upup-clock .clock-6 {
    top: 316px;
    left: 254px;
    background-position: -248px 0;
  }

  .upup-clock .clock-7 {
    top: 338px;
    left: 169px;
    background-position: -310px 0;
  }

  .upup-clock .clock-8 {
    top: 316px;
    left: 84px;
    background-position: -372px 0;
  }

  .upup-clock .clock-9 {
    top: 254px;
    left: 22px;
    background-position: -434px 0;
  }

  .upup-clock .clock-10 {
    top: 169px;
    left: 0;
    background-position: -496px 0;
  }

  .upup-clock .clock-11 {
    top: 84px;
    left: 22px;
    background-position: -558px 0;
  }

  .upup-clock .clock-12 {
    top: 22px;
    left: 84px;
    background-position: -620px 0;
  }

  .upup-clock .clock-pointer {
    position: absolute;
    z-index: 1;
    left: 0;
    top: 0;
    background: url('/client/static/image/upup/clock_pointer.png') 0 0
        no-repeat;
    width: 400px;
    height: 400px;
  }

  .upup-clock .clock-tool {
    position: absolute;
    z-index: 2;
    left: 83px;
    top: 83px;
    background: url('/client/static/image/upup/clock_fix.png') 0 0 no-repeat;
    width: 235px;
    height: 235px;
  }

  .upup-time {
    margin-top: 30px;
    display: flex;
    align-items: center;

    h3 {
      margin: 5px 10px;
      color: #eed144;
      font-size: 1.1rem;
      font-weight: bold;
    }
  }

}

</style>
