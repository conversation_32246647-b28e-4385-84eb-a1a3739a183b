{"files": [], "include": ["env.d.ts", "*.d.ts", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "auto-imports.d.ts", "components.d.ts", ".eslintrc-auto-import.json"], "compilerOptions": {"target": "esnext", "module": "esnext", "allowJs": true, "strict": true, "moduleResolution": "node", "esModuleInterop": true, "sourceMap": true, "resolveJsonModule": true, "skipLibCheck": true, "lib": ["esnext", "dom"], "paths": {"@/*": ["./src/*"]}}, "references": [{"path": "tsconfig.node.json"}]}