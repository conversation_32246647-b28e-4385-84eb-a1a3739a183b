/// <reference types="vitest/config" />
import { URL, fileURLToPath } from 'node:url'

import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Icons from 'unplugin-icons/vite'
import IconsResolver from 'unplugin-icons/resolver'
import { createHtmlPlugin } from 'vite-plugin-html'
import { defineConfig } from 'vite'
import { proxySetting } from './proxy'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default () => {
  return defineConfig({
    base: '/',
    plugins: [
      vue(),
      AutoImport({
        imports: ['vue'], // 自動導入Vue相關函數，例如：Ref、Reactive、toRef...等
        resolvers: [
          ElementPlusResolver(),
          // 自动导入图标组件
          IconsResolver({
            prefix: 'Icon'
          })
        ],
        eslintrc: { enabled: true },
        dts: './auto-imports.d.ts'
      }),
      Components({
        directoryAsNamespace: true,
        resolvers: [
          ElementPlusResolver(),
          // 自动注册图标组件
          IconsResolver({
            enabledCollections: ['ep']
          })
        ],
        dirs: ['src/views', 'src/views2', 'src/components'],
        dts: 'components.d.ts'
      }),
      Icons({
        autoInstall: true
      }),
      createHtmlPlugin({
        inject: {
          data: {
            title: ' Game Lobby '
          }
        }
      })
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    server: {
      proxy: proxySetting
    },
    build: {
      outDir: 'dist',
      assetsDir: 'assets',
      sourcemap: false,
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true
        }
      },
      rollupOptions: {
        output: {
          manualChunks(id) {
            if (id.includes('node_modules')) {
              if(id.includes('element-plus')) return 'element-plus'
              if(id.includes('@vue')) return '@vue'
              if(id.includes('lodash-es')) return 'lodash-es'
              if(id.includes('vue3-spinners')) return 'vue3-spinners'
              if(id.includes('axios')) return 'axios'
              if(id.includes('@intify')) return '@intify'
            }
          }
        }
      }
    },
    test: {
      environment: 'jsdom',
      globals: true,
      server: {
        deps: {
          inline: ['element-plus'],
        },
      },
    }
  })
}
